import { cookies } from "next/headers";

export const fetchInitialUser = async () => {
  const cookieStore = cookies();
  const accessToken = (await cookieStore).get("access_token")?.value;

  if (!accessToken) {
    return null;
  }

  const response = await fetch(`https://backend.example.com/api/user`, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    cache: "no-store",
  });

  if (!response.ok) {
    return null;
  }

  return response.json();
};
