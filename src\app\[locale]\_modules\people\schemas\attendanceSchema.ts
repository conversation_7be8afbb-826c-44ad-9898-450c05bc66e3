// src/app/[locale]/_modules/people/schemas/attendanceSchema.ts
import { z } from "zod";
import { TFunction } from "@/types";
import { MAX_FILE_SIZE } from "../constants";
import { Attendance_Event_type } from "@/constants/enum";
// Define Activity Types
export const ATTENDANCE_ACTIVITY_TYPE = {
  REGULAR: "regular",
  OVERTIME: "overtime",
  SICK_LEAVE: "sick_leave",
  VACATION: "vacation",
  LUNCH_BREAK: "lunch_break",
  WORK_FROM_HOME: "work_from_home",
  MEETING: "meeting",
} as const;

export type AttendanceActivityType = keyof typeof ATTENDANCE_ACTIVITY_TYPE;

export const ATTENDANCE_LOCATION = {
  OFFICE: "office",
  REMOTE: "remote",
  SITE: "site",
  
} as const;

export type AttendanceLocation = keyof typeof ATTENDANCE_LOCATION;



const createDateValidators = () => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const ninetyDaysAgo = new Date();
  ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);
  ninetyDaysAgo.setHours(0, 0, 0, 0);

  return {
    validateDateFormat: (date: Date) => !isNaN(date.getTime()),
    validateStartDateNotTooOld: (date: Date) => date >= ninetyDaysAgo,
    validateEndDateNotInPast: (date: Date) => date >= today,
    validateStartBeforeEnd: (startDate: Date, endDate: Date) =>
      startDate <= endDate,
  };
};

export const createAttendanceSchema = (t: TFunction) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  return z
    .object({
      // إذا في profile يأتي عبر prop/URL وإلا يختار المستخدم
      employee_id: z.string().optional(),

      // حقل event_type إجباري حسب enum
      event_type: z.nativeEnum(Attendance_Event_type, {
        required_error: t("common.form.attendance.error.event-type-required"),
      }),

      // activity_type كما كان
      activity_type: z
        .string({
          required_error: t(
            "common.form.attendance.error.activity-type-required",
          ),
        })
        .refine(
          (val) => Object.values(ATTENDANCE_ACTIVITY_TYPE).includes(val as any),
          { message: t("common.form.attendance.error.invalid-activity-type") },
        ),
        location: z
        .string({ required_error: t("common.form.attendance.error.location-required") })
        .refine(
          (val) => Object.values(ATTENDANCE_LOCATION).includes(val as any),
          { message: t("common.form.attendance.error.invalid-location") }
        ),

      // timestamp يُمرر مثلاً ISO string أو Date، ويجعل التحقق يشمل الوقت
    timestamp: z.preprocess(
  (val) => {
    if (typeof val === "string" && /^\d+$/.test(val)) {
      return new Date(Number(val) * 1000);
    }
    if (typeof val === "number") {
      return new Date(val * 1000);
    }
    // ISO datetime string
    if (typeof val === "string") {
      const dt = new Date(val);
      if (!isNaN(dt.getTime())) return dt;
    }
    // already a Date or leave for Zod to handle
    return val;
  },
  z
    .date()
    .refine((d) => !isNaN(d.getTime()), {
      message: t("common.form.attendance.error.invalid-date"),
    })
),


      // الملاحظة اختياري
      note: z
        .string()
        .optional()
        .refine((v) => !v || v.trim().length >= 3, {
          message: t("common.form.attendance.error.note-min"),
        })
        .refine((v) => !v || v.trim().length <= 500, {
          message: t("common.form.attendance.error.note-max"),
        }),

      // مرفقات اختياري
      documents: z
        .array(
          z.instanceof(File).refine((f) => f.size < MAX_FILE_SIZE, {
            message: t("common.form.attachment.error.file.maxSize"),
          }),
        )
        .optional()
        .refine(
          (files) => {
            if (!files || files.length === 0) return true;
            return files.reduce((sum, f) => sum + f.size, 0) <= MAX_FILE_SIZE;
          },
          {
            message: t("common.form.attachments.error.totalMaxSize"),
          },
        ),
    })
    .strict(); // تمنع حقول غير معرفة
};

export type CreateAttendanceSchemaType = z.infer<
  ReturnType<typeof createAttendanceSchema>
>;
