import { Suspense } from "react";
import CardWrapper from "../_modules/cm/_components/cards/card-wrapper";
import {
  CardsSkeleton,
  ChartSkeleton,
  SkeletonTable,
} from "../../../components/skeletons";
import dynamic from "next/dynamic";
import { getData } from "./TempData/appointment";
import MarketState from "../_modules/cm/_components/panels/wrappers/marked-state-wrapper";
import NewNotes from "../_modules/cm/_components/panels/wrappers/new-state-wrapper";
const CasesChart = dynamic(
  () => import("../_modules/cm/_components/appintments/chart/cases-chart"),
  { loading: () => <ChartSkeleton /> },
);

const SharedCasesChart = dynamic(
  () =>
    import("../_modules/cm/_components/appintments/chart/shared-cases-chart"),
  { loading: () => <ChartSkeleton /> },
);

const GroupCasesChart = dynamic(
  () =>
    import("../_modules/cm/_components/appintments/chart/group-shared-chart"),
  { loading: () => <ChartSkeleton /> },
);

const Panels = dynamic(
  () =>
    import("../_modules/cm/_components/panels/wrappers/state-panel-wrapper"),
  { loading: () => <CardsSkeleton hasDetail={false} /> },
);

const BeneficiariesAppointmentsTable = dynamic(
  () =>
    import(
      "../_modules/cm/_components/appintments/table/beneficiaries-appointments-table"
    ),
  { loading: () => <SkeletonTable rowCount={3} /> },
);

const CMSystem = async ({
  searchParams,
}: {
  searchParams: Promise<{ page: string; limit: string }>;
}) => {
  const appointmentsResponse = await getData();
  // Destructure the page and limit values from searchParams (with default values).
  const { page = "1", limit = "3" } = await searchParams;

  // Convert the string parameters to numbers.
  const currentPage = parseInt(page, 10);
  const pageLimit = parseInt(limit, 10);

  // Calculate start and end indices based on the current page and limit.
  const startIndex = (currentPage - 1) * pageLimit;
  const endIndex = startIndex + pageLimit;

  // Filter the data for the current page.
  const filteredData = appointmentsResponse.data.slice(startIndex, endIndex);

  // Prepare the filtered response with data and totalCount.
  const filteredAppointmentsResponse = {
    data: filteredData,
    totalCount: appointmentsResponse.totalCount,
  };
  return (
    <>
      <div className="h-full order-2 xl:order-1 shrink">
        <div className="grid gap-5 md:gap-6 sm:grid-cols-2 lg:grid-cols-3">
          <Suspense fallback={<CardsSkeleton hasDetail={false} />}>
            <CardWrapper />
          </Suspense>
        </div>
        <div className="my-5 flex gap-5">
          <CasesChart />
          <SharedCasesChart />
          <GroupCasesChart />
        </div>
        <div className="flex gap-5 mb-8">
          <Panels />
          <MarketState />
          <NewNotes />
        </div>
        <div className="pt-6 sm:pt-0">
          <BeneficiariesAppointmentsTable
            showPagination={false}
            data={filteredAppointmentsResponse}
            searchParams={{ page, limit }}
          />
        </div>
      </div>
    </>
  );
};

export default CMSystem;
