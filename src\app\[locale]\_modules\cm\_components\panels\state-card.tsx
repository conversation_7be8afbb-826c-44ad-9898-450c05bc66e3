import React, { <PERSON> } from "react";
import Image from "next/image";
import { PiUserCircleLight } from "react-icons/pi";

interface Note {
  id: string;
  userName: string;
  timestamp: string;
  content: string;
  userRole?: string;
  adminName?: string;
  userImage?: string;
}

interface StatusComponentProps {
  variant: "new" | "marked";
  notes: Note[];
  title: string;
  showAll?: string;
}

const StatusCards: FC<StatusComponentProps> = ({
  variant,
  notes,
  title,
  showAll = "عرض الكل",
}) => {
  return (
    <div
      className="bg-white rounded-2xl p-6  w-full border border-neutral-100 "
      dir="rtl"
    >
      <div className="flex justify-between items-center mb-6 font-alex">
        <h2 className="text-[16px] font-bold text-main-2">{title}</h2>
        <button className="text-gray-600 hover:text-neutral-800 transition-colors text-[8px]">
          {showAll}
        </button>
      </div>

      <div className="space-y-4 max-h-[418px] overflow-auto">
        {notes.map((note) => (
          <div
            key={note.id}
            className="border border-gray-100 rounded-lg p-4 hover:shadow-sm transition-shadow"
          >
            <div className="flex justify-between items-center mb-2">
              <div className="space-y-1">
                <div className="flex flex-col items-center gap-2">
                  <h3 className="font-medium text-xs text-gray-900">
                    {note.userName}
                  </h3>
                  <span className="text-gray-500 text-[10px] font-normal">
                    #{note.id}
                  </span>
                </div>
              </div>
              <span className="text-[8px] text-gray-500">{note.timestamp}</span>
            </div>

            <p className="text-neutral-600 text-[10px] font-normal leading-relaxed mb-3">
              {note.content}
            </p>

            {variant === "new" && note.userRole && (
              <div className="flex items-center justify-between pt-1">
                <div className="flex items-center gap-2">
                  <div className="relative h-6 w-6">
                    {note.userImage ? (
                      <Image
                        src={note?.userImage}
                        alt={note.userName}
                        fill
                        className="rounded-full object-cover"
                      />
                    ) : (
                      <PiUserCircleLight className="w-full h-full" />
                    )}
                  </div>
                  <div className="flex flex-col">
                    <span className="text-neutral-800 text-xs mb-[2px] font-medium">
                      {note.adminName}
                    </span>
                    <span className="text-neutral-500 text-[8px]">
                      {note.userRole}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default StatusCards;
