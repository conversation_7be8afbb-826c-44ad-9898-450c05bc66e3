import React from "react";
import { UseFormReturn } from "react-hook-form";
import FormFieldRenderer from "@/components/auth/fields/form-field-renderer";
import { getNotesField } from "@/hooks/form-fields/people-forms";
import { TFunction } from "@/types";
import { SalaryPackageSchemaType } from "@/app/[locale]/_modules/people/schemas/salaryPackageSchema";

type NotesSectionProps = {
  form: UseFormReturn<SalaryPackageSchemaType>;
  isPending: boolean;
  t: TFunction;
  mode?: "create" | "update";
};

export const NotesSection: React.FC<NotesSectionProps> = ({
  form,
  isPending,
  t,
  mode,
}) => {
  return (
    <div className="mt-6">
      <FormFieldRenderer
        fieldConfig={getNotesField(t, mode)}
        form={form}
        isPending={isPending}
      />
    </div>
  );
};
