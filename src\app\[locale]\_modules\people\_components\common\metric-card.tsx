"use client";

import { UpArrow } from "../../../../../../../public/images/icons";

export type MetricCardProps = {
  title: string;
  value: number | string;
  percentageChange?: number;
  comparisonText?: string;
  icon?: React.ReactNode;
  className?: string;
  titleStyle?: string;
  valuePrefix?: string;
  classesPrefix?: string;
  valueSuffix?: string;
  classesSuffix?: string;
};

export const MetricCard = ({
  title,
  value,
  percentageChange,
  comparisonText,
  icon,
  titleStyle,
  className = "",
  valuePrefix = "",
  valueSuffix = "",
  classesSuffix = "",
  classesPrefix = "",
}: MetricCardProps) => {
  const isPositive = percentageChange && percentageChange > 0;
  const showComparison = percentageChange !== undefined && comparisonText;

  return (
    <div
      className={`bg-white rounded-[20px] p-5 border border-gray-200 flex flex-col gap-4 min-h-[148px] ${className}`}
    >
      <h3 className={`text-gray-500 text-base font-medium ${titleStyle}`}>
        {title}
      </h3>
      <div className="text-2xl font-semibold">
        <span className={classesPrefix}> {valuePrefix}</span>
        {value}
        <span className={classesSuffix}>{valueSuffix}</span>
      </div>
      {showComparison && (
        <div className="flex items-center text-sm gap-1">
          <span
            className={`flex items-center font-bold tracking-[0.5%] text-sm ${
              isPositive ? "text-secondary" : "text-error"
            }`}
          >
            {icon || (
              <UpArrow
                className={`w-4 h-4 ${
                  !isPositive ? "rotate-180 stroke-error" : ""
                }`}
              />
            )}
            {percentageChange}%
          </span>
          <span className="text-gray-400 font-normal text-sm">
            {comparisonText}
          </span>
        </div>
      )}
    </div>
  );
};
