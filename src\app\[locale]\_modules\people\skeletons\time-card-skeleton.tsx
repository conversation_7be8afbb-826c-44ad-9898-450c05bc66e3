import { Skeleton } from "@/components/ui/skeleton";
import { Separator } from "@/components/ui/separator";

export const TimeCardSkeleton = () => {
  return (
    <div className="bg-white rounded-2xl p-6 border border-gray-200 flex flex-col gap-4 min-h-[155px]">
      {/* Date skeleton */}
      <Skeleton className="h-7 w-24 bg-gray-200 rounded-md" />

      {/* Desktop and tablet view skeleton */}
      <div className="hidden md:flex items-center">
        {/* Check-in column */}
        <div className="flex flex-col justify-start">
          <Skeleton className="h-4 w-16 bg-gray-200 rounded-md mb-6" />
          <Skeleton className="h-6 w-16 bg-gray-200 rounded-md text-center" />
        </div>

        <Separator
          orientation="vertical"
          className="h-12 w-0.5 mx-6 my-[5.5px] bg-gray-100"
        />

        {/* Middle time periods column */}
        <div className="w-full flex flex-col justify-evenly items-center gap-2">
          {/* Time markers */}
          <div className="w-full flex items-center justify-between">
            <Skeleton className="h-4 w-12 bg-gray-200 rounded-md" />
            <Skeleton className="h-4 w-12 bg-gray-200 rounded-md" />
            <Skeleton className="h-4 w-12 bg-gray-200 rounded-md" />
          </div>

          {/* Time period bar */}
          <div className="w-full relative h-10">
            <Skeleton className="h-10 w-full bg-gray-200 rounded-lg" />
          </div>
        </div>

        <Separator
          orientation="vertical"
          className="h-12 w-0.5 mx-6 my-[5.5px] bg-gray-100"
        />

        {/* Check-out column */}
        <div className="flex flex-col justify-start">
          <Skeleton className="h-4 w-16 bg-gray-200 rounded-md mb-6" />
          <Skeleton className="h-6 w-16 bg-gray-200 rounded-md text-center" />
        </div>

        <Separator
          orientation="vertical"
          className="h-12 w-0.5 mx-6 my-[5.5px] bg-gray-100"
        />

        {/* Total hours column */}
        <div className="flex flex-col justify-start">
          <Skeleton className="h-4 w-16 bg-gray-200 rounded-md mb-6" />
          <Skeleton className="h-6 w-16 bg-gray-200 rounded-md" />
        </div>
      </div>

      {/* Mobile view skeleton */}
      <div className="flex flex-col md:hidden">
        {/* Check-in section */}
        <div className="flex justify-between items-center border-b pb-3 mb-3">
          <div>
            <Skeleton className="h-4 w-24 bg-gray-200 rounded-md mb-1" />
            <Skeleton className="h-6 w-16 bg-gray-200 rounded-md" />
          </div>
          <Skeleton className="h-6 w-12 bg-gray-200 rounded-md" />
        </div>

        {/* Time periods for mobile */}
        <div className="space-y-2 mb-3">
          {/* First period */}
          <div className="flex items-start justify-between">
            <Skeleton className="h-4 w-16 bg-gray-200 rounded-md me-2" />
            <div className="flex-grow">
              <Skeleton className="h-10 w-full bg-gray-200 rounded-lg" />
            </div>
          </div>

          {/* Second period */}
          <div className="flex items-start justify-between">
            <Skeleton className="h-4 w-16 bg-gray-200 rounded-md me-2" />
            <div className="flex-grow">
              <Skeleton className="h-10 w-full bg-gray-200 rounded-lg" />
            </div>
          </div>
        </div>

        {/* Check-out section */}
        <div className="flex justify-between items-center border-b pb-3 mb-3">
          <div>
            <Skeleton className="h-4 w-24 bg-gray-200 rounded-md mb-1" />
            <Skeleton className="h-6 w-16 bg-gray-200 rounded-md" />
          </div>
          <Skeleton className="h-6 w-12 bg-gray-200 rounded-md" />
        </div>

        {/* Work duration section */}
        <div className="flex justify-between items-center">
          <Skeleton className="h-4 w-24 bg-gray-200 rounded-md" />
          <Skeleton className="h-6 w-16 bg-gray-200 rounded-md" />
        </div>
      </div>
    </div>
  );
};
