"use client";

import * as React from "react";
import clsx from "clsx";

interface Note {
  id: number;
  subStepId: number;
  count: number;
}

interface SubStep {
  id: number;
  title: string;
  status: "active" | "pending" | "completed";
}

interface Step {
  id: number;
  title: string;
  status: "active" | "pending" | "completed";
  label?: string;
  subSteps: SubStep[];
  include: {
    notes: Note[];
  };
}

interface Props {
  data: Step[];
}

export default function StepTimeline({ data }: Props) {
  // By default, only step 1 is expanded
  const [expandedSteps, setExpandedSteps] = React.useState<
    Record<number, boolean>
  >(
    data.reduce(
      (acc, step) => {
        acc[step.id] = step.id === 1;
        return acc;
      },
      {} as Record<number, boolean>,
    ),
  );

  const toggleStep = (stepId: number) => {
    // Close all steps first
    const newExpandedState = Object.keys(expandedSteps).reduce(
      (acc, key) => {
        acc[parseInt(key)] = false;
        return acc;
      },
      {} as Record<number, boolean>,
    );

    // Then toggle the clicked step
    newExpandedState[stepId] = !expandedSteps[stepId];

    setExpandedSteps(newExpandedState);
  };

  return (
    <div className="relative rounded-lg p-4 h-full">
      {/* Timeline container */}
      <div className="relative ">
        {data.map((step, index) => {
          const isExpanded = expandedSteps[step.id];
          const isLast = index === data.length - 1;

          return (
            <div key={step.id} className={clsx("relative", !isLast && "pb-6")}>
              {/* Vertical line between circles */}
              {!isLast && (
                <div
                  className={clsx(
                    "absolute start-2.5 top-7 h-[calc(100%-30px)] transition-colors duration-300 w-[2px] rounded-full z-0",
                    isExpanded ? "bg-main-2" : "bg-[#D4E5CD]",
                  )}
                />
              )}

              {/* Step header section */}
              <div
                className="flex items-center cursor-pointer mb-6 relative z-10"
                onClick={() => toggleStep(step.id)}
              >
                {/* Step number circle */}
                <div
                  className={clsx(
                    "absolute start-0 w-6 h-6 rounded-full flex items-center justify-center transition-colors duration-300",
                    isExpanded ? "bg-primary" : "bg-[#D4E5CD]",
                  )}
                >
                  <span
                    className={clsx(
                      "font-medium text-sm transition-colors duration-300",
                      isExpanded ? "text-main-2" : "text-[#86B177]",
                    )}
                  >
                    {step.id}
                  </span>
                </div>

                <div className="flex justify-between items-center w-full">
                  <h3
                    className={clsx(
                      "ps-10 font-medium text-base transition-colors duration-300",
                      isExpanded ? "text-main-2" : "text-[#2d3c4d]",
                    )}
                  >
                    {step.title}
                  </h3>

                  {/* Optional label */}
                  {step.id === 2 && (
                    <span className="ms-4 bg-green-50 text-green-700 text-xs rounded-full px-3 py-1">
                      قيد انتظار الموافقة
                    </span>
                  )}
                </div>
              </div>
              {/* Substeps */}
              <div
                className={clsx(
                  "ps-10 space-y-4 overflow-hidden transition-all duration-300 ease-in-out",
                  isExpanded ? "max-h-96 opacity-100" : "max-h-0 opacity-0",
                )}
              >
                {step.subSteps.map((subStep) => {
                  const noteObj = step.include.notes.find(
                    (note) => note.subStepId === subStep.id,
                  );

                  return (
                    <div
                      key={subStep.id}
                      className="flex justify-between items-center"
                    >
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center">
                          <div
                            className={clsx(
                              "w-[22px] h-[22px] rounded-full border-2 me-4 transition-colors duration-200",
                              subStep.status === "active"
                                ? "border-green-500"
                                : "border-gray-300",
                            )}
                          />
                          <span
                            className={clsx(
                              "transition-colors duration-200 font-normal text-sm",
                              subStep.status === "active"
                                ? "text-[#2d3c4d]"
                                : "text-gray-700",
                            )}
                          >
                            {subStep.title}
                          </span>
                        </div>

                        {noteObj && (
                          <span className="text-xs text-gray-500">
                            {noteObj.count} ملاحظات
                          </span>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
