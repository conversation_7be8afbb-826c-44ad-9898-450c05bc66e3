"use client";

import { useTranslations } from "next-intl";
import { TFunction } from "@/types";
import { usePathname, useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import { useLocale } from "next-intl";
import { LANGUAGES } from "@/constants/enum";
import { Button } from "@/components/ui/button";

type DeviceDetailsTabsProps = {
  deviceId: string;
};

export default function DeviceDetailsTabs({
  deviceId,
}: DeviceDetailsTabsProps) {
  const t = useTranslations() as TFunction;
  const pathname = usePathname();
  const router = useRouter();
  const locale = useLocale();
  const isAr = locale === LANGUAGES.ARABIC;

  const tabs = [
    {
      key: "users",
      label: t("people.devices-page.device-details.tabs.users"),
      path: `/${locale}/people/devices/${deviceId}`,
    },
    {
      key: "commands",
      label: t("people.devices-page.device-details.tabs.commands"),
      path: `/${locale}/people/devices/${deviceId}/commands`,
    },
  ];

  const handleTabClick = (path: string) => {
    router.push(path);
  };

  return (
    <div className="border-b border-gray-200">
      <nav className="-mb-px flex space-x-8" aria-label="Tabs">
        {tabs.map((tab) => {
          const isActive = pathname === tab.path;

          return (
            <Button
              variant={"ghost"}
              key={tab.key}
              onClick={() => handleTabClick(tab.path)}
              className={cn(
                "whitespace-nowrap py-2 px-1 border-b-2 font-medium text-base transition-colors rounded-none",
                isActive
                  ? "border-secondary text-secondary font-bold text-base"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300",
                isAr && "ml-8 mr-0",
              )}
              aria-current={isActive ? "page" : undefined}
            >
              {tab.label}
            </Button>
          );
        })}
      </nav>
    </div>
  );
}
