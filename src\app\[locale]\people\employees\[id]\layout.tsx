"use client";

import React, { Suspense } from "react";
import { useEmployeeDetails } from "../../../_modules/people/hooks/employees/useEmployeeDetails";
import { useLocale } from "next-intl";
import { useParams } from "next/navigation";
import { Locale } from "@/i18n/routing";
import { EmployeeProfileLayout } from "../../../_modules/people/_components/employees/profile";
import { Skeleton } from "@/components/ui/skeleton";

export default function EmployeeLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const params = useParams();

  const employeeId = params.id as string;
  const locale: Locale = useLocale() as Locale;
  const { employee, employeeData, userRoles, isLoading, error, mutate } =
    useEmployeeDetails(employeeId);

  return (
    <Suspense
      fallback={
        <div className="p-6">
          <Skeleton className="h-[400px] w-full bg-gray-200 flex-1 flex flex-col" />
        </div>
      }
    >
      <EmployeeProfileLayout
        employeeId={employeeId}
        locale={locale}
        employee={employee}
        employeeData={employeeData}
        isLoading={isLoading}
        userRoles={userRoles}
        error={error ? error.message : null}
        mutateEmployee={mutate}
      >
        <div className="flex flex-col flex-1">{children}</div>
      </EmployeeProfileLayout>
    </Suspense>
  );
}
