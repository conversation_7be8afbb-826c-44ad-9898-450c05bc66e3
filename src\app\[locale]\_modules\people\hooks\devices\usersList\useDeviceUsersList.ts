import useSWR from "swr";
import { TDeviceUsersResponse } from "../../../type/devices/usersList";
import { fetcher } from "@/services/fetcher";

export function useDeviceUsersList(
  deviceId: string,
  page: number = 1,
  limit: number = 25,
  filters: string = "",
) {
  let apiUrl = deviceId
    ? `/api/attendance/devices/${deviceId}/users?include=employee&page=${page}&limit=${limit}`
    : null;

  if (apiUrl && filters) {
    apiUrl += `&${filters}`;
  }

  const { data, error, isLoading, mutate } = useSWR<TDeviceUsersResponse>(
    apiUrl,
    fetcher,
  );

  return {
    data: data?.data || [],
    employeeData: data?.included || [], // Include the employee data
    totalCount: data?.meta?.pagination?.count ?? 0, // Updated to use 'count' instead of 'total_count'
    pagination: data?.meta?.pagination,
    device: data?.meta?.device,
    error,
    isLoading,
    mutate,
  };
}
