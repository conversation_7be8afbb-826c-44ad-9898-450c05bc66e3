import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Plus } from "lucide-react";
import { useTranslations } from "next-intl";
import { TFunction } from "@/types";
import { Trash } from "../../../../../../../../../public/images/icons";

type CommandParameter = {
  id: string;
  key: string;
  value: string;
};

type DeviceCommandParametersProps = {
  parameters: CommandParameter[];
  onAddParameter: () => void;
  onRemoveParameter: (id: string) => void;
  onUpdateParameter: (
    id: string,
    field: "key" | "value",
    value: string,
  ) => void;
};

export default function DeviceCommandParameters({
  parameters,
  onAddParameter,
  onRemoveParameter,
  onUpdateParameter,
}: DeviceCommandParametersProps) {
  const t = useTranslations() as TFunction;

  return (
    <div className="space-y-4">
      <h4 className="text-base font-medium text-start">
        {t("people.devices-page.device-commands.form.parameters.title")}
      </h4>

      {parameters.length > 0 && (
        <div className="space-y-4">
          {parameters.map((parameter, idx) => (
            <div
              key={parameter.id}
              className="rounded-lg p-4 border border-gray-200 bg-gray-50"
            >
              <div className="flex justify-between items-center mb-4">
                <h5 className="text-sm font-medium text-gray-900 text-start">
                  {t(
                    "people.devices-page.device-commands.form.parameters.item-title",
                  )}
                  {` (${idx + 1})`}
                </h5>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="p-2 h-8 w-8 group hover:bg-red-50"
                  onClick={() => onRemoveParameter(parameter.id)}
                >
                  <Trash className="!w-6 !h-6 text-error group-hover:text-red-600" />
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="block text-sm font-medium text-gray-600 mb-2 text-start">
                    {t(
                      "people.devices-page.device-commands.form.parameters.key.label",
                    )}
                  </Label>
                  <Input
                    className="h-12 text-start"
                    value={parameter.key}
                    placeholder={t(
                      "people.devices-page.device-commands.form.parameters.key.placeholder",
                    )}
                    onChange={(e) =>
                      onUpdateParameter(parameter.id, "key", e.target.value)
                    }
                  />
                </div>
                <div>
                  <Label className="block text-sm font-medium text-gray-600 mb-2 text-start">
                    {t(
                      "people.devices-page.device-commands.form.parameters.value.label",
                    )}
                  </Label>
                  <Input
                    className="h-12 text-start"
                    value={parameter.value}
                    placeholder={t(
                      "people.devices-page.device-commands.form.parameters.value.placeholder",
                    )}
                    onChange={(e) =>
                      onUpdateParameter(parameter.id, "value", e.target.value)
                    }
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add Parameter Button */}
      <div className="border-none rounded-lg bg-gray-50">
        <Button
          type="button"
          variant="ghost"
          className="w-fit h-8 text-sm lleading-5 font-medium border-none border-gray-300 rounded-lg bg-white hover:bg-gray-50 flex items-center justify-start gap-2 text-gray-600 hover:text-gray-800 p-0"
          onClick={onAddParameter}
        >
          <Plus className="w-4 h-4" />
          {t("people.devices-page.device-commands.form.parameters.add-button")}
        </Button>
      </div>
    </div>
  );
}
