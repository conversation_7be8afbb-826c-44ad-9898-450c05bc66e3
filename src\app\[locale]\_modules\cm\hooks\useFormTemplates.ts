import useSWR from "swr";
import { fetcher } from "@/services/fetcher";
import { FormTemplatesResponse } from "@/types/cm";

type UseFormTemplatesParams = {
  sort?: string;
};

export const useFormTemplates = ({ sort }: UseFormTemplatesParams = {}) => {
  let apiUrl = "/api/cm/form-templates";
  const params = new URLSearchParams();

  if (sort) {
    params.append("sort", sort);
  }

  if (params.toString()) {
    apiUrl += `?${params.toString()}`;
  }

  const { data, error, isLoading, mutate } = useSWR<FormTemplatesResponse>(
    apiUrl,
    fetcher,
  );
  console.log("🚀 ~ useFormTemplates ~ data:", data);

  return {
    templates: data?.data || [],
    meta: data?.meta,
    isLoading,
    error,
    mutate,
  };
};
