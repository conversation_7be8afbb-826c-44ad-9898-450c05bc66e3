"use client";

import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import ResponsiveDialog from "@/components/responsive-dialog";
import { useState } from "react";
import { Textarea } from "@/components/ui/textarea";
import { useTranslations } from "use-intl";
import { Label } from "@/components/ui/label";
import Image from "next/image";

type TEmployeeRequestApprovalProps = {
  showApprovalModal: boolean;
  setShowApprovalModal: React.Dispatch<React.SetStateAction<boolean>>;
  onAddNote: (note: string) => void;
};
const EmployeeRequestApprovalDialog = ({
  showApprovalModal,
  setShowApprovalModal,
  onAddNote,
}: TEmployeeRequestApprovalProps) => {
  return (
    <ResponsiveDialog
      open={showApprovalModal}
      onOpenChange={setShowApprovalModal}
      closeBtnStyle="top-[22px] border-none rtl:start-6 ltr:start-6 text-gray-500"
      header={<RequestDetailsHeader />}
    >
      <RequestDetailsContent
        setShowApprovalModal={setShowApprovalModal}
        onAddNote={onAddNote}
      />
    </ResponsiveDialog>
  );
};

const RequestDetailsHeader = () => {
  return (
    <div className="text-start">
      <div className="px-6 py-[22px]"></div>
    </div>
  );
};

const RequestDetailsContent = ({
  onAddNote,
  setShowApprovalModal,
}: {
  onAddNote: (note: string) => void;
  setShowApprovalModal: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const t = useTranslations();
  const [note, setNote] = useState("");
  const handleSaveNote = () => {
    if (!note.trim()) {
      setNote("");
      setShowApprovalModal(false);
      return;
    }
    setNote("");
    onAddNote(note);
  };
  return (
    <div className={"text-start"}>
      <div className="font-semibold flex flex-col gap-2 items-start justify-center mb-6">
        <div>
          <Image
            src="/images/icons/15-Checked.svg"
            alt="Approval request mark"
            width={72}
            height={72}
            className="object-contain mt-7 mb-4"
          />
        </div>
        <DialogTitle className="!m-0  text-[20px] leading-[30px]font-semibold tracking-[0.5%]">
          {t("people.leaves-requests-page.employee-approval-dialog.title")}
        </DialogTitle>
        <DialogDescription className="text-base font-normal">
          {t(
            "people.leaves-requests-page.employee-approval-dialog.description",
          )}
        </DialogDescription>
      </div>
      <div className="mb-10">
        <Label className="font-medium inline-block mb-1 text-sm leading-[18px] text-gray-500">
          <span>
            {t("people.leaves-requests-page.dialog-common.note-label")}
          </span>
          <span className="text-gray-400">
            (
            {t(
              "people.leaves-requests-page.employee-approval-dialog.note-hint",
            )}
            )
          </span>
        </Label>
        <Textarea
          value={note}
          onChange={(e) => setNote(e.target.value)}
          placeholder={t(
            "people.leaves-requests-page.dialog-common.note-placeholder",
          )}
          className="w-full rounded-[10px] text-right min-h-[138px] max-h-[138px] placeholder:text-gray-400 placeholder:text-base placeholder:leading-5 placeholder:font-normal"
        />
      </div>
      <div className="w-full rounded-none sm:rounded-[18px] bg-white flex flex-row items-center justify-between gap-4 sm:gap-6 rounded-t-none">
        <>
          <Button
            onClick={() => handleSaveNote()}
            className="w-full h-12 text-white font-semibold"
          >
            {t("common.buttonText.ok")}
          </Button>
        </>
      </div>
    </div>
  );
};

export default EmployeeRequestApprovalDialog;
