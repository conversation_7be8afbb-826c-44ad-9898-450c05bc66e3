import { NextRequest, NextResponse } from "next/server";
import { EmployeeAttendanceStats } from "@/app/[locale]/_modules/people/type/employee";

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: "Missing employee ID" },
        { status: 400 },
      );
    }

    // For now, we'll return mock data
    const attendanceStats: EmployeeAttendanceStats = {
      vacationDays: {
        value: 12,
        percentageChange: 12,
      },
      lateArrival: {
        value: 2,
        percentageChange: -12,
      },
      lateDeparture: {
        value: 10,
        percentageChange: 12,
      },
      absence: {
        value: 1,
        percentageChange: 12,
      },
    };

    return NextResponse.json({
      data: {
        id: id,
        type: "employee_attendance",
        attributes: attendanceStats,
      },
      meta: {
        // Any additional metadata can go here
      },
    });
  } catch (error) {
    console.error("Error in GET employee attendance route:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
