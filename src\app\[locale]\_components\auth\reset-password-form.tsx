"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { PAGES } from "@/enums";
import useFormFields from "@/hooks/useFormFields";
import { onSubmitResetPassword } from "@/server/actions/auth";
import { ResetPasswordSchema, ResetPasswordSchemaType } from "@/schemas/auth";
import { ActionState, TFunction, TinputField } from "@/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader } from "lucide-react";
import React, {
  startTransition,
  useActionState,
  useEffect,
  useRef,
} from "react";
import { useForm } from "react-hook-form";
import { useLocale, useTranslations } from "next-intl";
import FormFieldRenderer from "@/components/auth/fields/form-field-renderer";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import ErrorMessage from "@/components/errorMessage";
import { Locale } from "@/i18n/routing";

const ResetPassForm = () => {
  const t = useTranslations() as TFunction;
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const locale: Locale = useLocale() as Locale;
  const router = useRouter();
  const { getFormFields } = useFormFields({ formType: PAGES.RESETPASS, t });
  const formRef = useRef<HTMLFormElement | null>(null);
  const initialState: ActionState<null> = {
    error: "",
    success: "",
    issues: [],
  };

  const [state, submitAction, isPending] = useActionState(
    onSubmitResetPassword,
    initialState,
  );

  const form = useForm<ResetPasswordSchemaType>({
    resolver: zodResolver(ResetPasswordSchema(t)),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
    mode: "all",
  });
  const isSubmitSuccessful = form.formState.isSubmitSuccessful;
  useEffect(() => {
    if (isSubmitSuccessful) {
      form.reset();
    }
  }, [form, form.reset, isSubmitSuccessful]);

  useEffect(() => {
    if (state.success) {
      const params = new URLSearchParams(searchParams);
      params.set("status", "success");
      router.replace(`${pathname}?${params}`);
    }
  }, [state, router, pathname, searchParams]);

  // Show success message if password reset is successful

  const isStatusSuccess = searchParams.get("status") === "success";
  if (isStatusSuccess) {
    return (
      <Button asChild className="h-12">
        <Link href={`/${locale}/auth/login`} className="w-full">
          {t("auth.resetPass.buttons.login")}
        </Link>
      </Button>
    );
  }

  return (
    <Form {...form}>
      <form
        ref={formRef}
        action={submitAction}
        onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit(() => {
            startTransition(async () => {
              if (formRef.current) {
                await submitAction(new FormData(formRef.current!));
              }
            });
          })(e);
        }}
        className="space-y-3 mt-10"
      >
        {(getFormFields() as TinputField<ResetPasswordSchemaType>[])?.map(
          (fieldConfig) => {
            return (
              <FormFieldRenderer
                key={fieldConfig.name}
                fieldConfig={fieldConfig}
                form={form}
                isPending={isPending}
              />
            );
          },
        )}
        {/* handle form errors */}
        {state?.error && (!state?.issues || state.issues.length === 0) && (
          <ErrorMessage message={state.error} />
        )}
        {Array.isArray(state?.issues) && state?.issues?.length > 0 && (
          <ErrorMessage message={state.issues[0]} />
        )}
        <Button
          disabled={isPending}
          type="submit"
          className="w-full h-12 max-h-12 !mt-10"
        >
          {!isPending ? (
            t("auth.forgotPass.buttons.resetPass")
          ) : (
            <Loader className="animate-spin text-white" />
          )}
        </Button>
      </form>
    </Form>
  );
};

export default ResetPassForm;
