"use server";

import { ActionState } from "@/types";
import { peopleService } from "@/services/api/people";
import { revalidatePath } from "next/cache";
import { handleError } from "@/lib/utils";
import { TCommandExecutionResponse } from "../type/devices/usersList";

export type ExecuteDeviceCommandData = {
  deviceId: string;
  command: string;
  parameters: Array<{
    id: string;
    key: string;
    value: string;
  }>;
};

export async function executeDeviceCommand(
  prevState: ActionState<TCommandExecutionResponse>,
  formData: FormData,
): Promise<ActionState<TCommandExecutionResponse>> {
  try {
    // Extract data from FormData
    const deviceId = formData.get("deviceId") as string;
    const command = formData.get("command") as string;
    const parametersJson = formData.get("parameters") as string;

    if (!deviceId || !command) {
      return {
        success: "",
        error: "Device ID and command are required",
        issues: [],
        data: null,
      };
    }

    const parameters = parametersJson ? JSON.parse(parametersJson) : [];

    const queryParams = new URLSearchParams();
    queryParams.append("command[id]", command);

    parameters.forEach((param: { key: string; value: string }) => {
      if (param.value.trim()) {
        // Only add non-empty parameters
        queryParams.append(`command[parameters][${param.key}]`, param.value);
      }
    });

    // Execute the command via the people service
    const result = await peopleService.executeDeviceCommand(
      deviceId,
      queryParams,
    );

    revalidatePath(`/people/devices/${deviceId}`);

    return {
      success: "Device command executed successfully",
      error: "",
      issues: [],
      data: result,
    };
  } catch (error) {
    return handleError(error, "Failed to execute device command");
  }
}
