// Approval Action type
export type ApprovalAction = {
  id: number;
  user_id: string;
  action: "approve" | "reject";
  comment: string | null;
  created_at: string;
};

// A step in the approval process
export type ApprovalStep = {
  id: number;
  name: string;
  sequence: number;
  approval_type: string;
  complete: boolean;
  rejected: boolean;
  actions: ApprovalAction[];
};

// Used only in `current_step`, which includes approver_ids
export type CurrentApprovalStep = Omit<
  ApprovalStep,
  "complete" | "rejected" | "actions"
> & {
  approver_ids: string[];
};

// The entity under review
export type Approvable = {
  id: number;
  type: string;
};

// The user requesting the approval
export type Requestor = {
  id: number;
  name: string;
};

// Core attributes of an approval request
export type ApprovalRequestAttributes = {
  workflow_id: string;
  workflow_name: string;
  status: "pending" | "approved" | "rejected";
  created_at: string;
  updated_at: string;
  approvable: Approvable;
  requestor: Requestor;
  current_step: CurrentApprovalStep | null;
  steps: ApprovalStep[];
};

// Full API approval request item
export type ApprovalRequestData = {
  id: string;
  type: "approval_request";
  attributes: ApprovalRequestAttributes;
};

// API response structure for listing approval requests
export type ApprovalRequestResponse = {
  data: ApprovalRequestData[];
  meta: {
    pagination: {
      count: number;
      page: number;
      limit: number;
      from: number;
      to: number;
    };
  };
};

// Response type for approval actions (approve, reject, withdraw)
export type ApprovalActionResponse = {
  data?: {
    id: string;
    status?: string;
    updatedAt?: string;
    approvalRequest?: ApprovalRequestData;
    [key: string]: string | number | boolean | ApprovalRequestData | undefined;
  };
  error?: Error;
};
