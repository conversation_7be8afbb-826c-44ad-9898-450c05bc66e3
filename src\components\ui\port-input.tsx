"use client";

import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { Plus, Minus } from "lucide-react";
import { Button } from "./button";
import { Input } from "./input";

type PortInputProps = {
  value?: string;
  onChange?: (value: string) => void;
  onBlur?: () => void;
  disabled?: boolean;
  className?: string;
  placeholder?: string;
  min?: number;
  max?: number;
};

export const PortInput: React.FC<PortInputProps> = ({
  value = "",
  onChange,
  onBlur,
  disabled = false,
  className,
  placeholder = "4370",
  min = 1,
  max = 65535,
}) => {
  const [portValue, setPortValue] = useState<string>(value);

  useEffect(() => {
    setPortValue(value);
  }, [value]);

  const handleInputChange = (inputValue: string) => {
    // Only allow numbers
    const numericValue = inputValue.replace(/[^0-9]/g, "");

    // Validate range
    if (numericValue !== "") {
      const numValue = parseInt(numericValue);
      if (numValue < min || numValue > max) {
        return;
      }
    }

    setPortValue(numericValue);
    onChange?.(numericValue);
  };

  const handleIncrement = () => {
    const currentValue = parseInt(portValue) || 0;
    const newValue = Math.min(currentValue + 1, max);
    const newValueStr = newValue.toString();
    setPortValue(newValueStr);
    onChange?.(newValueStr);
  };

  const handleDecrement = () => {
    const currentValue = parseInt(portValue) || 0;
    const newValue = Math.max(currentValue - 1, min);
    const newValueStr = newValue.toString();
    setPortValue(newValueStr);
    onChange?.(newValueStr);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "ArrowUp") {
      e.preventDefault();
      handleIncrement();
    } else if (e.key === "ArrowDown") {
      e.preventDefault();
      handleDecrement();
    }
  };

  return (
    <div className={cn("flex items-center gap-2", className)}>
      {/* Decrement Button */}
      <Button
        variant={"ghost"}
        type="button"
        onClick={handleDecrement}
        disabled={disabled || parseInt(portValue) <= min}
        className={cn(
          "w-12 h-12 flex items-center justify-center",
          "border border-gray-300 rounded-lg",
          "hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500",
          "disabled:bg-gray-100 disabled:cursor-not-allowed disabled:text-gray-400",
          "transition-colors !shrink-0",
        )}
      >
        <Minus className="w-5 h-5" />
      </Button>

      {/* Port Input */}
      <Input
        type="text"
        value={portValue}
        onChange={(e) => handleInputChange(e.target.value)}
        onKeyDown={handleKeyDown}
        onBlur={onBlur}
        disabled={disabled}
        className={cn(
          "flex-1 w-full h-12 px-4 text-center border border-gray-300 rounded-lg",
          "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
          "disabled:bg-gray-100 disabled:cursor-not-allowed",
          "text-lg font-medium md:min-w-[120px]",
        )}
        placeholder={placeholder}
        maxLength={5}
      />

      {/* Increment Button */}
      <Button
        variant={"ghost"}
        type="button"
        onClick={handleIncrement}
        disabled={disabled || parseInt(portValue) >= max}
        className={cn(
          "w-12 h-12 flex items-center justify-center",
          "border border-gray-300 rounded-lg",
          "hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500",
          "disabled:bg-gray-100 disabled:cursor-not-allowed disabled:text-gray-400",
          "transition-colors shrink-0",
        )}
      >
        <Plus className="w-5 h-5" />
      </Button>
    </div>
  );
};
