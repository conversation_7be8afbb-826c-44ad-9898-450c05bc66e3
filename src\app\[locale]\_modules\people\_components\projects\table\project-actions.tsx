"use client";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { TFunction } from "@/types";
import { useLocale, useTranslations } from "next-intl";
import { useState } from "react";
import { Edit, Trash2, MoreHorizontal } from "lucide-react";
import { TProject } from "@/types/core/project";
import { LANGUAGES } from "@/constants/enum";
import { usePermission } from "@/contexts/PermissionContext";
import { PermissionEnum } from "@/enums/Permission";
import { Row } from "@tanstack/react-table";

type ProjectActionsProps<TData> = {
  row: Row<TData>;
  onEditProject?: (project: TProject) => void;
  onDeleteProject?: (projectId: string) => void;
  isUpdating?: boolean;
  isDeleting?: boolean;
};

const ProjectActions = <TData extends TProject>({
  row,
  onEditProject,
  onDeleteProject,
  isUpdating,
  isDeleting,
}: ProjectActionsProps<TData>) => {
  const t = useTranslations() as TFunction;
  const locale = useLocale();
  const isAr = locale === LANGUAGES.ARABIC;
  const { hasPermission } = usePermission();
  const project = row.original;

  const [isOpen, setIsOpen] = useState(false);

  // Check permissions
  const canUpdateProject = hasPermission(PermissionEnum.UPDATE_PROJECT);
  const canDeleteProject = hasPermission(PermissionEnum.DESTROY_PROJECT);

  // Calculate available actions
  const hasEditAction = canUpdateProject && onEditProject;
  const hasDeleteAction = canDeleteProject && onDeleteProject;
  const hasActions = hasEditAction || hasDeleteAction;

  const handleEdit = () => {
    setIsOpen(false);
    if (onEditProject) {
      onEditProject(project);
    }
  };

  const handleDelete = () => {
    setIsOpen(false);
    if (onDeleteProject) {
      onDeleteProject(project.id);
    }
  };

  return (
    <>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="h-8 w-8 p-0 focus-visible:ring-0 focus-visible:ring-offset-0"
            disabled={isUpdating || isDeleting || !hasActions}
          >
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align={isAr ? "start" : "end"}>
          {hasEditAction && (
            <DropdownMenuItem
              onClick={handleEdit}
              className="rtl:justify-end cursor-pointer"
            >
              {t("projects.page.table.actions.edit")}
              <Edit className="mr-2 h-4 w-4" />
            </DropdownMenuItem>
          )}
          {hasDeleteAction && (
            <DropdownMenuItem
              onClick={handleDelete}
              className="text-error hover:text-destructive rtl:justify-end cursor-pointer"
            >
              {t("projects.page.table.actions.delete")}
              <Trash2 className="mr-2 h-4 w-4" />
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
};

export default ProjectActions;
