// -------------- leave request --------------
export enum MAIN_CATEGORY {
  LEAVE = "leave",
  OVERTIME = "overtime",
}

export enum LEAVE_TYPE {
  ANNUAL = "annual",
  SICK = "sick",
  MARRIAGE = "marriage",
  MATERNITY = "maternity",
  PATERNITY = "paternity",
  UNPAID = "unpaid",
}

export enum LEAVE_DURATION {
  FULL_DAY = "full_day",
  HALF_DAY_MORNING = "half_day_morning",
  HALF_DAY_AFTERNOON = "half_day_afternoon",
}

export enum PeriodType {
  Work = "work",
  Break = "break",
  Late = "late",
  EarlyDeparture = "early_departure",
  EarlyArrival = "early_arrival",
}

export enum ActivityType {
  "regular" = 0,
  "break" = 1,
  "lunch" = 2,
  "meeting" = 3,
  "business_trip" = 4,
  "work_from_home" = 5,
  "remote" = 6,
  "training" = 7,
  "personal_errand" = 8,
}

export const activityColorMap: Record<
  string,
  { background: string; border: string; translationKey?: string }
> = {
  [PeriodType.EarlyArrival]: {
    background: "#E6FFFA",
    border: "#81E6D9",
    translationKey:
      "people.employees-page.profile.attendance.timeCard.periodKeys.earlyArrival",
  },
  [PeriodType.Break]: {
    background: "#FFFFF0",
    border: "#ECC94B",
    translationKey:
      "people.employees-page.profile.attendance.timeCard.periodKeys.break",
  },
  [PeriodType.Late]: {
    background: "#FEF2F2",
    border: "#EF4444",
    translationKey:
      "people.employees-page.profile.attendance.timeCard.periodKeys.late",
  },
  [ActivityType.regular]: {
    background: "#ECFDF5",
    border: "#0F766E",
    translationKey:
      "people.employees-page.profile.attendance.timeCard.periodKeys.regular",
  },
  [ActivityType.break]: {
    background: "#FFFFF0",
    border: "#ECC94B",
    translationKey:
      "people.employees-page.profile.attendance.timeCard.periodKeys.break",
  },
  [ActivityType.lunch]: {
    background: "#FFF7ED",
    border: "#FB923C",
    translationKey:
      "people.employees-page.profile.attendance.timeCard.periodKeys.lunch",
  },
  [ActivityType.meeting]: {
    background: "#EDF2F7",
    border: "#A0AEC0",
    translationKey:
      "people.employees-page.profile.attendance.timeCard.periodKeys.meeting",
  },
  [ActivityType.business_trip]: {
    background: "#FFFAF0",
    border: "#D69E2E",
    translationKey:
      "people.employees-page.profile.attendance.timeCard.periodKeys.business_trip",
  },
  [ActivityType.work_from_home]: {
    background: "#F0FFF4",
    border: "#48BB78",
    translationKey:
      "people.employees-page.profile.attendance.timeCard.periodKeys.work_from_home",
  },
  [ActivityType.remote]: {
    background: "#EBF8FF",
    border: "#63B3ED",
    translationKey:
      "people.employees-page.profile.attendance.timeCard.periodKeys.remote",
  },
  [ActivityType.training]: {
    background: "#FFF5F0",
    border: "#ED8936",
    translationKey:
      "people.employees-page.profile.attendance.timeCard.periodKeys.training",
  },
  [ActivityType.personal_errand]: {
    background: "#FFF5F5",
    border: "#F56565",
    translationKey:
      "people.employees-page.profile.attendance.timeCard.periodKeys.personal_errand",
  },
  default: {
    background: "#E5E7EB",
    border: "transparent",
    translationKey:
      "people.employees-page.profile.attendance.timeCard.periodKeys.default",
  },
};
