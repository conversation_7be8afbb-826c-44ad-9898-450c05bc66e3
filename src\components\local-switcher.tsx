"use client";

import { useState, useTransition } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Locale, usePathname, useRouter } from "@/i18n/routing";
import { Directions, LANGUAGES } from "@/constants/enum";
import { useLocale } from "next-intl";
import Image from "next/image";

const languages = [
  { code: "en", label: "English" },
  { code: "ar", label: "العربية", dir: "rtl" },
] as { code: Locale; label: string; dir?: Directions }[];

export function LanguageSwitcher() {
  const router = useRouter();
  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const [isPending, startTransition] = useTransition();
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();

  const switchLanguage = (locale: Locale) => {
    setIsOpen(false);
    startTransition(() => {
      router.replace({ pathname }, { locale });
    });
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="text-center flex font-medium bg-white text-[#444] flex-row-reverse group md:p-0 focus-visible:ring-0"
          disabled={isPending}
        >
          <div className="rounded-full border border-neutral-100 md:p-2.5 w-[98px] h-10 flex items-center justify-center text-center">
            {isAr ? (
              <span className="flex items-center gap-1">
                <Image
                  src={"/images/icons/arabic-icon.svg"}
                  alt="arabic-language-icon"
                  width={15}
                  height={15}
                />
                العربية
              </span>
            ) : (
              <span>English </span>
            )}
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align={isAr ? "start" : "end"}
        className="w-32 z-[9999]"
      >
        {languages.map((language) => (
          <DropdownMenuItem
            key={language.code}
            onClick={() => switchLanguage(language.code)}
            className="text-sm cursor-pointer hover:text-ring hover:!bg-background-v2"
            dir={language.dir}
          >
            {language.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
