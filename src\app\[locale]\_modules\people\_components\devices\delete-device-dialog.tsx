"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import ResponsiveDialog from "@/components/responsive-dialog";
import { useTranslations } from "next-intl";
import { TFunction } from "@/types";
import { TDevice } from "../../type/devices/device";
import { useDeviceMutations } from "../../hooks/devices/useDeviceMutations";
import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useSearchParams } from "next/navigation";
import { FilledTrash } from "../../../../../../../public/images/icons";

type DeleteDeviceDialogProps = {
  device: TDevice | null;
  isOpen: boolean;
  onClose: () => void;
  onConfirmDelete?: () => Promise<void>;
};

export default function DeleteDeviceDialog({
  device,
  isOpen,
  onClose,
  onConfirmDelete,
}: DeleteDeviceDialogProps) {
  const t = useTranslations() as TFunction;
  const searchParams = useSearchParams();

  // Get the same page and limit parameters as the table
  const page = searchParams.get("page") || "1";
  const limit = searchParams.get("limit") || "5";

  const { deleteDevice, isDeleting } = useDeviceMutations({
    page,
    limit,
    onSuccess: onClose, // Close dialog on success
  });

  if (!device) return null;

  const handleConfirm = async () => {
    if (onConfirmDelete) {
      // Use custom delete handler (for single device page)
      await onConfirmDelete();
    } else {
      // Use default delete handler (for devices list)
      const success = await deleteDevice(device.id);
      if (success) {
        onClose();
      }
    }
  };

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={onClose}
      className="max-w-md text-center"
      closeBtnStyle="start-6 top-4 border-none"
      header={
        <>
          <div className="mx-auto mt-10 w-20 h-20 bg-red-50 rounded-full flex items-center justify-center">
            <div className="w-14 h-14 bg-red-100 rounded-full flex items-center justify-center">
              <FilledTrash className="scale-x-90 text-red-600" />
            </div>
          </div>

          {/* Title */}
          <DialogTitle className="text-xl font-semibold text-gray-900 mb-2">
            {t("people.devices-page.delete-dialog.title")}
          </DialogTitle>

          {/* Description */}
          <DialogDescription className="text-gray-600 text-base font-normal leading-relaxed">
            {t("people.devices-page.delete-dialog.description")}
          </DialogDescription>
        </>
      }
    >
      {/* Device Name Input Field (Read-only) */}
      <div className="mb-6 text-right">
        <Label className="block text-sm font-medium text-gray-700 mb-2">
          {t("people.devices-page.delete-dialog.device-name-label")}
        </Label>
        <Input
          type="text"
          value={device.attributes.name}
          readOnly
          className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-700 text-right"
          placeholder={t(
            "people.devices-page.delete-dialog.device-name-placeholder",
          )}
        />
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3">
        <Button
          variant="ghost"
          onClick={handleConfirm}
          disabled={isDeleting}
          className="bg-red-50 text-error flex-1 h-12"
        >
          {isDeleting
            ? t("people.devices-page.delete-dialog.deleting")
            : t("people.devices-page.delete-dialog.delete")}
        </Button>
        <Button
          variant="outline"
          onClick={onClose}
          disabled={isDeleting}
          className="bg-gray-100 flex-1 h-12"
        >
          {t("people.devices-page.delete-dialog.cancel")}
        </Button>
      </div>
    </ResponsiveDialog>
  );
}
