import { NextRequest, NextResponse } from "next/server";
import { cmAPI } from "@/services/api/cm";
import { createErrorResponse } from "@/utils/api-error-extractor";

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: "Case ID is required" },
        { status: 400 },
      );
    }

    const response = await cmAPI.getCase(id);
    return NextResponse.json(response);
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET Single Case route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
