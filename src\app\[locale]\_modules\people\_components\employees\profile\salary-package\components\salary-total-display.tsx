import React from "react";
import FormattedCurrency from "@/components/formatted-currency";
import { TFunction } from "@/types";

type SalaryTotalDisplayProps = {
  totalAmount: number;
  t: TFunction;
};

export const SalaryTotalDisplay: React.FC<SalaryTotalDisplayProps> = ({
  totalAmount,
  t,
}) => {
  return (
    <div className="py-5 min-h-[68px] max-h-[68px] bg-dashed-border bg-[hsl(var(--background-v2),0.5)] mt-10">
      <div className="flex items-center justify-between px-2">
        <span className="text-lg font-medium text-secondary">
          {t("people.salary-package.total-package")}
        </span>
        <FormattedCurrency
          amount={totalAmount}
          numberStyle="text-xl font-bold text-secondary"
        />
      </div>
    </div>
  );
};
