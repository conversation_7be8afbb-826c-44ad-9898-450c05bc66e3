import { NextRequest, NextResponse } from "next/server";
import { cmAPI } from "@/services/api/cm";
import { createErrorResponse } from "@/utils/api-error-extractor";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    const formTemplateId = searchParams.get("form_template_id");

    const params: {
      form_template_id?: string;
    } = {};

    if (formTemplateId) {
      params.form_template_id = formTemplateId;
    }

    const response = await cmAPI.getFormSections(params);
    return NextResponse.json(response);
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET Form Sections route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
