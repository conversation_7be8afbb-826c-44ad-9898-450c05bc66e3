import { NextRequest, NextResponse } from "next/server";
import { peopleService } from "@/services/api/people";
import { createErrorResponse } from "@/utils/api-error-extractor";

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const getParam = (key: string) => url.searchParams.get(key);

    const period = getParam("context[comparison_period]") || "";
    const id = getParam("context[employee_id]") || "";
    const start_date = getParam("context[start_date]") || "";
    const end_date = getParam("context[end_date]") || "";
    const metricsArray = url.searchParams.getAll("filter[metric_key_in][]");

    const response = await peopleService.getStatistics(
      id || undefined,
      metricsArray,
      period,
      start_date,
      end_date,
    );
    return NextResponse.json(response);
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET statistics route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
