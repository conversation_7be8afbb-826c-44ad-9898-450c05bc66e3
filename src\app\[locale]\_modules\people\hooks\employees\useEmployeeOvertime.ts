import useSWR from "swr";
import { EmployeeOvertimeAttributes } from "../../type/employee";
import { fetcher } from "@/services/fetcher";

type ApiOvertimeResponse = {
  data: {
    id: string;
    type: string;
    attributes: EmployeeOvertimeAttributes;
  };
  meta: Record<string, any>;
};

export const useEmployeeOvertime = (employeeId: string) => {
  const { data, error, isLoading, mutate } = useSWR<ApiOvertimeResponse>(
    employeeId ? `/api/employees/${employeeId}/overtime` : null,
    fetcher,
  );

  return {
    overtimeStats: data?.data?.attributes,
    isLoading,
    error,
    mutate,
  };
};
