"use client";

import React from "react";
import { FormSection } from "@/types/cm";
import { CheckCircle, Circle, Clock } from "lucide-react";
import { cn } from "@/lib/utils";

type WorkflowSidebarProps = {
  sections: FormSection[];
  currentSectionIndex: number;
  completedSections: Set<number>;
  onSectionClick?: (index: number) => void;
  isReadOnly?: boolean;
};

type SectionStatus = "completed" | "current" | "pending" | "locked";

const getSectionStatus = (
  index: number,
  currentIndex: number,
  completedSections: Set<number>,
): SectionStatus => {
  if (completedSections.has(index)) return "completed";
  if (index === currentIndex) return "current";
  if (index < currentIndex) return "completed"; // Previous sections should be accessible
  return "locked"; // Future sections are locked until previous are completed
};

const StatusIcon = ({ status }: { status: SectionStatus }) => {
  switch (status) {
    case "completed":
      return <CheckCircle className="h-5 w-5 text-green-600" />;
    case "current":
      return <Clock className="h-5 w-5 text-blue-600" />;
    case "pending":
    case "locked":
    default:
      return <Circle className="h-5 w-5 text-gray-400" />;
  }
};

export const WorkflowSidebar = ({
  sections,
  currentSectionIndex,
  completedSections,
  onSectionClick,
  isReadOnly = false,
}: WorkflowSidebarProps) => {
  const handleSectionClick = (index: number) => {
    if (isReadOnly || !onSectionClick) return;

    const status = getSectionStatus(index, currentSectionIndex, completedSections);
    
    // Only allow clicking on completed sections or current section
    if (status === "completed" || status === "current") {
      onSectionClick(index);
    }
  };

  return (
    <div className="w-80 bg-gray-50 border-r border-gray-200 p-6 overflow-y-auto">
      {/* Header */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-2">
          تفاصيل الطفل {/* Child Details in Arabic */}
        </h2>
        <p className="text-sm text-gray-600">
          {sections.length} ملاحظات {/* X observations in Arabic */}
        </p>
      </div>

      {/* Progress Overview */}
      <div className="mb-6 p-4 bg-white rounded-lg border">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">التقدم العام</span>
          <span className="text-sm text-gray-600">
            {completedSections.size} / {sections.length}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-green-600 h-2 rounded-full transition-all duration-300"
            style={{
              width: `${(completedSections.size / sections.length) * 100}%`,
            }}
          />
        </div>
      </div>

      {/* Sections List */}
      <div className="space-y-2">
        {sections.map((section, index) => {
          const status = getSectionStatus(index, currentSectionIndex, completedSections);
          const isClickable = status === "completed" || status === "current";

          return (
            <div
              key={section.id}
              onClick={() => handleSectionClick(index)}
              className={cn(
                "flex items-center gap-3 p-3 rounded-lg border transition-all duration-200",
                {
                  // Current section
                  "bg-blue-50 border-blue-200 shadow-sm": status === "current",
                  // Completed sections
                  "bg-green-50 border-green-200": status === "completed",
                  // Locked sections
                  "bg-gray-100 border-gray-200": status === "locked",
                  // Clickable cursor
                  "cursor-pointer hover:shadow-md": isClickable && !isReadOnly,
                  // Disabled cursor
                  "cursor-not-allowed opacity-60": !isClickable || isReadOnly,
                }
              )}
            >
              {/* Status Icon */}
              <div className="flex-shrink-0">
                <StatusIcon status={status} />
              </div>

              {/* Section Content */}
              <div className="flex-1 min-w-0">
                <h3
                  className={cn("text-sm font-medium truncate", {
                    "text-blue-900": status === "current",
                    "text-green-900": status === "completed",
                    "text-gray-500": status === "locked",
                  })}
                >
                  {section.attributes.name}
                </h3>
                
                {section.attributes.description && (
                  <p
                    className={cn("text-xs mt-1 truncate", {
                      "text-blue-700": status === "current",
                      "text-green-700": status === "completed",
                      "text-gray-400": status === "locked",
                    })}
                  >
                    {section.attributes.description}
                  </p>
                )}

                {/* Field count indicator */}
                <div className="flex items-center gap-2 mt-2">
                  <span
                    className={cn("text-xs px-2 py-1 rounded-full", {
                      "bg-blue-100 text-blue-700": status === "current",
                      "bg-green-100 text-green-700": status === "completed",
                      "bg-gray-100 text-gray-500": status === "locked",
                    })}
                  >
                    {section.attributes.sequence_order} ملاحظات
                  </span>
                </div>
              </div>

              {/* Step Number */}
              <div
                className={cn(
                  "flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium",
                  {
                    "bg-blue-600 text-white": status === "current",
                    "bg-green-600 text-white": status === "completed",
                    "bg-gray-300 text-gray-600": status === "locked",
                  }
                )}
              >
                {index + 1}
              </div>
            </div>
          );
        })}
      </div>

      {/* Footer Info */}
      <div className="mt-6 p-4 bg-white rounded-lg border">
        <div className="text-xs text-gray-600 space-y-1">
          <div className="flex items-center gap-2">
            <CheckCircle className="h-3 w-3 text-green-600" />
            <span>مكتمل</span>
          </div>
          <div className="flex items-center gap-2">
            <Clock className="h-3 w-3 text-blue-600" />
            <span>الحالي</span>
          </div>
          <div className="flex items-center gap-2">
            <Circle className="h-3 w-3 text-gray-400" />
            <span>قيد الانتظار</span>
          </div>
        </div>
      </div>
    </div>
  );
};
