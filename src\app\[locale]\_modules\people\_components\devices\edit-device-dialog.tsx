"use client";

import ResponsiveDialog from "@/components/responsive-dialog";
import { useTranslations } from "next-intl";
import { TFunction } from "@/types";
import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import DeviceForm from "./header/device-form";
import { TDevice } from "../../type/devices/device";

type EditDeviceDialogProps = {
  device: TDevice | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (updatedDevice: TDevice) => void;
};

export default function EditDeviceDialog({
  device,
  isOpen,
  onClose,
  onSuccess,
}: EditDeviceDialogProps) {
  const t = useTranslations() as TFunction;

  if (!device) return null;

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={onClose}
      closeBtnStyle="top-[21px]"
      header={
        <>
          <div className="px-6 py-[22px] border-b">
            <DialogTitle className="font-semibold text-[18px] leading-[28px]">
              {t("people.devices-page.edit-device-dialog.header.title")}
            </DialogTitle>
            <DialogDescription className="text-sm text-gray-600 mt-1 sr-only">
              {t("people.devices-page.edit-device-dialog.header.description")}
            </DialogDescription>
          </div>
        </>
      }
    >
      <DeviceForm
        mode="update"
        deviceData={device}
        onClose={onClose}
        onSuccess={onSuccess}
      />
    </ResponsiveDialog>
  );
}
