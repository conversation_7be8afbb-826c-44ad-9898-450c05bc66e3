import { NextRequest, NextResponse } from "next/server";
import { cmAPI } from "@/services/api/cm";
import { createErrorResponse } from "@/utils/api-error-extractor";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    const formSectionId = searchParams.get("form_section_id");
    const caseId = searchParams.get("case_id");

    const params: {
      form_section_id?: string;
      case_id?: string;
    } = {};

    if (formSectionId) {
      params.form_section_id = formSectionId;
    }

    if (caseId) {
      params.case_id = caseId;
    }

    const response = await cmAPI.getFormFields(params);
    return NextResponse.json(response);
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET Form Fields route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
