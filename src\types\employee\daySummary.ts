export type AttendancePeriodAttributes = {
  period_type: string;
  start_timestamp: number;
  end_timestamp: number;
  duration_minutes: number;
  activity_type: string | null;
  is_predicted: boolean;
  notes: string | null;
  date: string;
  formatted_start_time: string;
  formatted_end_time: string;
  formatted_duration: string;
};

export type AttendancePeriod = {
  id: string;
  type: "attendance_period";
  attributes: AttendancePeriodAttributes;
};

export type WorkSchedule = {
  start_time: string;
  end_time: string;
  expected_hours: number;
};

export type Summary = {
  arrival_time: string;
  departure_time: string;
  minutes_spent_working: number;
  work_schedule: WorkSchedule;
};

export type AttendanceMeta = {
  date: string;
  summary: Summary;
};
