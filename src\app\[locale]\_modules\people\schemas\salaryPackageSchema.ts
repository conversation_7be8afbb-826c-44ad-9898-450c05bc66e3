import { z } from "zod";
import { TFunction } from "@/types";
import { parse } from "date-fns";

export const salaryPackageSchema = (
  t: TFunction,
  mode: "create" | "update" = "create",
) => {
  // Base schema with common validations
  const baseSchema = z.object({
    employee_id: z.string({
      required_error: t("common.form.employee.error.required"),
    }),

    base_salary: z
      .string()
      .min(1, { message: t("common.form.salary.base_salary.required") })
      .refine((val) => !isNaN(Number(val.replace(/,/g, ""))), {
        message: t("common.form.verifyCode.error.notNumber"),
      }),

    transportation_allowance: z
      .string()
      .refine((val) => !isNaN(Number(val.replace(/,/g, ""))), {
        message: t("common.form.verifyCode.error.notNumber"),
      })
      .default("0"),

    other_allowances: z
      .string()
      .refine((val) => !isNaN(Number(val.replace(/,/g, ""))), {
        message: t("common.form.verifyCode.error.notNumber"),
      })
      .default("0"),

    notes: z.string().optional(),

    end_date: z.preprocess(
      (val) => {
        if (typeof val === "string") {
          try {
            const parsedDate = parse(val, "dd-MM-yyyy", new Date());
            if (!isNaN(parsedDate.getTime())) {
              return parsedDate;
            }
          } catch (e) {}
          const standardDate = new Date(val);
          if (!isNaN(standardDate.getTime())) {
            return standardDate;
          }
          return undefined;
        }
        return val;
      },
      z
        .date({
          invalid_type_error: t("common.form.date.error.invalid"),
        })
        .optional(),
    ),
  });

  if (mode === "create") {
    return baseSchema.extend({
      effective_date: z.preprocess(
        (val) => {
          if (typeof val === "string") {
            try {
              const parsedDate = parse(val, "dd-MM-yyyy", new Date());
              if (!isNaN(parsedDate.getTime())) {
                return parsedDate;
              }
            } catch (e) {}
            const standardDate = new Date(val);
            if (!isNaN(standardDate.getTime())) {
              return standardDate;
            }
            return new Date("invalid date");
          }
          return val;
        },
        z.date({
          required_error: t("common.form.date.error.required"),
          invalid_type_error: t("common.form.date.error.invalid"),
        }),
      ),
    });
  }

  // For update mode, accept any valid date
  return baseSchema.extend({
    effective_date: z.preprocess(
      (val) => {
        if (typeof val === "string") {
          try {
            const parsedDate = parse(val, "dd-MM-yyyy", new Date());
            if (!isNaN(parsedDate.getTime())) {
              return parsedDate;
            }
          } catch (e) {}
          const standardDate = new Date(val);
          if (!isNaN(standardDate.getTime())) {
            return standardDate;
          }
          return new Date();
        }
        return val;
      },
      z.date({
        required_error: t("common.form.date.error.required"),
        invalid_type_error: t("common.form.date.error.invalid"),
      }),
    ),
  });
};

export type SalaryPackageSchemaType = z.infer<
  ReturnType<typeof salaryPackageSchema>
>;
