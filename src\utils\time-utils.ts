import { AttendancePeriod } from "@/types/employee/daySummary";

export const getTimePercentage = (
  start: string,
  end: string,
  totalTime: number,
): number => {
  const [startHours, startMinutes, startSeconds] = start.split(":").map(Number);
  const [endHours, endMinutes, endSeconds] = end.split(":").map(Number);

  const startDate = new Date(0, 0, 0, startHours, startMinutes, startSeconds);
  const endDate = new Date(0, 0, 0, endHours, endMinutes, endSeconds);

  const diffMs = endDate.getTime() - startDate.getTime();
  return (diffMs / 60000 / totalTime) * 100; // Convert milliseconds to minutes
};

export const getUniqTimePeriods = (periods: AttendancePeriod[]) => {
  const timeArray = new Array<string>();

  periods?.forEach((period) => {
    if (
      period.attributes.formatted_start_time &&
      !timeArray.includes(period.attributes.formatted_start_time)
    ) {
      timeArray.push(period.attributes.formatted_start_time);
    }
    if (
      period.attributes.formatted_end_time &&
      !timeArray.includes(period.attributes.formatted_end_time)
    ) {
      timeArray.push(period.attributes.formatted_end_time);
    }
  });

  return timeArray;
};
