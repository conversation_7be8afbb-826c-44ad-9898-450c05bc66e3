// Simulate network latency with a delay
function delay(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export async function fetchAppointmentCount(): Promise<number> {
  await delay(1000); // Simulate 1 second delay
  // Return dummy data
  return 42;
}

export async function fetchBeneficiaryCount(): Promise<number> {
  await delay(3000); // Simulate 3 second delay
  // Return dummy data
  return 100;
}

export async function fetchVideoCount(): Promise<number> {
  await delay(2000); // Simulate 1 second delay
  // Return dummy data
  return 10;
}

// Fetch all card data concurrently using Promise.all:
export async function fetchCardData() {
  const [appointmentCount, beneficiaryCount, videoCount] = await Promise.all([
    fetchAppointmentCount(),
    fetchBeneficiaryCount(),
    fetchVideoCount(),
  ]);
  return { appointmentCount, beneficiaryCount, videoCount };
}
