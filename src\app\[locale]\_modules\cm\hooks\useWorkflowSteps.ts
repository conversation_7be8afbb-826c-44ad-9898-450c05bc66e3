import useSWR from "swr";
import { fetcher } from "@/services/fetcher";

type UseWorkflowStepsParams = {
  caseId?: string;
};

export const useWorkflowSteps = ({ caseId }: UseWorkflowStepsParams = {}) => {
  const apiUrl = caseId ? `/api/cm/workflow-steps?case_id=${caseId}` : null;

  const { data, error, isLoading, mutate } = useSWR(apiUrl, fetcher);

  console.log("🚀 ~ useWorkflowSteps ~ data:", data);

  return {
    workflowSteps: data?.data || [],
    meta: data?.meta,
    isLoading,
    error,
    mutate,
  };
};
