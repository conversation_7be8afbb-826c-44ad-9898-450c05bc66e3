import { useOptimisticMutation } from "@/hooks/ues-optimistic-mutatation";
import { useApprovalMutations } from "./useApprovalMutations";
import { fetcher } from "@/services/fetcher";
import { EmployeeLeavesResponse, LeaveDetail } from "../type/employee-leaves";
import { OptimisticApprovalAction } from "../utils/approval-optimistic-updates";
import { createApprovalWorkflowUpdate } from "../utils/approval-workflow-helpers";

interface UseLeaveRequestMutationsProps {
  key: string;
}

type MutationParams = {
  id: string;
  leaveId?: string;
  comment?: string;
  approvalRequestId?: string;
  employeeId?: string;
};

// Leave-specific status update logic
const updateLeaveStatus = (
  leave: any,
  isWorkflowComplete: boolean,
  action: OptimisticApprovalAction,
) => {
  let newStatus = leave.attributes.status;

  if (action === "reject") {
    newStatus = "rejected";
  } else if (action === "approve" && isWorkflowComplete) {
    newStatus = "approved";
  } else if (action === "approve") {
    newStatus = "pending";
  }

  return {
    ...leave,
    attributes: {
      ...leave.attributes,
      status: newStatus,
    },
  };
};

const createLeaveApprovalUpdate = (
  currentData: EmployeeLeavesResponse | undefined,
  leaveId: string,
  action: OptimisticApprovalAction,
  comment?: string,
): EmployeeLeavesResponse | undefined => {
  return createApprovalWorkflowUpdate(
    currentData,
    leaveId,
    action,
    updateLeaveStatus,
    comment,
  );
};

export const useLeaveRequestMutations = ({
  key,
}: UseLeaveRequestMutationsProps) => {
  const {
    approveRequest: approveLeaveRequest,
    rejectRequest: rejectLeaveRequest,
    withdrawLeaveRequest,
    isApproving,
    isRejecting,
    isWithdrawing,
  } = useApprovalMutations();

  const result = useOptimisticMutation<
    EmployeeLeavesResponse,
    MutationParams,
    EmployeeLeavesResponse
  >({
    key,
    fetcher: fetcher,
    mutations: {
      acceptLeaveRequest: {
        updateFn: (data, params) => {
          const leaveId = params.leaveId || params.id;
          return createLeaveApprovalUpdate(
            data,
            leaveId,
            "approve",
            params.comment,
          );
        },
        mutationFn: async (params) => {
          try {
            await approveLeaveRequest(params.id, params.comment || "");
            return { data: undefined };
          } catch (error) {
            return {
              error:
                error instanceof Error
                  ? error
                  : new Error("Failed to approve leave request"),
            };
          }
        },
      },
      rejectLeaveRequest: {
        updateFn: (data, params) => {
          const leaveId = params.leaveId || params.id;
          return createLeaveApprovalUpdate(
            data,
            leaveId,
            "reject",
            params.comment,
          );
        },
        mutationFn: async (params) => {
          try {
            await rejectLeaveRequest(params.id, params.comment || "");
            return { data: undefined };
          } catch (error) {
            return {
              error:
                error instanceof Error
                  ? error
                  : new Error("Failed to reject leave request"),
            };
          }
        },
      },
      withdrawLeaveRequest: {
        updateFn: (data, params) => {
          const leaveId = params.leaveId || params.id;
          if (!data) return data;

          return {
            ...data,
            data: data.data.map((leave: LeaveDetail) =>
              leave.id === leaveId
                ? {
                    ...leave,
                    attributes: {
                      ...leave.attributes,
                      status: "withdrawn",
                    },
                  }
                : leave,
            ),
          };
        },
        mutationFn: async (params) => {
          try {
            if (!params.employeeId) {
              throw new Error(
                "Employee ID is required for withdrawing leave request",
              );
            }

            await withdrawLeaveRequest(params.id, params.employeeId);
            return { data: undefined };
          } catch (error) {
            return {
              error:
                error instanceof Error
                  ? error
                  : new Error("Failed to withdraw leave request"),
            };
          }
        },
      },
    },
    defaultData: {
      data: [],
      meta: {
        pagination: {
          count: 0,
          page: 1,
          limit: 10,
          from: 0,
          to: 0,
        },
      },
    },
    swrOptions: {
      dedupingInterval: 2000,
      revalidateAfterMutation: false,
      revalidateOnFocus: false,
    },
  });

  return {
    ...result,
    isApproving,
    isRejecting,
    isWithdrawing,
  };
};
