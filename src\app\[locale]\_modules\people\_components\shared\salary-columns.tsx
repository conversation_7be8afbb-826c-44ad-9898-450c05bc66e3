"use client";

import { Checkbox } from "@/components/ui/checkbox";
import { ColumnDef, Table, TableMeta } from "@tanstack/react-table";
import { mapSalaryStatusToCanonical } from "@/constants/translations-mapping";
import { Locale } from "@/i18n/routing";
import { TFunction } from "@/types";
import FormattedCurrency from "@/components/formatted-currency";
import SalaryStatus from "@/components/status/salary-status";
import { SALARY_STATUS } from "@/constants/enum";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { InfoIcon } from "lucide-react";
import {
  SalaryCalculation,
  SalaryCalculationDetail,
} from "../../type/employees-salaries";
import { CalculationDetails } from "./calculation-details";
import { findEmployeeById } from "../../utils/find-employee";
import { TIncludedEmployee } from "../../type/employee-leaves";
import { formatDate } from "@/lib/dateFormatter";
import { ApprovalRequestData } from "../../type/approval-request";
import ApprovalWorkFlow from "../approval-workflow";
import { SalaryActionsWithButtons } from "../employees/profile/salary/salary-actions-with-buttons";
import { filterIncludedByType } from "../../utils/included-data/getIncludedItem";

// Define the shared translation function type
interface TableMetaWithTranslation extends TableMeta<SalaryCalculation> {
  t: TFunction;
  locale?: Locale;
  onApproveSalary?: (salaryId: string) => void;
  onRejectSalary?: (salaryId: string) => void;
  onSubmitSalary?: (salaryId: string) => void;
  onPaySalary?: (salaryId: string) => void;
  onRegenerateSlip?: (salaryId: string) => void;
  employeesData?: TIncludedEmployee[];
  calculationDetailsData?: SalaryCalculationDetail[];
  includedData?: (
    | TIncludedEmployee
    | ApprovalRequestData
    | SalaryCalculationDetail
  )[];
  isApproving?: boolean;
  isRejecting?: boolean;
  isSubmitting?: boolean;
  isPaying?: boolean;
  isRegenerating?: boolean;
  onShowDetails?: (employeeSalary: SalaryCalculation) => void;
  onShowEditNote?: (employeeSalary: SalaryCalculation) => void;
  onShowWorkflowModal?: (salaryCalculation: SalaryCalculation) => void;
}

// Helper function to get meta data
const getMeta = (table: Table<SalaryCalculation>) =>
  table.options.meta as TableMetaWithTranslation;

// Base columns that are shared between both tables
const baseColumns: ColumnDef<SalaryCalculation>[] = [
  // Selection Checkbox
  {
    id: "select",
    enableSorting: false,
    enableHiding: false,
    header: ({ table }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#1A1C1E] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#1A1C1E] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(Boolean(value))}
        aria-label="Select row"
      />
    ),
  },
  // Period
  {
    accessorKey: "period",
    header: ({ table }) =>
      getMeta(table).t("people.employees-page.salary.table.columns.period"),
    enableColumnFilter: true,
    meta: {
      filterType: "date",
      filterVariant: "date",
    },
    size: 100,
    cell: ({ row, table }) => {
      const period = row.original.attributes.period;
      const { locale } = getMeta(table);
      return (
        <p className="text-sm font-semibold text-black">
          {formatDate(period, locale || "en")}
        </p>
      );
    },
  },
  // Total Hours
  {
    accessorKey: "totalHours",
    header: ({ table }) =>
      getMeta(table).t("people.employees-page.salary.table.columns.totalHours"),
    enableColumnFilter: true,
    meta: {
      filterType: "number",
      filterVariant: "number",
    },
    size: 100,
    cell: ({ row }) => {
      const totalHours = row.original.attributes.total_hours;
      return (
        <p className="text-sm font-semibold text-black">{totalHours || "-"}</p>
      );
    },
  },
  // Gross Salary
  {
    accessorKey: "grossSalary",
    header: ({ table }) =>
      getMeta(table).t(
        "people.employees-page.salary.table.columns.grossSalary",
      ),
    enableColumnFilter: true,
    meta: {
      filterType: "number",
      filterVariant: "number",
    },
    size: 100,
    cell: ({ row }) => (
      <FormattedCurrency
        amount={Number(row.original.attributes.gross_salary)}
        numberStyle="text-sm font-semibold text-black"
      />
    ),
  },
  // Net Salary
  {
    accessorKey: "netSalary",
    header: ({ table }) =>
      getMeta(table).t("people.employees-page.salary.table.columns.netSalary"),
    enableColumnFilter: true,
    meta: {
      filterType: "number",
      filterVariant: "number",
    },
    size: 100,
    cell: ({ row }) => (
      <FormattedCurrency
        amount={Number(row.original.attributes.net_salary)}
        numberStyle="text-sm font-semibold text-black"
      />
    ),
  },
  // Total Deductions with Tooltip
  {
    accessorKey: "totalDeductions",
    header: ({ table }) =>
      getMeta(table).t(
        "people.employees-page.salary.table.columns.totalDeductions",
      ),
    enableColumnFilter: true,
    meta: {
      filterType: "number",
      filterVariant: "number",
    },
    size: 100,
    cell: ({ row, table }) => {
      const { t, calculationDetailsData } = getMeta(table);
      const deductions = row.original.attributes.deductions;
      const totalDeductions = row.original.attributes.total_deductions;

      return (
        <div className="flex justify-center text-center items-center gap-1">
          <FormattedCurrency
            amount={Number(totalDeductions)}
            numberStyle="text-sm font-semibold text-black"
          />
          <DeductionsTooltip
            totalDeductions={totalDeductions}
            deductions={deductions}
            salaryCalculation={row.original}
            calculationDetailsData={calculationDetailsData || []}
            t={t}
          />
        </div>
      );
    },
  },
  // Status
  {
    accessorKey: "status",
    header: ({ table }) =>
      getMeta(table).t("people.employees-page.salary.table.columns.status"),
    enableColumnFilter: true,
    meta: {
      filterType: "text",
      filterVariant: "select",
      filterOptions: [
        { label: "Draft", value: "draft" },
        { label: "Submitted", value: "submitted" },
        { label: "Approved", value: "approved" },
        { label: "Paid", value: "paid" },
        { label: "Rejected", value: "rejected" },
      ],
    },
    size: 100,
    cell: ({ row, table }) => {
      const { t, includedData, onShowWorkflowModal } = getMeta(table);
      const status = row.original.attributes.status;
      const canonicalStatus = mapSalaryStatusToCanonical(status);

      // Get the translated label from the translation files
      const translatedLabel = t(
        `common.status.salary-status.${canonicalStatus}`,
      );

      return (
        <div className="flex items-center text-center justify-center">
          <ApprovalWorkFlow
            item={row.original}
            included={
              filterIncludedByType(includedData, "salary_calculation_detail") ||
              []
            }
            onShowWorkflowModal={onShowWorkflowModal}
            renderTrigger={() => (
              <SalaryStatus
                status={canonicalStatus as SALARY_STATUS}
                label={translatedLabel}
              />
            )}
          />
        </div>
      );
    },
  },
  // Actions
  {
    id: "actions",
    header: ({ table }) => (
      <div className="text-center">
        {getMeta(table).t("people.employees-page.salary.table.columns.actions")}
      </div>
    ),
    cell: ({ row, table }) => {
      const {
        onApproveSalary,
        onRejectSalary,
        onSubmitSalary,
        onPaySalary,
        onRegenerateSlip,
        isApproving,
        isRejecting,
        isSubmitting,
        includedData,
        isPaying,
        isRegenerating,
      } = getMeta(table);
      return (
        <div className="flex justify-center">
          <SalaryActionsWithButtons
            row={row}
            onApproveSalary={onApproveSalary}
            onRejectSalary={onRejectSalary}
            onSubmitSalary={onSubmitSalary}
            onPaySalary={onPaySalary}
            onRegenerateSlip={onRegenerateSlip}
            isApproving={isApproving}
            isRejecting={isRejecting}
            isSubmitting={isSubmitting}
            includedData={
              includedData?.filter(
                (item) =>
                  item.type === "salary_calculation_detail" ||
                  item.type === "approval_request",
              ) || []
            }
            isPaying={isPaying}
            isRegenerating={isRegenerating}
          />
        </div>
      );
    },
  },
];

// Export columns for employee salary table
export const employeeSalaryColumns: ColumnDef<SalaryCalculation>[] =
  baseColumns;

// Export columns for main employees salaries table with employee name column
export const employeesSalariesColumns: ColumnDef<SalaryCalculation>[] = [
  // First add the selection checkbox
  baseColumns[0],
  // Then add the employee name column
  {
    accessorKey: "employeeName",
    header: ({ table }) =>
      getMeta(table).t(
        "people.employees-salaries-page.table.columns.employeeName",
      ),
    enableColumnFilter: true,
    meta: {
      filterType: "text",
      filterVariant: "text",
    },
    size: 100,
    cell: ({ row, table }) => {
      const { employeesData } = getMeta(table);
      const employeeId = row.original.relationships?.employee?.data?.id;
      const employee =
        employeesData && employeeId
          ? findEmployeeById(employeesData, employeeId)
          : null;

      return <div className="flex justify-center">{employee?.name || "-"}</div>;
    },
  },
  // Then add all the remaining columns
  ...baseColumns.slice(1),
];

// Shared deductions tooltip component
const DeductionsTooltip = ({
  totalDeductions,
  deductions,
  salaryCalculation,
  calculationDetailsData,
  t,
}: {
  totalDeductions: SalaryCalculation["attributes"]["total_deductions"];
  deductions: SalaryCalculation["attributes"]["deductions"];
  salaryCalculation: SalaryCalculation;
  calculationDetailsData: SalaryCalculationDetail[];
  t: TFunction;
}) => {
  const hasCalculationDetails = calculationDetailsData.length > 0;
  return (
    <TooltipProvider>
      <Tooltip delayDuration={100}>
        <TooltipTrigger asChild>
          <InfoIcon className="h-4 w-4 text-secondary hover:text-secondary/80 cursor-help transition-colors" />
        </TooltipTrigger>
        <TooltipContent
          className="p-4 min-w-[250px] max-w-[400px] bg-white border border-border shadow-md rounded-md"
          sideOffset={5}
        >
          {hasCalculationDetails ? (
            <CalculationDetails
              salaryCalculation={salaryCalculation}
              calculationDetailsData={calculationDetailsData}
              t={t}
            />
          ) : (
            <div className="space-y-3">
              <h4 className="text-sm font-semibold text-secondary mb-2 pb-1 border-b border-border">
                {t("people.employees-page.salary.table.deductions.title")}
              </h4>
              <div className="space-y-2 text-xs">
                {/* Leave Deductions */}
                {deductions.leave_deductions > 0 && (
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">
                      {t(
                        "people.employees-page.salary.table.deductions.leave_deductions",
                      )}
                      :
                    </span>
                    <FormattedCurrency
                      amount={Number(deductions.leave_deductions)}
                      numberStyle="font-medium text-black"
                    />
                  </div>
                )}

                {/* Salary Advances */}
                {deductions.salary_advances > 0 && (
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">
                      {t(
                        "people.employees-page.salary.table.deductions.salary_advances",
                      )}
                      :
                    </span>
                    <FormattedCurrency
                      amount={Number(deductions.salary_advances)}
                      numberStyle="font-medium text-black"
                    />
                  </div>
                )}

                {/* Other Deductions */}
                {deductions.other_deductions > 0 && (
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">
                      {t(
                        "people.employees-page.salary.table.deductions.other_deductions",
                      )}
                      :
                    </span>
                    <FormattedCurrency
                      amount={Number(deductions.other_deductions)}
                      numberStyle="font-medium text-black"
                    />
                  </div>
                )}

                {/* Medical Insurance */}
                {deductions.medical_insurance > 0 && (
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">
                      {t(
                        "people.employees-page.salary.table.deductions.medical_insurance",
                      )}
                      :
                    </span>
                    <FormattedCurrency
                      amount={Number(deductions.medical_insurance)}
                      numberStyle="font-medium text-black"
                    />
                  </div>
                )}

                <div className="my-1 border-t border-dashed border-gray-200"></div>

                {/* Income Tax */}
                {deductions.income_tax > 0 && (
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">
                      {t(
                        "people.employees-page.salary.table.deductions.income_tax",
                      )}
                      :
                    </span>
                    <FormattedCurrency
                      amount={Number(deductions.income_tax)}
                      numberStyle="font-medium text-black"
                    />
                  </div>
                )}

                {/* Employee Social Security */}
                {deductions.employee_social_security > 0 && (
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">
                      {t(
                        "people.employees-page.salary.table.deductions.employee_social_security",
                      )}
                      :
                    </span>
                    <FormattedCurrency
                      amount={Number(deductions.employee_social_security)}
                      numberStyle="font-medium text-black"
                    />
                  </div>
                )}

                <div className="mt-2 pt-1 border-t border-border">
                  <div className="flex justify-between items-center">
                    <span className="font-semibold text-secondary">
                      {t("people.employees-page.salary.table.deductions.total")}
                      :
                    </span>
                    <FormattedCurrency
                      amount={Number(totalDeductions)}
                      numberStyle="font-semibold text-secondary"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
