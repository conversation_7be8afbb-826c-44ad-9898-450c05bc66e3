"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { HealthNoBack } from "../../../public/images/icons";
import { ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { useSystem } from "@/contexts/system-provider";
import { useUser } from "@/contexts/user-provider";

type SystemSelectorProps = {
  onClick?: () => void;
  compact?: boolean;
  className?: string;
  showIcon?: boolean;
};

/**
 * Reusable system selector button component
 * Used in both sidebar and mobile menu
 */
const SystemSelector: React.FC<SystemSelectorProps> = ({
  onClick,
  compact = false,
  className,
  showIcon = true,
}) => {
  const { currSystem, systemDisplayName } = useSystem();
  const { user } = useUser();

  return (
    <Button
      onClick={onClick}
      className={cn(
        "rounded-full h-11 p-3 text-sm font-semibold",
        compact && "rounded-lg h-8",
        className,
      )}
    >
      {showIcon && <HealthNoBack className="w-[18px] h-4 fill-none" />}
      {!compact && (
        <>
          <span className="truncate block w-full min-w-0 max-w-[140px] max-sm:w-auto">
            {user?.systems?.find((system) => system.alias === currSystem)
              ?.display_name || systemDisplayName}
          </span>
          <ChevronDown />
        </>
      )}
    </Button>
  );
};

export default SystemSelector;
