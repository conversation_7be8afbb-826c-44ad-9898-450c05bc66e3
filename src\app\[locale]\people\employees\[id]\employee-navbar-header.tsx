"use client";

import { useParams, usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { useEmployeeDetails } from "../../../_modules/people/hooks/employees/useEmployeeDetails";
import { useTranslations } from "next-intl";
import ProfileNavHeader from "../../../_modules/people/_components/employees/profile/employee-profile-nav-header";

export default function EmployeeNavbarHeader() {
  const pathname = usePathname();
  const t = useTranslations();
  const { id } = useParams();
  const [employeeId, setEmployeeId] = useState<string>("");
  const { employee, error, isLoading } = useEmployeeDetails(employeeId);

  useEffect(() => {
    setEmployeeId(id as string);
  }, [pathname, id]);

  return (
    <div className="max-md:px-2">
      <ProfileNavHeader
        headerName={t("people.employees-page.table.title").split(" ")[1]}
        name={
          error
            ? `Error: ${error.message || "Failed to load employee"}`
            : employee?.name || ""
        }
        isLoading={isLoading}
      />
    </div>
  );
}
