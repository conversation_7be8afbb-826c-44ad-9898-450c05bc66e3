"use client";

import {
  startTransition,
  useActionState,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { Loader } from "lucide-react";
import { PAGES } from "@/enums";
import { ActionState, TinputField } from "@/types";
import { DeviceSchemaType, deviceSchema } from "../../../schemas/deviceSchema";
import useFormFields from "@/hooks/useFormFields";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import FormFieldRenderer from "@/components/auth/fields/form-field-renderer";
import { TDevice } from "../../../type/devices/device";
import { useToastMessage } from "@/hooks/use-toast-message";

import { deviceAction } from "../../../actions/device-action";
import AdditionalSettings from "../additional-settings";
import { useDeviceMutations } from "../../../hooks/devices/useDeviceMutations";
type DeviceFormProps = {
  mode?: "create" | "update";
  deviceData?: TDevice;
  onClose?: () => void;
  onSuccess?: (device: TDevice, mode: "create" | "update") => void; // Callback with returned device data
};

export default function DeviceForm({
  mode = "create",
  deviceData,
  onClose,
  onSuccess,
}: DeviceFormProps) {
  const t = useTranslations();
  const formRef = useRef<HTMLFormElement | null>(null);
  const [selectKey, setSelectKey] = useState<number>(0);
  const [additionalSettings, setAdditionalSettings] = useState<
    Array<{
      id: string;
      type: string;
      value: string;
    }>
  >([]);

  const { getFormFields } = useFormFields({ formType: PAGES.ADDDEVICE, t });
  const { showToast } = useToastMessage();
  const { addDevice, updateDevice } = useDeviceMutations({});
  const initialState: ActionState<TDevice> = {
    error: "",
    success: "",
    issues: [],
    data: null,
  };

  const [state, submitAction, isPending] = useActionState(
    deviceAction.bind(null, mode, deviceData?.id || null),
    initialState,
  );

  // Helper function to convert device config to additional settings format
  const convertDeviceConfigToSettings = (deviceData: TDevice) => {
    // For update mode, don't automatically convert existing configs to additional settings
    // This prevents sending malformed FormData with JSON.stringify values
    // Users can manually add additional settings if needed
    // TODO: to be implemented later
    return [];
  };

  const defaultFormValues = useMemo(
    () => ({
      name: mode === "update" && deviceData ? deviceData.attributes.name : "",
      adapter_type:
        mode === "update" && deviceData
          ? deviceData.attributes.adapter_type
          : "",
      ip_address:
        mode === "update" && deviceData ? deviceData.attributes.ip_address : "",
      port:
        mode === "update" && deviceData
          ? deviceData.attributes.port.toString()
          : "",
      location:
        mode === "update" && deviceData ? deviceData.attributes.location : "",
      additional_settings:
        mode === "update" && deviceData
          ? convertDeviceConfigToSettings(deviceData)
          : [],
    }),
    [mode, deviceData],
  );
  const defaultSettings = defaultFormValues.additional_settings || [];

  const form = useForm<DeviceSchemaType>({
    resolver: zodResolver(deviceSchema(t)),
    defaultValues: defaultFormValues,
    mode: "all",
  });

  // Reset form when defaultFormValues change (for edit mode)
  useEffect(() => {
    form.reset(defaultFormValues);
    if (defaultFormValues.additional_settings) {
      setAdditionalSettings(defaultFormValues.additional_settings || []);
    }
  }, [form, defaultFormValues]);

  useEffect(() => {
    if (state?.success && state?.data) {
      showToast("success", state.success);

      // Update the local cache using mutations
      if (mode === "create") {
        addDevice(state.data);
      } else {
        updateDevice(state.data.id, state.data);
      }

      // Call the onSuccess callback with the returned device data
      onSuccess?.(state.data, mode);

      // Reset form for create mode
      if (mode === "create") {
        form.reset(defaultFormValues);
        setAdditionalSettings(defaultFormValues.additional_settings || []);
      }

      // Close the dialog/form
      onClose?.();

      setTimeout(() => {
        setSelectKey((prev) => prev + 1);
      }, 0);
    }
  }, [state, form, defaultFormValues, mode]);

  useEffect(() => {
    if (state?.error) {
      showToast("error", state.issues![0] || state.error);
    }
  }, [state]);

  const handleSubmit = (data: DeviceSchemaType) => {
    const formData = new FormData();

    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (key !== "additional_settings") {
          formData.append(key, value.toString());
        }
      }
    });

    // Handle device configuration settings - only send user-added settings
    if (additionalSettings.length > 0) {
      additionalSettings.forEach((setting) => {
        if (
          setting.value !== undefined &&
          setting.value !== null &&
          setting.value.trim() !== "" &&
          setting.type &&
          setting.type.trim() !== "" &&
          // Skip auto-generated config settings that contain JSON
          !setting.value.startsWith("{") &&
          !setting.value.startsWith("[")
        ) {
          formData.append(setting.type, setting.value.toString());
        } else {
          console.log(`Skipping invalid/auto-generated setting:`, setting);
        }
      });
    }

    startTransition(async () => {
      submitAction(formData);
    });
  };

  return (
    <div className="bg-white rounded-lg max-w-2xl mx-auto w-[99.5%]">
      <Form {...form}>
        <form
          ref={formRef}
          action={submitAction}
          onSubmit={form.handleSubmit(handleSubmit)}
          className="flex flex-col"
        >
          <div className="flex-1 flex flex-col gap-6 min-h-[calc(100vh-15rem)] pb-6">
            {(getFormFields() as TinputField<DeviceSchemaType>[]).map(
              (fieldConfig) => (
                <FormFieldRenderer
                  key={`${fieldConfig.name?.toString()}-${
                    fieldConfig.type === "select" ? selectKey : ""
                  }`}
                  fieldConfig={fieldConfig}
                  form={form}
                  isPending={isPending}
                />
              ),
            )}

            {/* Additional Settings Component */}
            <AdditionalSettings
              initialSettings={additionalSettings}
              onSettingsChange={(settings) => {
                setAdditionalSettings(settings);
                // Also update the form value for validation
                form.setValue("additional_settings", settings);
              }}
            />
          </div>
          <div className="sticky border-t border-t-slate-200 pt-2.5 bottom-0 left-0 w-[100%] rounded-t-[18px] bg-white flex flex-row items-center justify-between gap-4 sm:gap-6">
            <Button
              disabled={isPending || !form.formState.isValid}
              type="submit"
              className="px-4 py-2 w-full h-12 sm:max-w-[244px] rounded-lg"
            >
              {isPending ? (
                <Loader className="animate-spin" />
              ) : mode === "create" ? (
                t("common.buttonText.add")
              ) : (
                t("common.buttonText.update")
              )}
            </Button>
            <Button
              variant={"outline"}
              type="button"
              className="w-full h-12 sm:max-w-[244px] rounded-lg"
              onClick={() => {
                form.reset(defaultFormValues);
                setAdditionalSettings(defaultSettings);
                setTimeout(() => {
                  setSelectKey((prev) => prev + 1);
                }, 0);
              }}
            >
              {t("common.buttonText.cancel2")}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
