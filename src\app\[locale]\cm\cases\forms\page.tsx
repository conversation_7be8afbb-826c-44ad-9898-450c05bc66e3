import { FormsContainer } from "@/components/forms/forms-container";

type FormsPageProps = {
  searchParams: Promise<{ case_id?: string; mode?: "new" | "edit" }>;
};

export default async function FormsPage({ searchParams }: FormsPageProps) {
  const params = await searchParams;

  return (
    <div className="container mx-auto p-6">
      <FormsContainer caseId={params.case_id} mode={params.mode || "new"} />
    </div>
  );
}
