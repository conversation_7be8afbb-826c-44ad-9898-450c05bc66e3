# syntax=docker.io/docker/dockerfile:1
FROM node:22.15.0-alpine3.21

# Install system dependencies (if needed)
RUN apk add --no-cache libc6-compat

WORKDIR /app

# Copy dependency manifests and install dependencies
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* .npmrc* ./
RUN \
  if [ -f yarn.lock ]; then yarn; \
  elif [ -f package-lock.json ]; then npm install; \
  elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm install; \
  else echo "Lockfile not found." && exit 1; \
  fi

# Copy the rest of the source code
#COPY . .

# Expose the default Next.js port
EXPOSE 3000

# Run the Next.js development server
CMD ["npm", "run", "dev"]