import SalaryPackageHeader from "@/app/[locale]/_modules/people/_components/employees/profile/salary-package/header";
import SalaryPackageDisplay from "@/app/[locale]/_modules/people/_components/employees/profile/salary-package/salary-package-display";
import { getTranslations } from "next-intl/server";

export default async function SalaryPackage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const t = await getTranslations();
  return (
    <>
      <SalaryPackageHeader
        title={`${t("people.employees-page.profile.tabs.salary-package")}`}
        employeeId={id}
      />
      <SalaryPackageDisplay employeeId={id} />
    </>
  );
}
