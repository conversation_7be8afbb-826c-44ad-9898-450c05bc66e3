"use client";

import { ColumnDef, TableMeta, Table } from "@tanstack/react-table";
import { TFunction } from "@/types";
import { Locale } from "@/i18n/routing";
import { Checkbox } from "@/components/ui/checkbox";
import {
  TDeviceUser,
  TIncludedEmployee,
} from "../../../../type/devices/usersList";
import { Button } from "@/components/ui/button";
import { findEmployeeById } from "../../../../utils/find-employee";
import { TIncludedData } from "../../../../type/employee";
import { formatDate } from "@/lib/dateFormatter";
import { usePermission } from "@/contexts/PermissionContext";
import { PermissionEnum } from "@/enums/Permission";

interface TableMetaWithTranslation extends TableMeta<TDeviceUser> {
  t: TFunction;
  locale?: Locale;
  onFixCode?: (user: TDeviceUser) => void;
  includedData?: TIncludedData[];
  employeeData?: TIncludedEmployee[];
}

const getMeta = (table: Table<TDeviceUser>) =>
  table.options.meta as TableMetaWithTranslation;

// Component for fix code button with permission checking
const FixCodeButton = ({ user, meta }: { user: TDeviceUser; meta: TableMetaWithTranslation }) => {
  const { hasPermission } = usePermission();

  // Check if user has permission to update device user codes
  // Manage permission can do everything
  const canUpdateUserCodes = hasPermission(PermissionEnum.MANAGE_ATTENDANCE_DEVICE) ||
                            hasPermission(PermissionEnum.UPDATE_ATTENDANCE_DEVICE);

  if (!canUpdateUserCodes) {
    return null;
  }

  return (
    <Button
      className="rounded-xl text-sm py-2.5 px-4 h-10 font-semibold"
      onClick={() => meta.onFixCode?.(user)}
    >
      {meta.t("people.devices-page.users-table.actions.fixCode")}
    </Button>
  );
};

export const usersColumns: ColumnDef<TDeviceUser>[] = [
  {
    id: "select",
    enableSorting: false,
    enableHiding: false,
    header: ({ table }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#ACB5BB] flex items-center justify-center p-0"
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#1A1C1E] flex items-center justify-center p-0"
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
  },
  {
    accessorKey: "attributes.name",
    id: "userName",
    header: ({ table }) => (
      <div className="text-start">
        {getMeta(table).t("people.devices-page.users-table.columns.userName")}
      </div>
    ),
    cell: ({ row }) => (
      <p className="text-sm font-semibold text-black text-start">
        {row.original.attributes.name}
      </p>
    ),
  },
  {
    accessorKey: "created_at",
    id: "joinDate",
    header: ({ table }) => (
      <div className="text-start">
        {getMeta(table).t("people.devices-page.users-table.columns.joinDate")}
      </div>
    ),
    cell: ({ row, table }) => {
      const { employeeData = [], locale } = getMeta(table);
      const employeeId = row.original.relationships.employee?.data?.id;
      
      const getJoinDate = () => {
        if (!employeeId) return null;
        const employee = employeeData.find((item: TIncludedEmployee) => item.id === employeeId);
        return employee?.attributes?.start_date || null;
      };
      
      const joinDate = getJoinDate();
      
      return (
        <p className="text-sm font-semibold text-gray-500 text-start">
          {joinDate ? formatDate(joinDate, locale || 'en') : 'N/A'}
        </p>
      );
    },
  },
  {
    accessorKey: "attributes.user_id",
    id: "deviceUserId",
    header: ({ table }) => (
      <div className="text-start">
        {getMeta(table).t(
          "people.devices-page.users-table.columns.deviceUserId",
        )}
      </div>
    ),
    cell: ({ row }) => (
      <p className="text-sm font-semibold text-gray-500 text-start">
        {row.original.attributes.user_id}
      </p>
    ),
  },
  {
    accessorKey: "relationships.employee.data.id",
    id: "userId",
    header: ({ table }) => (
      <div className="text-start">
        {getMeta(table).t("people.devices-page.users-table.columns.userId")}
      </div>
    ),
    cell: ({ row, table }) => {
      const { employeeData } = getMeta(table);
      const employeeData_relationship = row.original.relationships.employee.data;

      // Check if employee data exists before accessing id
      if (!employeeData_relationship) {
        return (
          <p className="text-sm font-semibold text-gray-500 text-start">
            N/A
          </p>
        );
      }

      const employeeId = employeeData_relationship.id;
      const employee = findEmployeeById(employeeData ?? [], employeeId);

      return (
        employee ? (
          <p className="text-sm font-semibold text-gray-500 text-start">
            {employee?.user_id || "N/A"}
          </p>
        ) : (
          <p className="text-sm font-semibold text-gray-500 text-start">
            N/A
          </p>
        )
      );
    },
  },
  {
    id: "actions",
    header: ({ table }) => (
      <div className="text-start">
        {getMeta(table).t("people.devices-page.users-table.columns.actions")}
      </div>
    ),
    cell: ({ row, table }) => {
      const meta = getMeta(table);
      return (
        <div className="text-start">
          <FixCodeButton user={row.original} meta={meta} />
        </div>
      );
    },
  },
];
