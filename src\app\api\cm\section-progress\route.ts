import { NextRequest, NextResponse } from "next/server";
import { cmAPI } from "@/services/api/cm";
import { createErrorResponse } from "@/utils/api-error-extractor";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const caseId = searchParams.get("case_id");
    const sectionId = searchParams.get("section_id");

    if (!caseId || !sectionId) {
      return NextResponse.json(
        { error: "case_id and section_id parameters are required" },
        { status: 400 },
      );
    }

    const response = await cmAPI.getSectionProgress({
      case_id: caseId,
      section_id: sectionId,
    });
    return NextResponse.json(response);
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET Section Progress route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
