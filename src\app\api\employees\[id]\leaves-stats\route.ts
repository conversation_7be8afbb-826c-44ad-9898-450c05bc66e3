import { NextRequest, NextResponse } from "next/server";
import { EmployeeLeavesStats } from "@/app/[locale]/_modules/people/type/employee-leaves";

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: "Missing employee ID" },
        { status: 400 },
      );
    }

    // For now, we'll return mock data
    const leavesStats: EmployeeLeavesStats = {
      pendingLeaves: {
        value: 0,
        percentageChange: 12,
      },
      rejectedLeaves: {
        value: 1,
        percentageChange: 12,
      },
      approvedLeaves: {
        value: 8,
        percentageChange: 12,
      },
      leaveRequests: {
        value: 9,
        percentageChange: 12,
      },
    };

    return NextResponse.json({
      data: {
        id: id,
        type: "employee_leaves",
        attributes: leavesStats,
      },
      meta: {
        // Any additional metadata can go here
      },
    });
  } catch (error) {
    console.error("Error in GET employee leaves stats route:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
