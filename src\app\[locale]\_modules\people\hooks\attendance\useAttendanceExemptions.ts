import useSWR from "swr";
import { fetcher } from "@/services/fetcher";
import { THolidaysResponse } from "@/types/settings/holidays";

export const useAttendanceExemptions = () => {
  const { data, isLoading, error, mutate } = useSWR<THolidaysResponse>(
    "/api/attendance/exemptions",
    fetcher,
  );

  return {
    holidays: data?.data || [],
    isLoading: isLoading || (!error && !data),
    error,
    mutate,
  };
};
