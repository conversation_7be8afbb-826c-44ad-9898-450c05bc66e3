"use client";

import React from "react";
import { useTranslations } from "next-intl";
import { EmployeeProfileHeader } from "./employee-profile-header";
import { EmployeeProfileTabs } from "./employee-profile-tabs";
import { EmployeeProfileHeaderSkeleton } from "@/app/[locale]/_modules/people/skeletons/employee-profile-header-skeleton";
import {
  TEmployeeAttributes,
  TEmployeeData,
  TUserRoleIncluded,
  TEmployeeResponse,
} from "../../../type/employee";
import { KeyedMutator } from "swr";

type EmployeeProfileLayoutProps = {
  employeeId: string;
  locale: string;
  employee: TEmployeeAttributes | null | undefined;
  employeeData: TEmployeeData | null | undefined;
  isLoading: boolean;
  error: string | null;
  userRoles?: TUserRoleIncluded[];
  mutateEmployee?: KeyedMutator<TEmployeeResponse>;
  children: React.ReactNode;
};

export const EmployeeProfileLayout: React.FC<EmployeeProfileLayoutProps> = ({
  employeeId,
  locale,
  employee,
  employeeData,
  isLoading,
  error,
  userRoles = [],
  mutateEmployee,
  children,
}) => {
  const t = useTranslations();
  return (
    <div className="space-y-6 max-md:mt-6 flex flex-col flex-1">
      {isLoading ? (
        <EmployeeProfileHeaderSkeleton />
      ) : error || !employee ? (
        <div className="flex flex-col gap-6 p-6 bg-white rounded-[20px] border border-gray-200 shadow-sm">
          <h2 className="text-xl font-semibold text-red-500">
            {t("common.Error.title")}
          </h2>
          <p className="text-gray-700">{error || "Employee not found"}</p>
        </div>
      ) : (
        <EmployeeProfileHeader
          employeeId={employeeId}
          employee={employee}
          employeeData={employeeData}
          userRoles={userRoles}
          mutateEmployee={mutateEmployee}
        />
      )}

      {/* Navigation Tabs */}
      <EmployeeProfileTabs employeeId={employeeId} locale={locale} />

      {/* Content Area  */}
      <div className="flex-1 flex flex-col">{children}</div>
    </div>
  );
};
