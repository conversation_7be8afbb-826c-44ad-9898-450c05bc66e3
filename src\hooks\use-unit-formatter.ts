import { useLocale } from "next-intl";
import useMediaQuery from "./use-media-query";
import { Locale } from "@/i18n/routing";

export function useUnitFormatter() {
  const locale: Locale = useLocale() as Locale;
  const isSmallScreen = useMediaQuery("(max-width: 768px)");

  return (value: number, unit: "hour" | "minute" | "second" | "day") => {
    const formatter = new Intl.NumberFormat(locale, {
      style: "unit",
      unit,
      unitDisplay: isSmallScreen ? "short" : "long",
    });

    const parts = formatter.formatToParts(value);
    const numberPart =
      parts.find((part) => part.type === "integer")?.value || "";
    const unitPart = parts.find((part) => part.type === "unit")?.value || "";

    return { numberPart, unitPart };
  };
}
