import { z } from "zod";
import { TFunction } from "@/types";

export const timeValidation = (t: TFunction) =>
  z
    .string()
    .regex(
      /^([01]\d|2[0-3]):([0-5]\d)$/,
      t("common.Error.time.format") || "Invalid time format (HH:MM)",
    );

export const emailValidation = (t: TFunction) =>
  z
    .string()
    .email(t("common.form.email.error.notValid") || "Invalid email format");

export const phoneValidation = (t: TFunction) =>
  z
    .string()
    .min(1, t("common.form.mobile.error.required") || "Phone is required");

export const numberValidation = (t: TFunction) =>
  z.coerce
    .number({
      invalid_type_error:
        t("common.form.number.error.notNumber") || "Must be a number",
    })
    .min(0, t("common.form.number.error.positive") || "Must be positive");

export const stringValidation = (t: TFunction, fieldName: string = "Field") =>
  z.string().min(1, t("common.form.required") || `${fieldName} is required`);

export const optionalStringValidation = () => z.string().optional();

export const booleanValidation = () =>
  z.union([z.boolean(), z.string()]).transform((val) => {
    if (typeof val === "boolean") return val;
    return val === "true";
  });

export const urlValidation = (t: TFunction) =>
  z
    .string()
    .url(t("common.form.url.error.notValid") || "Invalid URL format")
    .optional()
    .or(z.literal(""));

export const positiveNumberValidation = (t: TFunction) =>
  z.coerce
    .number({
      invalid_type_error:
        t("common.form.number.error.notNumber") || "Must be a number",
    })
    .positive(
      t("common.form.number.error.positive") || "Must be a positive number",
    );

export const integerValidation = (t: TFunction) =>
  z.coerce
    .number({
      invalid_type_error:
        t("common.form.number.error.notNumber") || "Must be a number",
    })
    .int(t("common.form.number.error.integer") || "Must be an integer")
    .min(0, t("common.form.number.error.positive") || "Must be positive");

export const decimalValidation = (t: TFunction, decimalPlaces: number = 2) =>
  z.coerce
    .number({
      invalid_type_error:
        t("common.form.number.error.notNumber") || "Must be a number",
    })
    .refine(
      (val) => {
        const str = val.toString();
        const decimalIndex = str.indexOf(".");
        return (
          decimalIndex === -1 || str.length - decimalIndex - 1 <= decimalPlaces
        );
      },
      {
        message:
          t("common.form.number.error.decimal") ||
          `Must have at most ${decimalPlaces} decimal places`,
      },
    );

export const textareaValidation = (t: TFunction, maxLength: number = 1000) =>
  z
    .string()
    .max(
      maxLength,
      t("common.form.textarea.error.maxLength") ||
        `Must be less than ${maxLength} characters`,
    )
    .optional();

export const fileValidation = (t: TFunction, allowedTypes: string[] = []) =>
  z
    .any()
    .refine(
      (file) => !file || file instanceof File,
      t("common.form.file.error.invalid") || "Invalid file",
    )
    .refine(
      (file) =>
        !file || allowedTypes.length === 0 || allowedTypes.includes(file.type),
      t("common.form.file.error.type") || "Invalid file type",
    )
    .optional();

export const arrayValidation = (itemValidation?: z.ZodTypeAny) =>
  z.array(itemValidation || z.string()).optional();

export const stringArrayValidation = () => z.array(z.string()).optional();
