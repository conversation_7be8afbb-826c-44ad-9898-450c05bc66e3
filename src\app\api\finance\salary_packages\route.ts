import { NextRequest, NextResponse } from "next/server";
import { peopleService } from "@/services/api/people";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "10", 10);
    const sortedBy = searchParams.get("sort") || "start_date";

    // Extract employee ID from the filter parameter
    const employeeId = searchParams.get("filter[employee_id_eq]") || "";

    if (!employeeId) {
      return NextResponse.json(
        { error: "Missing employee ID filter" },
        { status: 400 },
      );
    }

    try {
      const response = await peopleService.getSalaryPackageHistory(
        employeeId,
        page,
        limit,
        sortedBy,
      );

      // Return the response directly without wrapping it
      return NextResponse.json(response);
    } catch (error) {
      return NextResponse.json(
        { error: "Unknown error occurred" },
        { status: 500 },
      );
    }
  } catch (error) {
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
