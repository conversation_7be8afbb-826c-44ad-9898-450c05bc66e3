{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "allowSyntheticDefaultImports": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@types": ["./src/types/*"], "@components/*": ["./src/components/*"], "@lib/*": ["./src/lib/*"], "@hooks/*": ["./src/hooks/*"], "@utils/*": ["./src/lib/utils/*"], "@enums/*": ["./src/enums/*"], "@services/*": ["./src/services/*"], "@schemas/*": ["./src/schemas/*"], "@server/*": ["./src/server/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}