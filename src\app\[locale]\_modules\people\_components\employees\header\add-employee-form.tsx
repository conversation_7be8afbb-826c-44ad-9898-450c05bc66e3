"use client";

import React, {
  useRef,
  startTransition,
  useState,
  useEffect,
  useMemo,
  useCallback,
} from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useActionState } from "react";
import { useTranslations } from "next-intl";

import { Loader } from "lucide-react";
import { PAGES } from "@/enums";
import { ActionState, TinputField } from "@/types";
import {
  EmployeeSchemaType,
  employeeSchema,
} from "../../../schemas/employeeSchema";
import useFormFields from "@/hooks/useFormFields";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import FormFieldRenderer from "@/components/auth/fields/form-field-renderer";
import { saveEmployee } from "../../../actions/employee-actions";
import { TEmployee } from "../../../type/employee";
import { useToastMessage } from "@/hooks/use-toast-message";
import { useEmployees } from "../../../hooks/employees/useEmployees";
import { useSearchParams } from "next/navigation";
import { Department } from "../../../constants";
import { useUser } from "@/contexts/user-provider";
import { useRoles } from "@/hooks/useRoles";

type AddEmployeeFormProps = {
  mode?: "add" | "edit";
  employee?: TEmployee | null;
  employeeId?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
};

export default function AddEmployeeForm({
  mode = "add",
  employee = null,
  employeeId,
  onSuccess,
  onCancel,
}: AddEmployeeFormProps = {}) {
  const t = useTranslations();
  const formRef = useRef<HTMLFormElement | null>(null);
  const [selectKey, setSelectKey] = useState<number>(0);
  const { getFormFields } = useFormFields({ formType: PAGES.ADDEMPLOYEE, t });
  const { showToast } = useToastMessage();
  const searchParams = useSearchParams();
  const page = searchParams.get("page") || "1";
  const limit = searchParams.get("limit") || "3";
  const { mutate: mutateEmployeesList } = useEmployees(
    Number(page),
    Number(limit),
  );
  const { mutateUser } = useUser();
  const { roles } = useRoles();

  // Create getScopeByRoleId function for schema validation
  const getScopeByRoleId = useCallback(
    (roleId: string) =>
      roles.find((r) => r.id.toString() === roleId)?.attributes.scope,
    [roles],
  );

  const initialState: ActionState<TEmployee> = {
    error: "",
    success: "",
    issues: [],
    data: null,
  };

  // Create bound action for both add and edit modes
  const boundAction = saveEmployee.bind(
    null,
    mode === "edit" ? employeeId || null : null,
  );

  const [state, submitAction, isPending] = useActionState(
    boundAction,
    initialState,
  );

  const defaultFormValues = useMemo(
    () => ({
      name: mode === "edit" && employee ? employee.name : "",
      email: mode === "edit" && employee ? employee.email : "",
      phone: mode === "edit" && employee ? employee.phone_intl || "" : "",
      department:
        mode === "edit" && employee
          ? (employee.department as Department)
          : "hr",
      start_date:
        mode === "edit" && employee
          ? new Date(employee.start_date)
          : new Date(),
      assignments: [],
      attachments: [],
      avatar:
        mode === "edit" && employee
          ? employee.avatar_url || undefined
          : undefined,
    }),
    [mode, employee],
  );

  const form = useForm<EmployeeSchemaType>({
    resolver: zodResolver(employeeSchema(t, getScopeByRoleId)),
    defaultValues: defaultFormValues,
    mode: "all",
  });

  useEffect(() => {
    if (state?.success) {
      showToast("success", state.success);

      if (mode === "add") {
        form.reset(defaultFormValues);
        setTimeout(() => {
          setSelectKey((prev) => prev + 1);
        }, 0);
      }
      mutateEmployeesList();

      if (mode === "edit") {
        mutateUser();
      }

      if (onSuccess) {
        onSuccess();
      }
    }
  }, [state, form, defaultFormValues, mutateEmployeesList, mode, onSuccess]);

  useEffect(() => {
    if (state?.error) {
      showToast("error", state.issues![0] || state.error);
    }
  }, [state]);

  const handleSubmit = (data: EmployeeSchemaType) => {
    const formData = new FormData();
    formData.append("name", data.name);
    formData.append("email", data.email);
    formData.append("phone", data.phone);
    formData.append("department", data.department);

    if (data.start_date) {
      // Just send the date as a string - the schema will handle conversion
      formData.append("start_date", data.start_date.toISOString());
    }
    if (data.avatar instanceof File) {
      formData.append("avatar", data.avatar);
    }

    // Handle assignments using user_roles_list format
    if (data.assignments && data.assignments.length > 0) {
      const assignmentWithDefault =
        data.assignments.length === 1
          ? data.assignments.map((assignment) => ({
              ...assignment,
              default: true,
            }))
          : data.assignments;

      assignmentWithDefault.forEach((assignment, index) => {
        // Append role_id and project_id separately using the user_roles_list format
        formData.append(
          `employee[user_roles_list][${index}][role_id]`,
          assignment.role,
        );
        formData.append(
          `employee[user_roles_list][${index}][project_id]`,
          assignment.project || "",
        );
        formData.append(
          `employee[user_roles_list][${index}][is_default]`,
          assignment.default ? "true" : "false",
        );
      });
    }

    if (data.attachments && data.attachments.length > 0) {
      data.attachments.forEach((file) => {
        formData.append(`attachments[]`, file);
      });
    }
    startTransition(() => {
      submitAction(formData);
    });
  };

  return (
    <div className="bg-white rounded-lg max-w-2xl mx-auto w-[99.5%]">
      <Form {...form}>
        <form
          ref={formRef}
          action={submitAction}
          onSubmit={form.handleSubmit(handleSubmit)}
          className="space-y-6"
        >
          {(getFormFields() as TinputField<EmployeeSchemaType>[])
            .filter((fieldConfig) => {
              // In edit mode, only show avatar, name, email, and phone fields
              if (mode === "edit") {
                return ["avatar", "name", "email", "phone"].includes(
                  fieldConfig.name as string,
                );
              }
              return true; // Show all fields in add mode
            })
            .map((fieldConfig) => (
              <FormFieldRenderer
                key={`${fieldConfig.name?.toString()}-${
                  fieldConfig.type === "select" ? selectKey : ""
                }`}
                fieldConfig={fieldConfig}
                form={form}
                isPending={isPending}
              />
            ))}

          <div className="sticky border-t border-t-slate-200 pt-2.5 bottom-0 left-0 w-[100%] rounded-t-[18px] bg-white flex flex-row items-center justify-between gap-4 sm:gap-6">
            <Button
              disabled={isPending || !form.formState.isValid}
              type="submit"
              className="px-4 py-2 w-full h-12 sm:max-w-[244px] rounded-lg"
            >
              {isPending ? (
                <Loader className="animate-spin" />
              ) : mode === "edit" ? (
                t("common.buttonText.update")
              ) : (
                t("common.buttonText.add")
              )}
            </Button>
            <Button
              variant={"outline"}
              type="button"
              className="w-full h-12 sm:max-w-[244px] rounded-lg"
              onClick={() => {
                if (mode === "edit" && onCancel) {
                  onCancel();
                } else {
                  form.reset(defaultFormValues);
                  // Force a re-render to ensure all fields are cleared
                  setTimeout(() => {
                    setSelectKey((prev) => prev + 1);
                  }, 0);
                }
              }}
            >
              {t("common.buttonText.cancel2")}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
