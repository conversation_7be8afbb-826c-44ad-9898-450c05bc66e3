name: <PERSON><PERSON> and Push Frontend Docker Image

on:
  push:
    branches: [ develop ]
  release:
    types: [ published ]
  workflow_dispatch:
    inputs:
      ref:
        description: 'Tag or branch ref to use for image tag'
        required: false
        default: 'latest'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Check out Repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Docker Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ vars.REGISTRY_HOST }}
          username: ${{ vars.REGISTRY_USERNAME }}
          password: ${{ secrets.REGISTRY_PASSWORD }}

      - name: Determine Image Tag
        id: image_tag
        run: |
          if [ "${GITHUB_EVENT_NAME}" = "release" ]; then
            # If this was triggered by a release, use the release tag
            tag=${GITHUB_REF#refs/tags/}
          elif [ "${GITHUB_EVENT_NAME}" = "push" ] && [ "${GITHUB_REF}" = "refs/heads/develop" ]; then
            tag="develop"
          else
            # Otherwise, use the manually provided ref (defaults to 'latest')
            tag="${{ github.event.inputs.ref }}"
          fi
          echo "Image tag is: $tag"
          echo "tag=$tag" >> $GITHUB_OUTPUT

      - name: Determine Dockerfile
        id: dockerfile
        run: |
          if [ "${GITHUB_REF}" = "refs/heads/develop" ]; then
            dockerfile="./infra/staging.dockerfile"
          else
            dockerfile="./infra/production.dockerfile"
          fi
          echo "dockerfile=$dockerfile" >> $GITHUB_OUTPUT

      - name: Build and Push Docker Image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ${{ steps.dockerfile.outputs.dockerfile }}
          push: true
          tags: ${{ vars.REGISTRY_HOST }}/${{ vars.REGISTRY_USERNAME }}/frontend:${{ steps.image_tag.outputs.tag }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Trigger QA Deployment
        if: github.event_name == 'push' && github.ref == 'refs/heads/develop'
        uses: peter-evans/repository-dispatch@v3
        with:
          token: ${{ secrets.ATHAR_PAT }}
          repository: athar-association/athar-ems
          event-type: qa-deploy-trigger
          client-payload: '{"service": "frontend", "tag": "${{ steps.image_tag.outputs.tag }}"}'
