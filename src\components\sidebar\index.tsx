"use client";

import React, { useState } from "react";
import { But<PERSON> } from "../ui/button";
import { TbSettings2 } from "react-icons/tb";
import { SidebarData } from "@/types";
import NavLinks from "./nav-links";
import { cn } from "@/lib/utils";
import useMediaQuery from "@/hooks/use-media-query";
import { useTranslations } from "next-intl";
import { iconMap } from "@/constants";
import { useSettingsModal } from "@/contexts/settings-modal-context";
import dynamic from "next/dynamic";

import LoaderPortal from "../loader/loader-portal";
import SystemSelector from "../system/system-selector";

const SelectSystemModal = dynamic(() => import("./select-system-modal"), {
  ssr: false,
  loading: () => <LoaderPortal overlayColor="#000" overlayOpacity={0.6} />,
});
const Sidebar = ({ data }: { data: SidebarData }) => {
  const [openSelectModal, setOpenSelectModal] = useState(false);
  const [collapse, setCollapse] = useState(false);
  const isMediumScreen = useMediaQuery("(max-width: 1024px)");
  const t = useTranslations();
  const { openSettings } = useSettingsModal();
  // If it's a medium screen, force collapse; otherwise, rely on manual toggle
  const effectiveCollapse = isMediumScreen ? true : collapse;

  return (
    <aside
      className={cn(
        "bg-background-v2 sticky top-11 max-h-[91vh] px-4 transition-all max-md:hidden",
        {
          "w-[90px] mx-auto justify-center flex flex-col items-center max-w-[90px]":
            effectiveCollapse,
          "w-[250px] max-w-[250px]": !effectiveCollapse,
        },
      )}
    >
      <Button
        onClick={() => setCollapse((prev) => !prev)}
        variant={"ghost"}
        aria-label="resize-sidebar"
        className={cn(
          "absolute end-0 h-full w-2 p-0 bg-transparent cursor-w-resize focus-visible:ring-0",
          { "cursor-default ": isMediumScreen },
        )}
      />
      <div className="flex flex-col h-full overflow-auto">
        <header
          className={cn("flex items-center gap-2 mb-5 ms-2", {
            "justify-center ms-0": effectiveCollapse,
          })}
        >
          {data.icon &&
            iconMap[data.icon] &&
            React.createElement(iconMap[data.icon], {
              className: "w-6 h-6",
            })}
          {!effectiveCollapse && (
            <h2 className="font-bold text-[20px]">{data.head}</h2>
          )}
        </header>
        <nav className="flex-1">
          <ul className="flex flex-col gap-2">
            <NavLinks collapse={effectiveCollapse} items={data.items || []} />
          </ul>
        </nav>
        <div
          className={cn("flex flex-col justify-start gap-5", {
            "mb-2": effectiveCollapse,
          })}
        >
          <Button
            onClick={() => openSettings("GENERAL", "EDIT_PROFILE")}
            variant={"ghost"}
            className="text-start justify-start text-neutral-500"
          >
            <TbSettings2
              size={100}
              width={24}
              height={24}
              className="!w-6 !h-6"
            />
            {!effectiveCollapse && <span>{t("common.sidebar.settings")}</span>}
          </Button>
          <div className="flex items-center justify-center">
            <SystemSelector
              onClick={() => setOpenSelectModal((prev) => !prev)}
              compact={effectiveCollapse}
              showIcon={true}
            />
          </div>
          {openSelectModal && (
            <SelectSystemModal
              openModal={openSelectModal}
              setOpenModal={setOpenSelectModal}
            />
          )}
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
