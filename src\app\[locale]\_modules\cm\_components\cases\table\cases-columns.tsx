"use client";

import { Checkbox } from "@/components/ui/checkbox";
import { ColumnDef, TableMeta } from "@tanstack/react-table";
import { Locale } from "@/i18n/routing";
import { formatDate } from "@/lib/dateFormatter";
import { TFunction } from "@/types";
import { CasesActions } from "./cases-actions";
import ApprovalWorkFlow from "@/app/[locale]/_modules/people/_components/approval-workflow";
import { TCaseAttributes } from "@/types/cm";

interface TableMetaWithTranslation
  extends TableMeta<{ id: string; type: string; attributes: TCaseAttributes }> {
  t: TFunction;
  locale?: Locale;
  includedData?: any[]; // For approval workflow
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const getMeta = (table: any) => table.options.meta as TableMetaWithTranslation;

export const columns: ColumnDef<{
  id: string;
  type: string;
  attributes: TCaseAttributes;
}>[] = [
  {
    id: "select",
    enableSorting: false,
    enableHiding: false,
    enableColumnFilter: false,
    header: ({ table }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#ACB5BB] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) =>
          table.toggleAllPageRowsSelected(Boolean(value))
        }
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#1A1C1E] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(Boolean(value))}
        aria-label="Select row"
      />
    ),
  },

  // رقم الحالة (Case Number) - Using ID from API
  {
    id: "caseNumber",
    accessorKey: "id",
    enableSorting: false,
    enableHiding: false,
    enableColumnFilter: false,
    header: ({ table }) =>
      getMeta(table).t("cm.cases.table.columns.caseNumber"),
    cell: ({ row }) => {
      const caseData = row.original;
      return <p className="text-sm font-semibold text-black">{caseData.id}</p>;
    },
  },

  // اسم الحالة (Case Name) - Using user_id from API
  {
    id: "caseName",
    accessorKey: "user_id",
    enableColumnFilter: true,
    meta: {
      filterType: "text",
      filterVariant: "text",
    },
    header: ({ table }) => (
      <div className="text-start">
        {getMeta(table).t("cm.cases.table.columns.caseName")}
      </div>
    ),
    cell: ({ row }) => {
      const caseData = row.original;
      return (
        <p className="text-sm font-semibold text-black text-start">
          {caseData.attributes.beneficiary_name}
        </p>
      );
    },
  },

  // العمر (Age) - No data available in API, show empty
  {
    id: "age",
    enableColumnFilter: false,
    header: ({ table }) => (
      <div className="text-center">
        {getMeta(table).t("cm.cases.table.columns.age")}
      </div>
    ),
    cell: ({ row }) => {
      const caseData = row.original;
      return (
        <p className="text-sm text-gray-400 text-center">
          {caseData.attributes.beneficiary_age}
        </p>
      );
    },
  },

  // الجنس (Gender) - No data available in API, show empty
  {
    id: "gender",
    enableColumnFilter: false,
    header: ({ table }) => (
      <div className="text-center">
        {getMeta(table).t("cm.cases.table.columns.gender")}
      </div>
    ),
    cell: ({ row }) => {
      const caseData = row.original;
      return (
        <p className="text-sm text-gray-400 text-center">
          {caseData.attributes.beneficiary_gender}
        </p>
      );
    },
  },

  // تاريخ التسجيل (Registration Date) - Using start_date from API
  {
    id: "registrationDate",
    accessorKey: "start_date",
    enableColumnFilter: true,
    meta: {
      filterType: "date",
      filterVariant: "date",
    },
    header: ({ table }) => (
      <div className="text-center">
        {getMeta(table).t("cm.cases.table.columns.registrationDate")}
      </div>
    ),
    cell: ({ row, table }) => {
      const caseData = row.original;
      const { locale } = getMeta(table);
      return (
        <p className="text-sm text-gray-700 text-center">
          {formatDate(caseData.attributes.created_at, locale ?? "ar")}
        </p>
      );
    },
  },

  {
    id: "status",
    accessorKey: "status",
    enableColumnFilter: true,
    meta: {
      filtertype: "text",
      filterVariant: "text",
    },
    header: ({ table }) => (
      <div className="text-center">
        {getMeta(table).t("cm.cases.table.columns.status")}
      </div>
    ),
    cell: ({ row, table }) => {
      const caseData = row.original;
      const { includedData } = getMeta(table);

      // If case has approval workflow, show it
      return (
        <div
          className="flex justify-center"
          onClick={(e) => e.stopPropagation()}
        >
          <ApprovalWorkFlow
            item={caseData as any}
            included={includedData || []}
          />
        </div>
      );
    },
  },

  // الإجراء (Actions)
  {
    id: "actions",
    enableSorting: false,
    enableHiding: false,
    enableColumnFilter: false,
    header: ({ table }) => (
      <div className="text-center">
        {getMeta(table).t("cm.cases.table.columns.actions")}
      </div>
    ),
    cell: ({ row }) => {
      const caseData = row.original;
      return <CasesActions caseId={caseData.id} />;
    },
  },
];
