"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
} from "react";
import { Permission } from "@/types/auth";
import { useUserPermission } from "@/app/[locale]/_modules/people/hooks/user/useUserPermission";
import {
  saveDataToSessionStorage,
  getDataFromSessionStorage,
} from "@/lib/local-storage";
import { useSystem } from "./system-provider";

interface PermissionContextType {
  hasPermission: (permission: string) => boolean;
  permissions: Permission[];
  isLoading: boolean;
  error: Error | null;
  refetch: () => void;
  clearPermissions: () => void;
}

const PermissionContext = createContext<PermissionContextType | null>(null);

export const PermissionProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { currSystem } = useSystem();
  const {
    permission: apiPermissions,
    isLoading,
    error,
    mutate,
  } = useUserPermission(currSystem);

  // Fetch core permissions when on people system (for Projects feature)
  const {
    permission: corePermissions,
    isLoading: coreIsLoading,
    error: coreError,
    mutate: coreMutate,
  } = useUserPermission(currSystem === "people" ? "core" : undefined);

  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [isPermissionsLoaded, setIsPermissionsLoaded] = useState(false);
  // Create system-specific storage key
  const STORAGE_KEY = `user_permissions_${currSystem}`;
  const CORE_STORAGE_KEY = `user_permissions_core`;

  // Clear permissions when system changes
  useEffect(() => {
    setPermissions([]);
    setIsPermissionsLoaded(false);
    // Clear all system-specific permission storage when system changes
    if (typeof window !== "undefined") {
      const systems = ["people", "procure", "cm", "core"];
      systems.forEach((system) => {
        if (system !== currSystem) {
          sessionStorage.removeItem(`user_permissions_${system}`);
        }
      });
    }
  }, [currSystem]);

  // Load permissions from session storage for current system
  useEffect(() => {
    console.log("🔍 PERMISSION CONTEXT - Loading from storage:");
    console.log("  currSystem:", currSystem);
    console.log("  STORAGE_KEY:", STORAGE_KEY);

    if (currSystem && currSystem !== "core") {
      const storedPermissions =
        getDataFromSessionStorage<Permission[]>(STORAGE_KEY);
      const storedCorePermissions =
        getDataFromSessionStorage<Permission[]>(CORE_STORAGE_KEY);

      console.log("  storedPermissions:", storedPermissions);
      console.log("  storedCorePermissions:", storedCorePermissions);

      // Merge stored permissions
      const mergedStoredPermissions: Permission[] = [];
      if (storedPermissions && storedPermissions.length > 0) {
        mergedStoredPermissions.push(...storedPermissions);
      }
      if (storedCorePermissions && storedCorePermissions.length > 0) {
        storedCorePermissions.forEach((corePerm: Permission) => {
          if (!mergedStoredPermissions.find(p => p.id === corePerm.id)) {
            mergedStoredPermissions.push(corePerm);
          }
        });
      }

      if (mergedStoredPermissions.length > 0) {
        console.log("  ✅ Loading merged permissions from storage:", mergedStoredPermissions.length, "permissions");
        setPermissions(mergedStoredPermissions);
        setIsPermissionsLoaded(true);
      } else {
        console.log("  ❌ No stored permissions found");
      }
    } else {
      console.log("  ⚠️ Skipping storage load - currSystem is core or undefined");
    }
  }, [currSystem, STORAGE_KEY, CORE_STORAGE_KEY]);

  // Clear permissions from both state and session storage
  const clearPermissions = useCallback(() => {
    setPermissions([]);
    setIsPermissionsLoaded(false);
    if (typeof window !== "undefined") {
      sessionStorage.removeItem(STORAGE_KEY);
      sessionStorage.removeItem(CORE_STORAGE_KEY);
    }
  }, [STORAGE_KEY, CORE_STORAGE_KEY]);

  // Update permissions when API data changes - merge system and core permissions
  useEffect(() => {
    console.log("🔍 PERMISSION CONTEXT - API permissions update:");
    console.log("  apiPermissions:", apiPermissions);
    console.log("  apiPermissions length:", apiPermissions?.length || 0);
    console.log("  corePermissions:", corePermissions);
    console.log("  corePermissions length:", corePermissions?.length || 0);
    console.log("  isLoading:", isLoading);
    console.log("  coreIsLoading:", coreIsLoading);

    // Merge system permissions and core permissions
    const mergedPermissions: Permission[] = [];

    if (apiPermissions && apiPermissions.length > 0) {
      mergedPermissions.push(...apiPermissions);
    }

    if (corePermissions && corePermissions.length > 0) {
      // Add core permissions that don't already exist
      corePermissions.forEach((corePerm: Permission) => {
        if (!mergedPermissions.find(p => p.id === corePerm.id)) {
          mergedPermissions.push(corePerm);
        }
      });
    }

    if (mergedPermissions.length > 0) {
      console.log("  ✅ Saving merged permissions to storage and state");
      console.log("  projectPermissions from merged:", mergedPermissions.filter(p => p.id.includes("project")).map(p => p.id));
      console.log("  salaryPermissions from merged:", mergedPermissions.filter(p => p.id.includes("salary")).map(p => p.id));

      // Save system permissions and core permissions separately
      if (apiPermissions && apiPermissions.length > 0) {
        saveDataToSessionStorage(STORAGE_KEY, apiPermissions);
      }
      if (corePermissions && corePermissions.length > 0) {
        saveDataToSessionStorage(CORE_STORAGE_KEY, corePermissions);
      }

      setPermissions(mergedPermissions);
      setIsPermissionsLoaded(true);
    } else {
      console.log("  ❌ No permissions to save");
    }
  }, [apiPermissions, corePermissions, STORAGE_KEY, isLoading, coreIsLoading]);

  const hasPermission = useCallback(
    (perm: string) => {
      return permissions.some((p) => p.id === perm);
    },
    [permissions],
  );

  // Refetch both system and core permissions
  const refetchPermissions = useCallback(() => {
    mutate();
    coreMutate();
  }, [mutate, coreMutate]);

  const shouldRenderChildren = isPermissionsLoaded || error || coreError || (currSystem === "core") || (!isLoading && !coreIsLoading && !apiPermissions && !corePermissions);

  return (
    <PermissionContext.Provider
      value={{
        permissions,
        hasPermission,
        isLoading: isLoading || coreIsLoading,
        error: error || coreError,
        refetch: refetchPermissions,
        clearPermissions,
      }}
    >
      {shouldRenderChildren ? children : null}
    </PermissionContext.Provider>
  );
};

export const usePermission = (): PermissionContextType => {
  const ctx = useContext(PermissionContext);
  if (!ctx)
    throw new Error("usePermission must be used within PermissionProvider");
  return ctx;
};
