"use client";

import React, { useState } from "react";
import { DataTable } from "@/components/table";
import { columns } from "./cases-columns";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";
import { Locale, useRouter } from "@/i18n/routing";
import { RowSelectionState } from "@tanstack/react-table";
import { useTableRegistration } from "@/hooks/useTableRegistration";
import { useCases } from "../../../hooks/useCases";
import { PaginationWithLinks } from "@/components/pagination-with-links";

type CasesTableProps = {
  searchParams: {
    page?: string;
    limit?: string;
    search?: string;
    sort?: string;
    [key: string]: string | undefined;
  };
  showPagination?: boolean;
};

const CasesTable: React.FC<CasesTableProps> = ({
  searchParams,
  showPagination = false,
}) => {
  const t = useTranslations();
  const locale = useLocale() as Locale;
  console.log("🚀 ~ CasesTable ~ locale:", locale);
  const router = useRouter();
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  // Extract parameters
  const page = parseInt(searchParams.page || "1", 10);
  const limit = parseInt(searchParams.limit || "5", 10);
  const sort = searchParams.sort || "-id";

  const { cases, pagination, isLoading, error } = useCases({
    page,
    limit,
    sort,
  });
  console.log("🚀 ~ CasesTable ~ cases:", cases);

  // Handle row click navigation
  const handleRowClick = (row: {
    id: string;
    type: string;
    attributes: Record<string, unknown>;
  }) => {
    router.push(`/cm/cases/forms?case_id=${row.id}&mode=edit`);
  };

  useTableRegistration("cases", columns);

  return (
    <>
      <DataTable
        data={cases}
        dataCount={cases.length}
        columns={columns}
        tableContainerClass="text-start"
        title={t("cm.cases.table.title")}
        meta={{
          t,
          locale,
          includedData: [], // Mock included data for approval workflow
        }}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        translationPrefix="cm.cases.table"
        tableId="cases"
        isLoading={isLoading}
        error={error}
        initialLimit={5}
        onRowClick={handleRowClick}
        exportConfig={{
          entity: "cases",
          defaultSelectedColumns: ["id"],
          exportColumns: [
            { id: "id", label: "Case ID", category: "Basic" },
            {
              id: "user_id",
              label: "User ID",
              required: true,
              category: "Basic Info",
            },
            {
              id: "start_date",
              label: "Start Date",
              required: true,
              category: "Dates",
            },
            {
              id: "status",
              label: "Status",
              category: "Status",
            },
            {
              id: "department",
              label: "Department",
              category: "Department Info",
            },
            {
              id: "department_name",
              label: "Department Name",
              category: "Department Info",
            },
          ],
        }}
      />
      {showPagination && (
        <div className="w-full pt-[18px]">
          <PaginationWithLinks
            page={pagination?.page ?? Number(page)}
            pageSize={Number(limit)}
            totalCount={pagination.count}
            firstLastCounts={{
              firstCount: pagination?.from ?? 1,
              lastCount: pagination?.to ?? Number(limit),
            }}
            pageSizeSelectOptions={{
              pageSizeOptions: [5, 10, 25, 30, 45, 50],
              pageSizeSearchParam: "limit",
            }}
            isLoading={isLoading}
            isDisabled={!cases?.length}
          />
        </div>
      )}
    </>
  );
};

export default CasesTable;
