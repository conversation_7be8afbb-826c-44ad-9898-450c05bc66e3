"use client";

import useS<PERSON> from "swr";
import { fetcher } from "@/services/fetcher";
import { TRolesResponse } from "@/types/core/role";

export const useRoles = (sort: string = "-id") => {
  const { data, error, isLoading, mutate } = useSWR<TRolesResponse>(
    `/api/roles?sort=${sort}`,
    fetcher,
  );

  return {
    roles: data?.data || [],
    isLoading,
    isError: error,
    mutate,
  };
};
