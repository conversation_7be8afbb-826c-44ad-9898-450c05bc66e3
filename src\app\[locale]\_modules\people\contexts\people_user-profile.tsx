"use client";

import {
  isTokenExpired,
  notifyTokenExpired,
} from "@/lib/token-expiration-manager";
import { fetcher } from "@/services/fetcher";
import { TUser } from "@/types/auth";
import { isTokenExpirationError } from "@/utils/auth";
import { createContext, useContext, ReactNode } from "react";
import useSWR, { KeyedMutator, SWRResponse } from "swr";

// Define props for the provider
interface PeopleUserProviderProps {
  children: ReactNode;
  initialUser?: TUser | null;
}

// Define the context value interface
interface UserContextType {
  systemUser: TUser | null;
  error: Error | null;
  isLoading: boolean | null;
  mutate: KeyedMutator<TUser | null>;
}

// Create the context with a default value of undefined
const UserContext = createContext<UserContextType>({
  systemUser: null,
  error: null,
  isLoading: false,
  mutate: async () => null,
});

// PeopleUserProvider component
export function PeopleUserProvider({ children }: PeopleUserProviderProps) {
  const {
    data: swrUser,
    error: swrError,
    isLoading,
    mutate,
  }: SWRResponse<TUser | null, Error> = useSWR<TUser | null, Error>(
    "/api/user",
    fetcher,
    {
      revalidateOnMount: true,
      onErrorRetry: (error: any) => {
        if (error.status === 401 || isTokenExpirationError(error)) {
          if (!isTokenExpired()) {
            notifyTokenExpired();
          }
          return false;
        }
        // Don't retry on 404
        if (error.status === 404) return;
      },
    },
  );

  console.log(swrUser);

  return (
    <UserContext.Provider
      value={{
        systemUser: swrUser || null,
        error: swrError || null,
        mutate,
        isLoading,
      }}
    >
      {children}
    </UserContext.Provider>
  );
}

// Custom hook for consuming the context
export function usePeopleUser(): UserContextType {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a PeopleUserProvider");
  }
  return context;
}
