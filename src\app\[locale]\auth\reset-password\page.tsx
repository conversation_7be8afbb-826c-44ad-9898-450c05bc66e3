import CardWrapper from "@/components/auth/card-wrapper";
import React from "react";
import ResetPassForm from "../../_components/auth/reset-password-form";
import { getTranslations } from "next-intl/server";

const ResetPassword = async ({
  searchParams,
}: {
  searchParams: Promise<{ status: string }>;
}) => {
  const t = await getTranslations("auth");
  const isStatusSuccess = (await searchParams).status === "success" || null;
  return (
    <CardWrapper
      headerTitle={
        isStatusSuccess ? t("passwordUpdated.title") : t("resetPass.title")
      }
      headerLabel={
        isStatusSuccess
          ? t("passwordUpdated.description")
          : t("resetPass.description")
      }
    >
      <ResetPassForm />
    </CardWrapper>
  );
};

export default ResetPassword;
