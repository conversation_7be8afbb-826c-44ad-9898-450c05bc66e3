import useSWR from "swr";
import { fetcher } from "@/services/fetcher";
import { TCasesResponse } from "@/types/cm";
import { useSearchParams } from "next/navigation";
import { useFilterParams } from "@/hooks/filters/useFilterParams";
import { useApiUrl } from "@/hooks/useApiUrl";

type UseCasesParams = {
  page?: number;
  limit?: number;
  sort?: string;
};

export const useCases = ({
  page = 1,
  limit = 5,
  sort = "-id",
}: UseCasesParams = {}) => {
  const searchParams = useSearchParams();
  const { filters } = useFilterParams("cases");
  const searchQuery = searchParams.get("search") || "";

  const apiUrl = useApiUrl({
    baseUrl: "/api/cm/cases",
    page: Number(page),
    limit: Number(limit),
    sort: String(sort),
    search: searchQuery || undefined,
    filters,
    tableId: "cases",
  });

  const { data, error, isLoading, mutate } = useSWR<TCasesResponse>(
    apiUrl,
    fetcher,
  );

  return {
    cases: data?.data || [],
    meta: data?.meta,
    pagination: {
      page: data?.meta.pagination.page || page,
      from: data?.meta.pagination.from || 1,
      to: data?.meta.pagination.to || limit,
      count: data?.meta.pagination.count || 0,
    },
    isLoading,
    error,
    mutate,
  };
};
