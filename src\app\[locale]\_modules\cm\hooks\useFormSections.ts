import useSWR from "swr";
import { fetcher } from "@/services/fetcher";
import { FormSectionsResponse } from "@/types/cm";

type UseFormSectionsParams = {
  sort?: string;
  formTemplateId?: string | number;
};

export const useFormSections = ({
  formTemplateId,
}: UseFormSectionsParams = {}) => {
  // Build URL with query parameters
  let apiUrl = "/api/cm/form-sections";
  const params = new URLSearchParams();

  if (formTemplateId) {
    params.append("form_template_id", formTemplateId.toString());
  }

  if (params.toString()) {
    apiUrl += `?${params.toString()}`;
  }

  const { data, error, isLoading, mutate } = useSWR<FormSectionsResponse>(
    formTemplateId ? apiUrl : null, // Only fetch if formTemplateId is provided
    fetcher,
  );
  console.log("🚀 ~ useFormSections ~ data:", data);

  return {
    sections: data?.data || [],
    meta: data?.meta,
    isLoading,
    error,
    mutate,
  };
};
