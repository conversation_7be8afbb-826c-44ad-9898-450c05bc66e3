"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { DocumentUpload } from "../../../../../public/images/icons";
import { useTranslations } from "next-intl";

type UploadButtonProps = {
  onClick: () => void;
  buttonClassName?: string;
  uploadButtonText?: string;
  uploadTranslationKey?: string;
  uploadButtonIcon?: React.ReactNode;
  isPending?: boolean;
  readOnly?: boolean;
  isDisabled?: boolean;
};

export default function UploadButton({
  onClick,
  buttonClassName,
  uploadButtonText,
  uploadTranslationKey = "common.form.attachments.buttons.upload",
  uploadButtonIcon,
  isPending,
  readOnly,
  isDisabled,
}: UploadButtonProps) {
  const t = useTranslations();

  return (
    <div className="flex items-center">
      <Button
        variant="outline"
        type="button"
        className={cn(
          "flex items-center gap-2 h-12 border-dashed border-2",
          buttonClassName,
        )}
        onClick={onClick}
        disabled={isPending || readOnly || isDisabled}
      >
        {uploadButtonIcon || (
          <DocumentUpload className="text-gray-500 !w-6 !h-6" />
        )}
        <span>{uploadButtonText || t(uploadTranslationKey)}</span>
      </Button>
    </div>
  );
}
