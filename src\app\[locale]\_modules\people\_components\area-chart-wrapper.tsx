"use client";

import Reusable<PERSON>rea<PERSON>hart from "@/components/chart/area-chart";
import { ChartSkeleton } from "@/components/skeletons";
import { LANGUAGES, MONTHS } from "@/constants/enum";
import { getMonthTranslations } from "@/constants/translations-mapping";
import useMediaQuery from "@/hooks/use-media-query";
import { TFunction } from "@/types";
import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { Locale } from "@/i18n/routing";

const fakeData = [
  { day: 1, attendance: 32, highlight: false },
  { day: 2, attendance: 29, highlight: false },
  { day: 3, attendance: 28, highlight: false },
  { day: 4, attendance: 25, highlight: false },
  { day: 5, attendance: 29, highlight: false },
  { day: 6, attendance: 32, highlight: false },
  { day: 7, attendance: 31, highlight: false },
  { day: 8, attendance: 35, highlight: true },
  { day: 9, attendance: 45, highlight: false },
  { day: 10, attendance: 51, highlight: false },
  { day: 11, attendance: 40, highlight: false },
  { day: 12, attendance: 39, highlight: false },
  { day: 13, attendance: 40, highlight: false },
  { day: 14, attendance: 41, highlight: false },
  { day: 15, attendance: 38, highlight: false },
  { day: 16, attendance: 42, highlight: false },
  { day: 17, attendance: 47, highlight: false },
  { day: 18, attendance: 44, highlight: false },
  { day: 19, attendance: 46, highlight: false },
  { day: 20, attendance: 50, highlight: false },
  { day: 21, attendance: 48, highlight: false },
  { day: 22, attendance: 45, highlight: false },
  { day: 23, attendance: 49, highlight: false },
  { day: 24, attendance: 52, highlight: false },
  { day: 25, attendance: 51, highlight: false },
  { day: 26, attendance: 47, highlight: false },
  { day: 27, attendance: 43, highlight: false },
  { day: 28, attendance: 40, highlight: false },
  { day: 29, attendance: 42, highlight: false },
  { day: 30, attendance: 39, highlight: false },
];

export default function WavyAreaChart() {
  const isMobile = useMediaQuery("(max-width:767px)");
  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const t = useTranslations() as TFunction;

  const monthOptions = Object.values(MONTHS).map((month) => ({
    value: month,
    label: getMonthTranslations(month, t), // Using the updated function
  }));
  const currMonth = monthOptions[new Date().getMonth()].value;
  const [selectedMonth, setSelectedMonth] = useState(currMonth.toLowerCase());

  const [data, setData] = useState<{}[]>([]);
  useEffect(() => {
    setTimeout(() => {
      setData(fakeData);
    }, 3000);
  }, []);

  if (data.length === 0) {
    return (
      <ChartSkeleton
        showLabel={false}
        skeletonWrapperStyle="h-[380px] max-h-[380px] rounded-[20px]"
      />
    );
  }

  return (
    <ReusableAreaChart
      data={data}
      xKey="day"
      yKey="attendance"
      title={t("people.daily-attendance-chart.daily-attendance-title")}
      titleStyle="leading-[30px] sm:text-[20px] font-medium text-gray-700 leading-6 max-sm:max-w-[187px]"
      chartMargin={{
        top: 24,
        right: isAr ? 10 : 24,
        left: isAr ? 24 : 24,
        bottom: 24,
      }}
      height={isMobile ? 300 : 314}
      width="100%"
      yDomain={[0, 60]}
      yTicks={[0, 10, 20, 30, 40, 50, 60]}
      selectData={{
        selectOptions: monthOptions,
        onSelectChange: setSelectedMonth,
        selectValue: selectedMonth,
      }}
      tooltipFormatter={(value) => [
        `${value}%`,
        t("people.daily-attendance-chart.attendance-percentage"),
      ]}
      primaryColor="hsl(var(--primary))"
      secondaryColor="hsl(var(--secondary))"
    />
  );
}
