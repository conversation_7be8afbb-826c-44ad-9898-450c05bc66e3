import useSWR from "swr";
import { fetcher } from "@/services/fetcher";
import { EmployeeLeavesResponse } from "../../type/employee-leaves";

export const useEmployeeLeaveDetails = (
  employeeId: string,
  page: number,
  limit: number,
) => {
  // Try using the general leaves API with employee filter instead of employee-specific endpoint
  const apiKey = `/api/leaves?page=${page}&limit=${limit}&include=employee,approval_request&sort=-id&filter[employee_id_eq]=${employeeId}`;
  const { data, error, isLoading, mutate } = useSWR<EmployeeLeavesResponse>(
    employeeId ? apiKey : null,
    fetcher,
    {
      dedupingInterval: 2000,
      keepPreviousData: true,
    },
  );

  return {
    leaveDetails: data?.data || [],
    includedData: data?.included || [],
    pagination: data?.meta?.pagination,
    isLoading,
    error,
    mutate,
  };
};
