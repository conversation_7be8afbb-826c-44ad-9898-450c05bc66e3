import React from "react";
import useS<PERSON> from "swr";
import { fetcher } from "@/services/fetcher";
import { FormFieldsResponse } from "@/types/cm";

type UseFormFieldsParams = {
  formSectionId?: string | number;
  caseId?: string | number;
};

export const useFormFields = ({
  formSectionId,
  caseId,
}: UseFormFieldsParams = {}) => {
  // Build URL with query parameters
  let apiUrl = "/api/cm/form-fields";
  const params = new URLSearchParams();

  if (formSectionId) {
    params.append("form_section_id", formSectionId.toString());
  }

  if (caseId) {
    params.append("case_id", caseId.toString());
  }

  if (params.toString()) {
    apiUrl += `?${params.toString()}`;
  }

  const { data, error, isLoading, mutate } = useSWR<FormFieldsResponse>(
    // Only fetch if formSectionId is provided
    formSectionId ? apiUrl : null,
    fetcher,
  );
  console.log("🚀 ~ useFormFields ~ data:", data);

  const processedFields = data?.data.sort(
    (a, b) => a.display_order - b.display_order,
  );
  console.log("🚀 ~ useFormFields ~ processedFields:", processedFields);

  return {
    fields: processedFields,
    meta: data?.meta,
    isLoading,
    error,
    mutate,
  };
};
