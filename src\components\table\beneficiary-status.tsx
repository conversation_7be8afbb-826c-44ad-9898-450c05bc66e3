import { BENEFICIARY_STATUS } from "@/constants/enum";

const statusBackgroundColors = {
  [BENEFICIARY_STATUS.INCOMING]: "bg-success-50",
  [BENEFICIARY_STATUS.PENDING]: "bg-hang-50",
  [BENEFICIARY_STATUS.WAITING]: "bg-wait-50",
  [BENEFICIARY_STATUS.COMPLETED]: "bg-success-50",
};

const statusTextColors = {
  [BENEFICIARY_STATUS.INCOMING]: "text-success",
  [BENEFICIARY_STATUS.PENDING]: "text-hang",
  [BENEFICIARY_STATUS.WAITING]: "text-wait",
  [BENEFICIARY_STATUS.COMPLETED]: "text-success",
};

const BeneficiaryStatus = ({
  status,
  label,
}: {
  status:
    | BENEFICIARY_STATUS.INCOMING
    | BENEFICIARY_STATUS.PENDING
    | BENEFICIARY_STATUS.WAITING
    | BENEFICIARY_STATUS.COMPLETED;
  label: string;
}) => {
  return (
    <div
      className={`min-w-24 max-w-24 mx-auto min-h-[27px] leading-[27px] px-3 rounded-sm text-sm font-normal text-start ${statusBackgroundColors[status]} ${statusTextColors[status]}`}
    >
      {label}
    </div>
  );
};

export default BeneficiaryStatus;
