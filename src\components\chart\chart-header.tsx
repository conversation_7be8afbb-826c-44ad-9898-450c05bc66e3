import { cn } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import type { ChartHeader } from "@/types";

const ChartHeader = ({
  title,
  titleStyle,
  selectPlaceholder,
  selectOptions,
  legendLabels,
  selectValue,
  selectTriggerStyle,
  onSelectChange,
  headerClassName,
}: ChartHeader) =>
  (title || selectOptions || legendLabels) && (
    <div className={headerClassName}>
      <div className="flex flex-row items-center justify-between w-full">
        {title && (
          <h2 className={cn("text-[16px] font-bold", titleStyle)}>{title}</h2>
        )}
        {selectOptions && onSelectChange && (
          <div className="min-w-20 max-h-8">
            <Select value={selectValue} onValueChange={onSelectChange}>
              <SelectTrigger
                className={cn("border-[#E5E6E6]", selectTriggerStyle)}
              >
                <SelectValue
                  className={"h-full"}
                  placeholder={selectPlaceholder ?? "annual"}
                />
              </SelectTrigger>
              <SelectContent className="border-none">
                {selectOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
      </div>
      {legendLabels && (
        <ul className={cn("gap-6 flex justify-end list-none m-0 pe-3 pt-4")}>
          <li className="flex items-center font-semibold text-xs flex-row-reverse gap-[5px]">
            <span>{legendLabels[0]}</span>
            <span className="block w-2 h-2 rounded-[2px] bg-secondary" />
          </li>
          <li className="flex items-center font-semibold text-xs flex-row-reverse gap-[5px]">
            <span>{legendLabels[1]}</span>
            <span className="block w-2 h-2 rounded-[2px] bg-primary" />
          </li>
        </ul>
      )}
    </div>
  );

export default ChartHeader;
