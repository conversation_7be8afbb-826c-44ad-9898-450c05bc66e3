// src/components/sidebar/select-system-modal.tsx
"use client";

import { useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { TFunction } from "@/types";
import SystemSelector from "@/app/[locale]/_components/auth/select-system-button";

const SelectSystemModal = ({
  openModal,
  setOpenModal,
}: {
  openModal: boolean;
  setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const locale = useLocale();
  const t = useTranslations() as TFunction;
  const [isNavigating, setIsNavigating] = useState(false);

  const handleNavigationStart = () => {
    setIsNavigating(true);

    // Close the modal after a delay to allow for navigation
    setTimeout(() => {
      setOpenModal(false);
      setIsNavigating(false);
    }, 3000);
  };

  return (
    <Dialog
      open={openModal}
      onOpenChange={(open) => {
        // Prevent closing the modal during navigation
        if (!isNavigating) {
          setOpenModal(open);
        }
      }}
    >
      <DialogContent
        hideClose={true}
        className="p-6 pb-12 bg-white text-right md:w-[480px] !rounded-[20px] gap-0"
      >
        <DialogHeader className="mb-[18px]">
          <Button
            className="flex justify-start w-4 h-4 mb-8 p-0 focus-visible:ring-0"
            variant="ghost"
            onClick={() => !isNavigating && setOpenModal(false)}
            disabled={isNavigating}
          >
            <X className="!w-6 !h-6 text-[#727A90] stroke-[1.3px]" />
          </Button>

          <div className="text-start !mt-0">
            <DialogTitle className="text-base font-bold mb-2">
              {t("auth.selectSystem.headerTitle")}
            </DialogTitle>

            <DialogDescription className="text-gray-500 text-sm">
              {t("auth.selectSystem.headerLabel")}
            </DialogDescription>
          </div>
        </DialogHeader>

        <SystemSelector
          locale={locale.toString()}
          t={t}
          selectButtonStyle="border-2 border-gray-300 border-dashed hover:border hover:border-solid rounded-[12px]"
          systemTitleStyle="text-black"
        />
      </DialogContent>
    </Dialog>
  );
};

export default SelectSystemModal;
