"use client";

import { Skeleton } from "@/components/ui/skeleton";

export default function AttachmentSkeleton() {
  return (
    <div className="bg-white rounded-2xl border border-gray-200 overflow-hidden max-h-[220px]">
      <div className="p-4 flex flex-col h-full items-center min-h-[220px]">
        <div className="flex justify-start self-start mb-2 w-full">
          <Skeleton className="h-6 w-6 rounded-full bg-gray-200" />
        </div>

        <div className="flex flex-col items-center justify-center w-[100px] h-[100px] rounded-2xl">
          <Skeleton className="h-7 w-7 rounded bg-gray-200" />
        </div>

        <div className="mt-4 pb-2 text-center w-full">
          <Skeleton className="h-5 w-32 mx-auto bg-gray-200 rounded-md" />
          <Skeleton className="h-4 w-20 mx-auto mt-2 bg-gray-200 rounded-md" />
        </div>
      </div>
    </div>
  );
}
