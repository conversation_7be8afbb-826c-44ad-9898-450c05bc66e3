"use server";

import { peopleService } from "@/services/api/people";
import { getCoreSessionToken } from "@/services/api";
import { handleApiError } from "@/utils/api-error-handler";

export async function renameAttachment(
  employeeId: string,
  attachmentId: string,
  newName: string,
) {
  try {
    const coreSessionToken = await getCoreSessionToken();
    if (!coreSessionToken) {
      throw new Error("Unauthorized");
    }

    const response = await peopleService.renameAttachment(
      employeeId,
      attachmentId,
      newName,
    );

    return response;
  } catch (error) {
    const handledResponse = handleApiError(error);
    if (handledResponse) {
      return handledResponse;
    }

    throw error;
  }
}

export async function createAttachment(employeeId: string, formData: FormData) {
  try {
    const coreSessionToken = await getCoreSessionToken();
    if (!coreSessionToken) {
      throw new Error("Unauthorized");
    }

    const response = await peopleService.createAttachment(employeeId, formData);

    return response;
  } catch (error) {
    const handledResponse = handleApiError(error);
    if (handledResponse) {
      return handledResponse;
    }

    throw error;
  }
}

export async function deleteAttachment(
  employeeId: string,
  attachmentId: string,
) {
  try {
    const coreSessionToken = await getCoreSessionToken();
    if (!coreSessionToken) {
      throw new Error("Unauthorized");
    }
    await peopleService.deleteAttachment(employeeId, attachmentId);

    return { success: true };
  } catch (error) {
    const handledResponse = handleApiError(error);
    if (handledResponse) {
      return handledResponse;
    }

    throw error;
  }
}
