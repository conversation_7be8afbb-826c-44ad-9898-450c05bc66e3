"use server";

import { isValidationSuccess, validateFormData } from "@/lib/form-utils";
import { handleError } from "@/lib/utils";
import { ActionState } from "@/types";
import { salarySchema, SalarySchemaType } from "../schemas/salarySchema";

export const updateTotalSalary = async (
  mode: "salary" | "note",
  prevState: ActionState<null>,
  data: FormData,
): Promise<ActionState<null>> => {
  try {
    const validation = await validateFormData<SalarySchemaType>(data, (t) =>
      salarySchema(t, mode),
    );

    if (!isValidationSuccess(validation)) {
      return {
        error: validation.error,
        issues: validation.issues,
      };
    }

    const { totalSalary, note } = validation.data;

    const response = await fetch("/api/data/update-salary", {
      method: "POST",
      body: JSON.stringify({ totalSalary: Number(totalSalary), note }),
      headers: { "Content-Type": "application/json" },
    });

    if (!response.ok) {
      throw new Error("Update failed");
    }

    return {
      success: "updated total salary successfully 😊",
      error: "",
      issues: [],
    };
  } catch (err) {
    return handleError(err, "An error occurred during salary update");
  }
};

// update salary status

export const updateSalaryStatus = async ({
  id,
  status,
}: {
  id: string;
  status: "paid" | "rejected";
}) => {
  const response = await fetch(`/api/data/salaries/${id}`, {
    method: "PUT",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ status }),
  });

  if (!response.ok) {
    throw new Error("Failed to update salary status");
  }

  return response.json();
};
