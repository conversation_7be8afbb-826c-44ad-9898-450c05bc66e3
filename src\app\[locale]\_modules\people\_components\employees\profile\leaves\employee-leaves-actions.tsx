import { useState } from "react";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";
import { CiMenuKebab } from "react-icons/ci";
import { Loader, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { useApprovalPermissions } from "../../../../hooks/useApprovalPermissions";
import { getIncludedItem } from "../../../../utils/included-data/getIncludedItem";
import { LANGUAGES } from "@/constants/enum";
import {
  LeaveDetail,
  TIncludedEmployee,
} from "../../../../type/employee-leaves";
import { ApprovalRequestData } from "../../../../type/approval-request";
import { Row } from "@tanstack/react-table";
import { Locale } from "@/i18n/routing";
import {
  Edit,
  RightCircle,
} from "../../../../../../../../../public/images/icons";
import { UpdateDatesModal } from "@/components/modals/update-dates-modal";

type Props<TData> = {
  row: Row<TData>;
  onWithdrawLeave?: (leaveId: string) => void;
  onUpdateDates?: (
    leaveId: string,
    startDate: Date,
    endDate: Date,
    leaveDuration?: string,
    leaveType?: string,
  ) => void;
  onAcceptLeave?: (leaveId: string) => void;
  onRejectLeave?: (leaveId: string) => void;
  isWithdrawing?: boolean;
  isUpdatingDates?: boolean;
  isAccepting?: boolean;
  isRejecting?: boolean;
  includedData: (TIncludedEmployee | ApprovalRequestData)[];
};

const EmployeeLeavesActions = <TData extends LeaveDetail>({
  row,
  onWithdrawLeave,
  onUpdateDates,
  onAcceptLeave,
  onRejectLeave,
  isWithdrawing,
  isUpdatingDates,
  isAccepting,
  isRejecting,
  includedData,
}: Props<TData>) => {
  const t = useTranslations();
  const locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;

  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);

  const leave = row.original;
  const { id, attributes, relationships } = leave;
  const { start_date, end_date, status, leave_type, leave_duration } =
    attributes;

  const isEndDateValid = end_date ? new Date(end_date) >= new Date() : false;

  const approvalRequestId = relationships?.approval_request?.data?.id;
  const approvalRequest = approvalRequestId
    ? (getIncludedItem(includedData, "approval_request", approvalRequestId) as
        | ApprovalRequestData
        | undefined)
    : undefined;

  const { canAct, hasActed, userAction } =
    useApprovalPermissions(approvalRequest);

  const canWithdraw =
    ["waiting", "pending", "approved"].includes(status) && isEndDateValid;

  const canUpdateDates =
    status === "waiting" || (status === "pending" && isEndDateValid);

  const canAccept = approvalRequest
    ? ["waiting", "pending"].includes(status) && canAct && !hasActed
    : ["waiting", "pending"].includes(status);

  const canReject = canAccept;

  const hasAnyActions =
    canAccept ||
    canReject ||
    canUpdateDates ||
    canWithdraw ||
    (approvalRequest && hasActed);

  const handleUpdateDates = (newStartDate: Date, newEndDate: Date) => {
    onUpdateDates?.(id, newStartDate, newEndDate, leave_duration, leave_type);
    setIsUpdateModalOpen(false);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            disabled={!hasAnyActions}
            variant="outline"
            className="h-8 w-8 p-0"
          >
            <span className="sr-only">Open menu</span>
            <CiMenuKebab />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align={isAr ? "start" : "end"}
          className="w-48 rounded-xl"
        >
          {/* Accept */}
          {(canAccept ||
            (approvalRequest && hasActed && userAction === "approve")) && (
            <DropdownMenuItem
              onClick={() => canAccept && !isAccepting && onAcceptLeave?.(id)}
              disabled={isAccepting || (approvalRequest && hasActed)}
              className="text-gray-500 focus:text-green-600 flex-row-reverse justify-start disabled:opacity-50"
              title={
                hasActed && userAction === "approve"
                  ? "Already Approved"
                  : undefined
              }
            >
              {isAccepting ? (
                <Loader className="animate-spin ms-2 !h-6 !w-6" />
              ) : (
                <RightCircle className="ms-2 !h-6 !w-6" />
              )}
              {hasActed && userAction === "approve" && "✓ "}
              {t("people.leaves-requests-component.actions.approve")}
            </DropdownMenuItem>
          )}

          {/* Reject */}
          {(canReject ||
            (approvalRequest && hasActed && userAction === "reject")) && (
            <DropdownMenuItem
              onClick={() => canReject && !isRejecting && onRejectLeave?.(id)}
              disabled={isRejecting || (approvalRequest && hasActed)}
              className="text-gray-500 focus:text-red-600 flex-row-reverse justify-start disabled:opacity-50"
              title={
                hasActed && userAction === "reject"
                  ? "Already Rejected"
                  : undefined
              }
            >
              {isRejecting ? (
                <Loader className="animate-spin ms-2 !h-6 !w-6" />
              ) : (
                <X className="ms-2 !h-6 !w-6" />
              )}
              {hasActed && userAction === "reject" && "✓ "}
              {t("people.leaves-requests-component.actions.reject")}
            </DropdownMenuItem>
          )}

          {/* Update Dates */}
          {canUpdateDates && (
            <DropdownMenuItem
              onClick={() => !isUpdatingDates && setIsUpdateModalOpen(true)}
              disabled={isUpdatingDates}
              className="text-gray-500 focus:text-blue-600 flex-row-reverse justify-start"
            >
              {isUpdatingDates ? (
                <Loader className="animate-spin ms-2 !h-6 !w-6" />
              ) : (
                <Edit className="ms-2 !h-6 !w-6" />
              )}
              {t(
                "people.employees-page.profile.leaves.update-dates.update-button",
              )}
            </DropdownMenuItem>
          )}

          {/* Withdraw */}
          {canWithdraw && (
            <DropdownMenuItem
              onClick={() => !isWithdrawing && onWithdrawLeave?.(id)}
              disabled={isWithdrawing}
              className="text-gray-600 focus:text-orange-600 flex-row-reverse justify-start"
            >
              {isWithdrawing ? (
                <Loader className="animate-spin ms-2 !h-6 !w-6" />
              ) : (
                <X className="ms-2 !h-6 !w-6" />
              )}
              {t("people.employees-page.profile.leaves.table.actions.withdraw")}
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      <UpdateDatesModal
        isOpen={isUpdateModalOpen}
        onClose={() => setIsUpdateModalOpen(false)}
        onUpdate={handleUpdateDates}
        initialStartDate={start_date}
        initialEndDate={end_date}
        isLoading={!!isUpdatingDates}
        mode="range"
        title={t("people.employees-page.profile.leaves.update-dates.title")}
        description={t(
          "people.employees-page.profile.leaves.update-dates.description",
        )}
        submitButtonText={t(
          "people.employees-page.profile.leaves.update-dates.update-button",
        )}
        startDateLabel={t(
          "people.employees-page.profile.leaves.update-dates.start-date",
        )}
        endDateLabel={t(
          "people.employees-page.profile.leaves.update-dates.end-date",
        )}
        selectedRangeLabel={t(
          "people.employees-page.profile.leaves.update-dates.selected-range",
        )}
        noneLabel={t("people.employees-page.profile.leaves.update-dates.none")}
        allowPastDates={true}
        minDate={(() => {
          // For leaves update mode, allow past dates but not more than 90 days
          const ninetyDaysAgo = new Date();
          ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);
          ninetyDaysAgo.setHours(0, 0, 0, 0);
          return ninetyDaysAgo;
        })()}
      />
    </>
  );
};

export { EmployeeLeavesActions };
