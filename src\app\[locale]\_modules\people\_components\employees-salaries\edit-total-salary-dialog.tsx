"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { DialogTitle } from "@/components/ui/dialog";
import { PAGES } from "@/enums";
import useFormFields from "@/hooks/useFormFields";
import { useToast } from "@/hooks/use-toast";
import ResponsiveDialog from "@/components/responsive-dialog";
import { ActionState, TFunction, TinputField } from "@/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderCircle } from "lucide-react";
import React, {
  startTransition,
  useActionState,
  useEffect,
  useRef,
} from "react";
import { useForm } from "react-hook-form";
import { useLocale, useTranslations } from "next-intl";
import ErrorMessage from "@/components/errorMessage";
import FormFieldRenderer from "@/components/auth/fields/form-field-renderer";
import { updateTotalSalary } from "../../actions/salaries";
import { SalarySchemaType, salarySchema } from "../../schemas/salarySchema";
import { formatNumber } from "@/lib/format-number";
import { Locale } from "@/i18n/routing";

type EditSalaryModalProps = {
  mode: "salary" | "note";
  currentTotalSalary?: string | number;
  currentNote?: string;
  showEditSalaryDialog: boolean;
  setShowEditSalaryDialog: React.Dispatch<React.SetStateAction<boolean>>;
};

export default function EditSalaryModal({
  mode,
  currentTotalSalary,
  currentNote,
  showEditSalaryDialog,
  setShowEditSalaryDialog,
}: EditSalaryModalProps) {
  return (
    <ResponsiveDialog
      open={showEditSalaryDialog}
      onOpenChange={setShowEditSalaryDialog}
      closeBtnStyle="top-[22px]"
      header={<EditTotalSalaryHeader mode={mode} />}
    >
      <EditTotalSalaryContent
        mode={mode}
        currentTotalSalary={currentTotalSalary}
        currentNote={currentNote}
        setShowEditSalaryDialog={setShowEditSalaryDialog}
      />
    </ResponsiveDialog>
  );
}

const EditTotalSalaryHeader = ({ mode }: { mode: "salary" | "note" }) => {
  const t = useTranslations("people");
  return (
    <div className="px-6 py-[22px] border-b">
      <DialogTitle className="!m-0 text-[18px] font-semibold leading-7">
        {mode === "salary"
          ? t("employees-salaries-page.edit-salary-dialog.header.title")
          : t(
              "employees-salaries-page.edit-salary-dialog.content.note-placeholder",
            )}
      </DialogTitle>
    </div>
  );
};

const EditTotalSalaryContent = ({
  mode,
  currentTotalSalary,
  currentNote,
  setShowEditSalaryDialog,
}: Omit<EditSalaryModalProps, "showEditSalaryDialog">) => {
  const t = useTranslations() as TFunction;
  const { toast } = useToast();
  const { getFormFields } = useFormFields({
    formType: PAGES.UPDATETOTALSALARY,
    t,
  });
  const formRef = useRef<HTMLFormElement | null>(null);
  const initialState: ActionState<null> = {
    error: "",
    success: "",
    issues: [],
  };
  const locale: Locale = useLocale() as Locale;

  const editBasedOnMode = updateTotalSalary.bind(null, mode);
  const [state, submitAction, isPending] = useActionState(
    editBasedOnMode,
    initialState,
  );

  const defaultValues = {
    totalSalary: formatNumber(Number(currentTotalSalary), locale),
    note: currentNote || "",
  };

  const form = useForm<SalarySchemaType>({
    resolver: zodResolver(salarySchema(t, mode)),
    defaultValues,
    mode: "all",
  });

  const { isSubmitSuccessful } = form.formState;

  useEffect(() => {
    if (isSubmitSuccessful) {
      form.reset(defaultValues);
    }
  }, [form, form.reset, isSubmitSuccessful, defaultValues]);

  useEffect(() => {
    if (state.success) {
      toast({
        className: "toast-success",
        description: state.success,
      });
      setShowEditSalaryDialog(false);
    }
  }, [state, setShowEditSalaryDialog, toast]);

  const fields = getFormFields() as TinputField<SalarySchemaType>[];
  return (
    <Form {...form}>
      <form
        ref={formRef}
        action={submitAction}
        onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit((data) => {
            const payload = {
              ...data,
              totalSalary: data.totalSalary
                ? Number(data.totalSalary.replace(/,/g, ""))
                : data.totalSalary,
            };

            const formData = new FormData();
            Object.entries(payload).forEach(([key, value]) => {
              formData.append(key, String(value));
            });

            startTransition(async () => {
              await submitAction(formData);
            });
          })(e);
        }}
      >
        {fields.map((fieldConfig) => (
          <FormFieldRenderer
            key={fieldConfig.name}
            fieldConfig={fieldConfig}
            form={form}
            isPending={isPending}
          />
        ))}

        {state?.error && (!state?.issues || state.issues.length === 0) && (
          <ErrorMessage message={state.error} />
        )}
        {Array.isArray(state?.issues) && state?.issues?.length > 0 && (
          <ErrorMessage message={state.issues[0]} />
        )}
        <div className="flex justify-between gap-6 mt-10">
          <Button
            disabled={isPending || !form.formState.isDirty}
            type="submit"
            className="w-full h-12 max-h-12"
          >
            {!isPending ? (
              t("common.buttonText.submit")
            ) : (
              <LoaderCircle className="animate-spin text-white" />
            )}
          </Button>
          <Button
            type="button"
            variant="outline"
            className="w-full h-12 max-h-12"
            disabled={isPending}
            onClick={() => form.reset(defaultValues)}
          >
            {t("common.buttonText.cancel2")}
          </Button>
        </div>
      </form>
    </Form>
  );
};
