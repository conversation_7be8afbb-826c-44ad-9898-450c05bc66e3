import { NextRequest, NextResponse } from "next/server";
import { cmAPI } from "@/services/api/cm";
import { createErrorResponse } from "@/utils/api-error-extractor";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    const search = searchParams.get("search") || "";
    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "10", 10);
    const sort = searchParams.get("sort") || "-id";

    // Build filter string and decode any encoded square brackets
    const filterParts: string[] = [];
    for (const [key, value] of searchParams.entries()) {
      if (key.startsWith("filter[") && !key.startsWith("group[")) {
        // Decode the key to ensure square brackets are not encoded
        const decodedKey = decodeURIComponent(key);
        filterParts.push(`${decodedKey}=${encodeURIComponent(value)}`);
      }
    }

    const filters = filterParts.join("&");

    const response = await cmAPI.getCases(page, limit, sort, search, filters);

    return NextResponse.json(response);
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET Attendacne Exemptions route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
