import useSWR from "swr";
import { fetcher } from "@/services/fetcher";
import { CaseProgress } from "@/types/cm";

type UseCaseProgressParams = {
  caseId?: string;
};

export const useCaseProgress = ({ caseId }: UseCaseProgressParams = {}) => {
  const apiUrl = caseId ? `/api/cm/case-progress?case_id=${caseId}` : null;

  const { data, error, isLoading, mutate } = useSWR<{ data: CaseProgress }>(
    apiUrl,
    fetcher,
  );

  console.log("🚀 ~ useCaseProgress ~ data:", data);

  return {
    progress: data?.data,
    isLoading,
    error,
    mutate,
  };
};
