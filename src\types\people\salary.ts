// Salary-related types for the people module

export type SalaryPackageAttributes = {
  base_salary: string;
  housing_allowance: string;
  transportation_allowance: string;
  other_allowances: string;
  total_package_value: string;
  effective_date: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
};

export type SalaryPackage = {
  id: string;
  type: string;
  attributes: SalaryPackageAttributes;
};

export type SalaryPackageResponse = {
  data: SalaryPackage;
};

// Salary history types
export type SalaryHistoryEntry = {
  id: string;
  attributes: {
    base_salary: string;
    housing_allowance: string;
    transportation_allowance: string;
    other_allowances: string;
    total_package_value: string;
    effective_date: string;
    notes?: string;
  };
};

export type SalaryHistoryResponse = {
  data: SalaryHistoryEntry[];
  meta: {
    pagination: {
      current_page: number;
      total_pages: number;
      total_count: number;
      per_page: number;
    };
  };
};

// Form types
export type SalaryPackageFormData = {
  base_salary: number;
  housing_allowance: number;
  transportation_allowance: number;
  other_allowances: number;
  effective_date: Date;
  notes?: string;
};

export type SalaryUpdateFormData = {
  base_salary?: number;
  housing_allowance?: number;
  transportation_allowance?: number;
  other_allowances?: number;
  notes?: string;
};

// Display types
export type SalaryField = {
  key: keyof SalaryPackageFormData;
  label: string;
  value: number;
  formatOnChange?: boolean;
  formatOnBlur?: boolean;
};

export type SalaryCalculationData = {
  total_package_value: number;
  base_salary: number;
  housing_allowance: number;
  transportation_allowance: number;
  other_allowances: number;
};
