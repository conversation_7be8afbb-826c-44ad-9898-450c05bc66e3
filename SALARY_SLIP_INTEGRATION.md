# Salary Slip Preview and Download Integration

This document describes the integration of salary slip preview and download functionality with the existing AtharPeople system.

## Overview

The salary slip feature allows users to preview and download salary slips for approved salary calculations. This integration leverages the existing file preview system and download utilities to provide a seamless user experience.

## Features

### 1. Slip Preview
- **Functionality**: Opens salary slips in a modal preview using the existing `FilePreview` component
- **Supported Formats**: PDF files (primary format for salary slips)
- **UI**: Eye icon in the salary actions dropdown menu
- **Translation Keys**: 
  - English: `people.employees-page.profile.salary.slip.preview`
  - Arabic: `people.employees-page.profile.salary.slip.preview`

### 2. Slip Download
- **Functionality**: Downloads salary slip files directly to the user's device
- **UI**: Download icon in the salary actions dropdown menu
- **Loading State**: Shows "Downloading..." text during the download process
- **Translation Keys**:
  - English: `people.employees-page.profile.salary.slip.download`
  - Arabic: `people.employees-page.profile.salary.slip.download`

## Technical Implementation

### API Endpoints

#### GET `/api/finance/salary_calculations/[id]/slip`
- **Purpose**: Handle both preview and download requests for salary slips
- **Parameters**:
  - `id`: Salary calculation ID
  - `action`: Query parameter (`preview` | `download`)
- **Response**:
  - For `preview`: JSON with file metadata (`fileName`, `fileUrl`, `fileType`)
  - For `download`: Binary file stream with appropriate headers

### Frontend Components

#### Updated Components
1. **EmployeeSalaryActions** (`employee-salary-actions.tsx`)
   - Added slip preview and download functionality
   - Integrated with existing FilePreview component
   - Added loading states and error handling

2. **PeopleService** (`people.ts`)
   - Added `getSalarySlip()` method for API communication
   - Handles both preview and download requests

### File Structure
```
services/frontend/src/
├── app/api/finance/salary_calculations/[id]/slip/
│   └── route.ts                          # New API route
├── app/[locale]/_modules/people/_components/employees/profile/salary/
│   └── employee-salary-actions.tsx       # Updated component
├── services/api/
│   └── people.ts                         # Updated service
└── messages/
    ├── en.json                           # Updated translations
    └── ar.json                           # Updated translations
```

## Usage

### Prerequisites
- Salary calculation must have `has_salary_slip: true`
- User must have appropriate permissions to view salary information
- Backend API must support slip generation and retrieval

### User Interface
1. Navigate to employee profile → Salary tab
2. Locate salary calculation with available slip (indicated by `has_salary_slip: true`)
3. Click the actions dropdown (three dots)
4. Choose either:
   - **Preview Slip**: Opens slip in modal preview
   - **Download Slip**: Downloads slip file

### Error Handling
- **Preview Errors**: Shows toast notification with error message
- **Download Errors**: Shows toast notification with error message
- **Network Issues**: Graceful fallback with user-friendly messages

## Translation Keys

### English (`en.json`)
```json
{
  "people": {
    "employees-page": {
      "profile": {
        "salary": {
          "slip": {
            "preview": "Preview Slip",
            "download": "Download Slip",
            "downloading": "Downloading...",
            "preview-error": "Failed to preview salary slip",
            "download-success": "Salary slip downloaded successfully",
            "download-error": "Failed to download salary slip"
          }
        }
      }
    }
  }
}
```

### Arabic (`ar.json`)
```json
{
  "people": {
    "employees-page": {
      "profile": {
        "salary": {
          "slip": {
            "preview": "معاينة القسيمة",
            "download": "تحميل القسيمة",
            "downloading": "جاري التحميل...",
            "preview-error": "فشل في معاينة قسيمة الراتب",
            "download-success": "تم تحميل قسيمة الراتب بنجاح",
            "download-error": "فشل في تحميل قسيمة الراتب"
          }
        }
      }
    }
  }
}
```

## Backend Integration

The frontend expects the backend to provide:

1. **Preview Endpoint**: Returns file metadata for preview
   ```json
   {
     "fileName": "salary-slip-123.pdf",
     "fileUrl": "https://example.com/slip.pdf",
     "fileType": "application/pdf"
   }
   ```

2. **Download Endpoint**: Returns binary file with proper headers
   ```
   Content-Type: application/pdf
   Content-Disposition: attachment; filename="salary-slip-123.pdf"
   ```

## Testing

To test the integration:

1. **Preview Functionality**:
   - Ensure salary calculation has `has_salary_slip: true`
   - Click preview action
   - Verify modal opens with slip content

2. **Download Functionality**:
   - Click download action
   - Verify file downloads with correct filename
   - Check download success notification

3. **Error Scenarios**:
   - Test with invalid slip ID
   - Test network failure scenarios
   - Verify error messages display correctly

## Future Enhancements

- Support for multiple slip formats (Excel, Word)
- Bulk download functionality
- Slip generation status tracking
- Email slip functionality
