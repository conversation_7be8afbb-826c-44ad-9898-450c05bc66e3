"use client";

import React from "react";
import HomepageMetricCards from "../_modules/people/_components/cards/homepage-metric-cards";
import WavyArea<PERSON>hart from "../_modules/people/_components/area-chart-wrapper";
import DistributionOfLeavesChart from "../_modules/people/_components/distribution-of-leaves-chart";
import OrdersPercentageOverYear from "../_modules/people/_components/order-percentage-over-year";
import LatestLeavesRequests from "../_modules/people/_components/latest-leaves-requests";
import { usePermission } from "@/contexts/PermissionContext";
import { PermissionEnum } from "@/enums/Permission";
import EmployeeProfileMainView from "../_modules/people/_components/employee-profile-main-view";
import Loader from "@/components/loader";

const PeopleSystem = () => {
  const { hasPermission, isLoading: isPermissionLoading } = usePermission();

  // Check if user has READ_OWN_EMPLOYEE but not READ_EMPLOYEE
  const hasReadOwnEmployee = hasPermission(PermissionEnum.READ_OWN_EMPLOYEE);
  const hasReadEmployee = hasPermission(PermissionEnum.READ_EMPLOYEE);

  // Show employee profile if user has read_own but not read_employee
  const shouldShowEmployeeProfile = hasReadOwnEmployee && !hasReadEmployee;

  if (isPermissionLoading) return <Loader />;

  if (shouldShowEmployeeProfile) {
    return (
      <div className={`max-md:mt-8`}>
        <EmployeeProfileMainView />
      </div>
    );
  }

  // Show full dashboard for users with full permissions
  return (
    <div className={`max-md:mt-8 grid grid-cols-12 gap-4`}>
      <div
        className={`grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 col-span-12 font-readex_pro`}
      >
        <HomepageMetricCards />
      </div>
      <div className="border rounded-[20px] border-gray-200 col-span-12 max-h-[380px] font-readex_pro">
        <WavyAreaChart />
      </div>
      <div className="grid xl:grid-cols-[1.6fr_1fr] col-span-12 gap-4">
        <div className="border rounded-[20px] border-gray-200 max-h-[526px]">
          <DistributionOfLeavesChart />
        </div>
        <div className="border rounded-[20px] border-gray-200 max-h-[530px]">
          <LatestLeavesRequests />
        </div>
      </div>
      <div className="border rounded-[20px] border-gray-200 col-span-12 max-h-[380px]">
        <OrdersPercentageOverYear />
      </div>
    </div>
  );
};

export default PeopleSystem;
