.search-class-style {
  @apply p-3 border-b border-input w-full py-2 px-2.5 m-0 text-[0.9rem];
}
.country-list-style {
  @apply w-full !mt-1 bg-background border border-input rounded-md !shadow-[0_6px_10px_rgba(0,0,0,0.2)] z-50 overflow-hidden;
}
.phone-input-style {
  @apply h-10 w-full rounded-md border font-medium border-input bg-transparent py-1 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 shadow-sm;
}

.phone-input-style.error {
  border-color: hsl(var(--destructive)) !important;
  outline: none !important;
  box-shadow: 0 0 0 1px hsl(var(--destructive)) !important;
}

.form-error-message {
  color: hsl(var(--destructive));
  font-size: 0.8rem;
  margin-top: 0.25rem;
  font-weight: 500;
}

.react-tel-input .flag-dropdown {
  border-radius: 6px;
  width: 45px;
}

.react-tel-input .flag-dropdown:hover {
  background-color: #fff;
}

.react-tel-input .flag-dropdown .selected-flag {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-inline: 8px;
}

.react-tel-input .flag-dropdown .selected-flag:hover {
  border-radius: 6px;
}

.react-tel-input .flag-dropdown .selected-flag:active {
  border-radius: 6px;
  background-color: #fff;
}

.react-tel-input .flag-dropdown .selected-flag .flag .arrow {
  padding: 0;
  margin: 0;
  inset-inline-start: 20px;
  border-left-width: 4px;
  border-right-width: 4px;
  height: 6px;
  top: 35%;
  border-top-color: rgba(128, 128, 128, 1);
}

.react-tel-input .country:hover {
  background: hsl(var(--background-v2)) !important;
}

.react-tel-input .country.highlight {
  background: hsl(var(--background-v2)) !important;
}
