#!/bin/bash

# Development server management script
# Usage: ./dev-server.sh [start|stop|restart|status|logs]

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_FILE="/tmp/athar-frontend-dev.pid"
LOG_FILE="/tmp/athar-frontend-dev.log"

start_server() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            echo "Development server is already running (PID: $PID)"
            echo "Use './dev-server.sh stop' to stop it first"
            return 1
        else
            echo "Removing stale PID file..."
            rm -f "$PID_FILE"
        fi
    fi

    echo "Starting development server..."
    cd "$SCRIPT_DIR"
    
    # Start the server in background and capture PID
    nohup npm run dev > "$LOG_FILE" 2>&1 &
    SERVER_PID=$!
    
    # Save PID to file
    echo "$SERVER_PID" > "$PID_FILE"
    
    echo "Development server started (PID: $SERVER_PID)"
    echo "Logs: $LOG_FILE"
    echo "PID file: $PID_FILE"
    
    # Wait a moment and check if server started successfully
    sleep 3
    if ps -p "$SERVER_PID" > /dev/null 2>&1; then
        echo "✅ Server is running successfully"
        # Show last few lines of log to see the port
        tail -n 5 "$LOG_FILE"
    else
        echo "❌ Server failed to start. Check logs:"
        cat "$LOG_FILE"
        rm -f "$PID_FILE"
        return 1
    fi
}

stop_server() {
    if [ ! -f "$PID_FILE" ]; then
        echo "No PID file found. Server may not be running."
        return 1
    fi
    
    PID=$(cat "$PID_FILE")
    
    if ps -p "$PID" > /dev/null 2>&1; then
        echo "Stopping development server (PID: $PID)..."
        kill "$PID"
        
        # Wait for graceful shutdown
        sleep 2
        
        # Force kill if still running
        if ps -p "$PID" > /dev/null 2>&1; then
            echo "Force killing server..."
            kill -9 "$PID"
        fi
        
        rm -f "$PID_FILE"
        echo "✅ Development server stopped"
    else
        echo "Server with PID $PID is not running"
        rm -f "$PID_FILE"
    fi
}

server_status() {
    if [ ! -f "$PID_FILE" ]; then
        echo "❌ Development server is not running (no PID file)"
        return 1
    fi
    
    PID=$(cat "$PID_FILE")
    
    if ps -p "$PID" > /dev/null 2>&1; then
        echo "✅ Development server is running (PID: $PID)"
        echo "📁 Logs: $LOG_FILE"
        echo "🔗 Likely URL: http://localhost:3000 or http://localhost:3001"
        return 0
    else
        echo "❌ Development server is not running (stale PID file)"
        rm -f "$PID_FILE"
        return 1
    fi
}

show_logs() {
    if [ ! -f "$LOG_FILE" ]; then
        echo "No log file found at $LOG_FILE"
        return 1
    fi
    
    echo "📋 Development server logs:"
    echo "=========================="
    tail -f "$LOG_FILE"
}

restart_server() {
    echo "Restarting development server..."
    stop_server
    sleep 1
    start_server
}

case "$1" in
    start)
        start_server
        ;;
    stop)
        stop_server
        ;;
    restart)
        restart_server
        ;;
    status)
        server_status
        ;;
    logs)
        show_logs
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|logs}"
        echo ""
        echo "Commands:"
        echo "  start   - Start the development server"
        echo "  stop    - Stop the development server"
        echo "  restart - Restart the development server"
        echo "  status  - Check if server is running"
        echo "  logs    - Show server logs (tail -f)"
        echo ""
        echo "Files:"
        echo "  PID: $PID_FILE"
        echo "  Logs: $LOG_FILE"
        exit 1
        ;;
esac
