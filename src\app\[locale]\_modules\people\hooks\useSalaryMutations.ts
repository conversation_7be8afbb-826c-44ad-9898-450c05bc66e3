"use client";

import { useOptimisticMutation } from "@/hooks/ues-optimistic-mutatation";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { SalaryCalculationApiResponse } from "../type/employees-salaries";
import { SALARY_STATUS } from "@/constants/enum";
import { updateSalaryStatus as updateSalaryStatusAction } from "../actions/update-salary-status";
import { useToastMessage } from "@/hooks/use-toast-message";
import { fetcher } from "@/services/fetcher";
import { useApprovalMutations } from "./useApprovalMutations";
import { mutate } from "swr";
import { OptimisticApprovalAction } from "../utils/approval-optimistic-updates";
import { createApprovalWorkflowUpdate } from "../utils/approval-workflow-helpers";
import { useFilterParams } from "@/hooks/filters/useFilterParams";
import { useApiUrl } from "@/hooks/useApiUrl";
import { useSearchParams } from "next/navigation";
import { filterIncludedByType } from "../utils/included-data/getIncludedItem";

type UseSalaryMutationsProps = {
  employeeId?: string;
  page?: string | number;
  limit?: string | number;
  sort?: string;
  tableId?: string;
  onSuccess?: () => void;
};

// Salary-specific status update logic
const updateSalaryStatus = (
  salary: any,
  isWorkflowComplete: boolean,
  action: OptimisticApprovalAction,
) => {
  let newStatus = salary.attributes.status;

  if (action === "reject") {
    newStatus = SALARY_STATUS.REJECTED;
  } else if (action === "approve" && isWorkflowComplete) {
    newStatus = SALARY_STATUS.APPROVED;
  }

  return {
    ...salary,
    attributes: {
      ...salary.attributes,
      status: newStatus,
    },
  };
};

const createSalaryApprovalUpdate = (
  currentData: SalaryCalculationApiResponse | undefined,
  salaryId: string,
  action: OptimisticApprovalAction,
  comment?: string,
): SalaryCalculationApiResponse | undefined => {
  return createApprovalWorkflowUpdate(
    currentData,
    salaryId,
    action,
    updateSalaryStatus,
    comment,
  );
};

export const useSalaryMutations = ({
  employeeId,
  page = 1,
  limit = 5,
  sort = "-period_start_date",
  tableId = "salary-calculations",
  onSuccess,
}: UseSalaryMutationsProps = {}) => {
  const { showToast } = useToastMessage();
  const t = useTranslations();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isPaying, setIsPaying] = useState(false);
  const [isRegenerating, setIsRegenerating] = useState(false);
  const searchParams = useSearchParams();
  const { filters } = useFilterParams(tableId);

  // Use the generic approval mutations hook for approve/reject
  const { approveRequest, rejectRequest, isApproving, isRejecting } =
    useApprovalMutations({
      approveSuccess: t(
        "people.employees-page.salary.table.actions.approve-success",
      ),
      approveError: t(
        "people.employees-page.salary.table.actions.approve-error",
      ),
      rejectSuccess: t(
        "people.employees-page.salary.table.actions.reject-success",
      ),
      rejectError: t("people.employees-page.salary.table.actions.reject-error"),
    });

  // Handle employee-specific filtering
  const salaryFilters = employeeId
    ? { ...filters, employee_id_eq: employeeId }
    : filters;

  const searchQuery = searchParams.get("search") || "";

  // Build the API URL using modern approach
  const apiUrl = useApiUrl({
    baseUrl: "/api/finance/salary_calculations",
    page: Number(page),
    limit: Number(limit),
    sort: String(sort),
    search: searchQuery || undefined,
    filters: salaryFilters,
    tableId: tableId,
  });

  // Use the optimistic mutation hook
  const { data, error, isLoading, mutateData } = useOptimisticMutation<
    SalaryCalculationApiResponse,
    { salaryId: string; status: SALARY_STATUS }
  >({
    key: apiUrl,
    fetcher,
    mutations: {
      submitSalary: {
        updateFn: (currentData, { salaryId }) => {
          if (!currentData) return currentData;

          return {
            ...currentData,
            data: currentData.data.map((salary) =>
              salary.id === salaryId
                ? {
                    ...salary,
                    attributes: {
                      ...salary.attributes,
                      status: SALARY_STATUS.SUBMITTED,
                    },
                  }
                : salary,
            ),
          };
        },
        mutationFn: async ({ salaryId }) => {
          setIsSubmitting(true);
          try {
            const result = await updateSalaryStatusAction(
              salaryId,
              SALARY_STATUS.SUBMITTED,
            );

            if (result.error) {
              throw new Error(result.error);
            }

            showToast(
              "success",
              t("people.employees-page.salary.table.actions.submit-success"),
            );

            // Call onSuccess callback if provided
            if (onSuccess) {
              onSuccess();
            }

            // Return undefined to keep the optimistic update
            return { data: undefined };
          } catch (error) {
            showToast(
              "error",
              t("people.employees-page.salary.table.actions.submit-error"),
            );
            throw error;
          } finally {
            setIsSubmitting(false);
          }
        },
      },
      approveSalary: {
        updateFn: (currentData, { salaryId }) => {
          return createSalaryApprovalUpdate(currentData, salaryId, "approve");
        },
        mutationFn: async ({ salaryId }) => {
          try {
            // Find the approval request ID from the salary calculation
            const salaryCalculation = data?.data.find(
              (salary) => salary.id === salaryId,
            );
            const approvalRequestId =
              salaryCalculation?.relationships?.approval_request?.data?.id;

            if (!approvalRequestId) {
              throw new Error("Approval request ID not found");
            }

            // Use the generic approval hook
            await approveRequest(approvalRequestId, "");

            return { data: undefined };
          } catch (error) {
            throw error;
          }
        },
      },
      rejectSalary: {
        updateFn: (currentData, { salaryId }) => {
          return createSalaryApprovalUpdate(currentData, salaryId, "reject");
        },
        mutationFn: async ({ salaryId }) => {
          try {
            // Find the approval request ID from the salary calculation
            const salaryCalculation = data?.data.find(
              (salary) => salary.id === salaryId,
            );
            const approvalRequestId =
              salaryCalculation?.relationships?.approval_request?.data?.id;

            if (!approvalRequestId) {
              throw new Error("Approval request ID not found");
            }

            // Use the generic rejection hook
            await rejectRequest(approvalRequestId, "");

            return { data: undefined };
          } catch (error) {
            throw error;
          }
        },
      },
      paySalary: {
        updateFn: (currentData, { salaryId }) => {
          if (!currentData) return currentData;

          return {
            ...currentData,
            data: currentData.data.map((salary) =>
              salary.id === salaryId
                ? {
                    ...salary,
                    attributes: {
                      ...salary.attributes,
                      status: SALARY_STATUS.PAID,
                    },
                  }
                : salary,
            ),
          };
        },
        mutationFn: async ({ salaryId }) => {
          setIsPaying(true);
          try {
            const result = await updateSalaryStatusAction(
              salaryId,
              SALARY_STATUS.PAID,
            );

            if (result.error) {
              throw new Error(result.error);
            }

            showToast(
              "success",
              t("people.employees-page.salary.table.actions.pay-success") ||
                "Salary paid successfully",
            );

            // Call onSuccess callback if provided
            if (onSuccess) {
              onSuccess();
            }

            // Return undefined to keep the optimistic update
            return { data: undefined };
          } catch (error) {
            showToast(
              "error",
              t("people.employees-page.salary.table.actions.pay-error") ||
                "Failed to pay salary",
            );
            throw error;
          } finally {
            setIsPaying(false);
          }
        },
      },
    },
    swrOptions: {
      dedupingInterval: 2000,
      revalidateAfterMutation: false,
      keepPreviousData: true,
    },
  });

  // Helper functions to call the mutations
  const submitSalary = async (salaryId: string) => {
    try {
      // First perform the optimistic update
      await mutateData("submitSalary", {
        salaryId,
        status: SALARY_STATUS.SUBMITTED,
      });
      // Then call the API to update the salary status
      await mutate(apiUrl);

      return true;
    } catch (error) {
      return false;
    }
  };

  const approveSalary = async (salaryId: string) => {
    try {
      // Simply call mutateData with the mutation name and parameters
      await mutateData("approveSalary", {
        salaryId,
        status: SALARY_STATUS.APPROVED,
      });
      return true;
    } catch (error) {
      return false;
    }
  };

  const rejectSalary = async (salaryId: string) => {
    try {
      // The mutation function will handle finding the approval request ID
      await mutateData("rejectSalary", {
        salaryId,
        status: SALARY_STATUS.REJECTED,
      });
      return true;
    } catch (error) {
      return false;
    }
  };

  const regenerateSlip = async (salaryId: string) => {
    if (isRegenerating) return false;

    setIsRegenerating(true);
    try {
      // Call the API route instead of the service directly
      const response = await fetch(
        `/api/finance/salary_calculations/${salaryId}/regenerate_slip`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include", // Include cookies for authentication
        },
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || `HTTP ${response.status}: ${response.statusText}`,
        );
      }

      // Revalidate the data to refresh the UI
      await mutate(apiUrl);
      showToast(
        "success",
        t("people.employees-page.salary.slip.regenerate-success") ||
          "Slip regenerated successfully",
      );
      return true;
    } catch (error) {
      console.error("Regenerate slip error:", error);
      showToast(
        "error",
        t("people.employees-page.salary.slip.regenerate-error") ||
          "Failed to regenerate slip",
      );
      return false;
    } finally {
      setIsRegenerating(false);
    }
  };

  const paySalary = async (salaryId: string) => {
    try {
      // Simply call mutateData with the mutation name and parameters
      await mutateData("paySalary", {
        salaryId,
        status: SALARY_STATUS.PAID,
      });
      return true;
    } catch (error) {
      return false;
    }
  };

  return {
    SalaryStatus: data?.data || [],
    employeesData: filterIncludedByType(data?.included, "employee") || [],
    calculationDetailsData:
      filterIncludedByType(data?.included, "salary_calculation_detail") || [],
    includedData: data?.included || [],
    // Modern data structure
    salaryCalculations: data?.data || [],
    totalCount: data?.meta?.pagination?.count ?? 0,
    pagination: data?.meta?.pagination,
    isLoading,
    error,

    // Mutation functions
    submitSalary,
    approveSalary,
    rejectSalary,
    paySalary,
    regenerateSlip,
    isSubmitting,
    isApproving,
    isRejecting,
    isPaying,
    isRegenerating,
  };
};
