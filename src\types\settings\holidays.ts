// Holiday-related types for the settings module

export type THolidayAttributes = {
  id: number;
  name: string;
  start_date: string;
  end_date: string;
  exemption_type: string;
  exemption_type_label?: string;
  description?: string;
  is_active?: boolean;
  affects_salary?: boolean;
  affects_attendance?: boolean;
  duration_days?: number;
  created_at?: string;
  updated_at?: string;
};

export type THoliday = {
  id: number;
  type: string;
  attributes: THolidayAttributes;
};

// Helper type for working with dates in components
export type THolidayDisplay = {
  id: number;
  name: string;
  startDate: Date;
  endDate: Date;
  exemption_type: string;
  exemption_type_label?: string;
  description?: string;
  is_active?: boolean;
  duration_days?: number;
};

// API response types
export type THolidaysResponse = {
  data: THoliday[];
  meta: {
    pagination: {
      current_page: number;
      total_pages: number;
      total_count: number;
      per_page: number;
    };
  };
};

export type THolidayResponse = {
  data: THoliday;
};

// Form types
export type THolidayFormData = {
  name: string;
  start_date: Date;
  end_date: Date;
};

// Calendar event type for holiday display
export type THolidayCalendarEvent = {
  id: string;
  title: string;
  start: string;
  end: string;
  backgroundColor: string;
  borderColor: string;
  textColor: string;
  extendedProps: {
    holiday: THoliday;
  };
};
