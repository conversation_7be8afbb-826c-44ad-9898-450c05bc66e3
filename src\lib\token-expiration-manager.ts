let tokenExpired = false;
const listeners = new Set<() => void>();
let lastRouteBeforeExpiry: string | null = null;

export function notifyTokenExpired() {
  if (tokenExpired) return;
  tokenExpired = true;
  listeners.forEach((fn) => fn());
}

export function setLastRouteBeforeExpiry(route: string) {
  lastRouteBeforeExpiry = route;
}

export function getLastRouteBeforeExpiry(): string | null {
  return lastRouteBeforeExpiry;
}

export function clearLastRouteBeforeExpiry() {
  lastRouteBeforeExpiry = null;
}

export function onTokenExpired(fn: () => void) {
  listeners.add(fn);
  return () => listeners.delete(fn);
}

export function resetTokenExpiredFlag() {
  tokenExpired = false;
}

export function resetTokenExpiredFlagAndClearRoute() {
  tokenExpired = false;
  clearLastRouteBeforeExpiry();
}

export function isTokenExpired() {
  return tokenExpired;
}
