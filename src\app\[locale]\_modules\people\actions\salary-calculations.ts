"use server";

import { handleError } from "@/lib/utils";
import { ActionState } from "@/types";
import { peopleService } from "@/services/api/people";

export async function calculateSalaryPeriod(
  period: string,
): Promise<ActionState<{ success: boolean }>> {
  try {
    if (!period) {
      return {
        error: "Period is required",
        success: "",
        issues: ["Please provide a valid period"],
        data: null,
      };
    }

    await peopleService.calculateSalaryPeriod(period);

    return {
      success: "Salary calculation initiated successfully",
      error: "",
      issues: [],
      data: { success: true },
    };
  } catch (err) {
    return handleError(
      err,
      "Failed to calculate salary for the selected period",
      [],
    );
  }
}
