"use client";

import React, { Suspense, useCallback } from "react";
import ProjectsHeader from "../../_modules/people/_components/projects/header/projects-header";
import ProjectsTable from "../../_modules/people/_components/projects/table";
import { useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";
import { TFunction } from "@/types";
import { mutate } from "swr";

const Projects = () => {
  const searchParams = useSearchParams();
  const t = useTranslations("projects.page") as TFunction;

  // Convert searchParams to the expected format
  const params: { [key: string]: string | string[] | undefined } = {};
  searchParams.forEach((value, key) => {
    params[key] = value;
  });

  // Function to refresh projects data
  const refreshProjects = useCallback(() => {
    mutate(
      (key: string) => typeof key === "string" && key.includes("/api/projects"),
    );
  }, []);

  return (
    <>
      <ProjectsHeader
        title={"إدارة المشاريع وتعيين الأدوار للموظفين"}
        onProjectCreated={refreshProjects}
      />
      <Suspense fallback={<div>{"Loading..."}</div>}>
        <ProjectsTable
          showPagination={true}
          searchParams={params}
          onProjectUpdated={refreshProjects}
        />
      </Suspense>
    </>
  );
};

export default Projects;
