import {
  activityColorMap,
  ActivityType,
} from "@/app/[locale]/_modules/people/enum";
import { AttendancePeriod } from "@/types/employee/daySummary";

export const getPeriodColor = (period: AttendancePeriod): any => {
  const { period_type, activity_type } = period?.attributes || {};
  const key = activity_type
    ? ActivityType[activity_type as keyof typeof ActivityType]
    : period_type;

  return activityColorMap[key] || activityColorMap["default"]; // fallback gray
};
