"use client";

import { useTranslations } from "next-intl";
import ResponsiveDialog from "@/components/responsive-dialog";
import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import { TProject } from "@/types/core/project";
import { TFunction } from "@/types";
import AddProjectForm from "./header/add-project-form";

type ProjectDialogProps = {
  mode: "add" | "edit";
  project?: TProject | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
};

export default function ProjectDialog({
  mode,
  project = null,
  isOpen,
  onClose,
  onSuccess,
}: ProjectDialogProps) {
  const t = useTranslations() as TFunction;

  const handleSuccess = () => {
    onClose();
    if (onSuccess) {
      onSuccess();
    }
  };

  const getTitle = () => {
    return mode === "add"
      ? t("projects.page.create.title")
      : t("projects.page.update.title");
  };

  const getDescription = () => {
    return mode === "add"
      ? t("projects.page.create.description")
      : t("projects.page.update.description");
  };

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={onClose}
      closeBtnStyle="top-[21px]"
      header={
        <>
          <div className="px-6 py-[22px] border-b">
            <DialogTitle className="font-semibold text-[18px] leading-[28px]">
              {getTitle()}
            </DialogTitle>
            <DialogDescription className="text-sm text-gray-600 mt-1 sr-only">
              {getDescription()}
            </DialogDescription>
          </div>
        </>
      }
    >
      <AddProjectForm
        mode={mode}
        project={project}
        onSuccess={handleSuccess}
        onCancel={onClose}
      />
    </ResponsiveDialog>
  );
}
