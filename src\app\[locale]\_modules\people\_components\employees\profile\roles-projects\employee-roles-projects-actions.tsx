"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { LANGUAGES } from "@/constants/enum";
import { Row } from "@tanstack/react-table";
import { useLocale, useTranslations } from "next-intl";
import { CiMenuKebab } from "react-icons/ci";
import { Locale } from "@/i18n/routing";
import { Trash } from "../../../../../../../../../public/images/icons";
import { TRoleProjectPair } from "../../../../type/employee-roles-projects";

type EmployeeRolesProjectsActionsProps<TData> = {
  row: Row<TData>;
  onDeleteRoleProject?: (roleProjectId: string) => void;
  isDeleting?: boolean;
};

export const EmployeeRolesProjectsActions = <TData extends TRoleProjectPair>({
  row,
  onDeleteRoleProject,
  isDeleting,
}: EmployeeRolesProjectsActionsProps<TData>) => {
  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const t = useTranslations();

  // Get the role project details
  const roleProject = row.original;
  const userRoleId = roleProject.userRoleId;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="h-8 w-8 p-0 data-[state=open]:bg-muted"
        >
          <span className="sr-only">Open menu</span>
          <CiMenuKebab className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align={isAr ? "start" : "end"}
        className="w-[160px] rounded-xl"
      >
        {onDeleteRoleProject && (
          <DropdownMenuItem
            className="cursor-pointer text-error hover:text-error hover:bg-red-50 focus:text-error focus:bg-red-50 rtl:justify-end"
            onClick={() => onDeleteRoleProject(userRoleId)}
            disabled={isDeleting}
          >
            <Trash className="mr-2 !h-6 !w-6" />
            <span>{t("common.buttonText.delete")}</span>
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
