"use client";

import DeviceCommandsForm from "./device-commands-form";
import DeviceCommandsSkeleton from "./device-commands-skeleton";
import { useAvailableCommands } from "../../../../hooks/devices/commands/useAvailableCommands";

type DeviceCommandsTabProps = {
  deviceId: string;
};

export default function DeviceCommandsTab({
  deviceId,
}: DeviceCommandsTabProps) {
  const { isLoading: isLoadingCommands } = useAvailableCommands(deviceId);

  // Show skeleton while commands are loading on initial load
  if (isLoadingCommands) {
    return (
      <div className="space-y-6">
        <DeviceCommandsSkeleton />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <DeviceCommandsForm deviceId={deviceId} />
    </div>
  );
}
