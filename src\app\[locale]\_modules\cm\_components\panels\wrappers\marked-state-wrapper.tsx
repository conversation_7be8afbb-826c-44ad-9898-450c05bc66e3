import StatusCards from "../state-card";

const MarketState = () => {
  const markedNotes = [
    {
      id: "6487",
      userName: "زين علي",
      timestamp: "منذ 3 أيام",
      content:
        "تم تحديث خطة العلاج للمستفيد وإضافة جلسات إضافية للعلاج الطبيعي. يرجى مراجعة التفاصيل والموافقة على التعديلات المقترحة.",
    },
    {
      id: "7283",
      userName: "2زين علي",
      timestamp: "منذ 3 أيام",
      content:
        "تقرير تقييم شامل للحالة يظهر تحسناً ملحوظاً في الاستجابة للعلاج. يُنصح بمتابعة الخطة الحالية مع زيادة تكرار الجلسات.",
    },
    {
      id: "9837",
      userName: "3زين علي",
      timestamp: "منذ 3 أيام",
      content:
        "ملاحظة مهمة حول ضرورة تنسيق المواعيد مع الفريق الطبي لضمان استمرارية الرعاية وتجنب تضارب المواعيد.",
    },
    // Add more notes as needed
  ];

  return (
    <>
      <StatusCards
        variant="marked"
        title="الحالات المميزه بعلامة"
        notes={markedNotes}
      />
    </>
  );
};

export default MarketState;
