{"HomePage": {"title": ",Hello World", "about": "Go to the next page"}, "common": {"NotFoundPage": "404 Page Not Found", "PageNotFoundMessage": "Sorry, the page you are looking for does not exist.", "NoActionsAvailable": "No actions available", "GoHome": "Go to Homepage", "unknown": "Unknown", "not-available": "Not Available", "loading": "Loading...", "calculate": "Calculate", "layouts": {"root": "Comprehensive enterprise management platform for efficient HR, procurement, and case management operations", "cm": "Efficient case management system for scheduling appointments, managing beneficiary records, and tracking the assessment plan.", "people": "Integrated HR system for managing employees, leave requests, attendance tracking, and payroll processing.", "procure": "Procurement system for managing purchase requests, handle the approvals within the organization and manage the categories."}, "Error": {"title": "Sorry! Something went wrong", "description": "<p>Unfortunately, an error occurred.</p><p>You can try <retry>reloading the page</retry> you were visiting.</p>", "retry": "Retry", "noChanges": "No changes detected. Update availability before submitting.", "time": {"format": "Invalid time format", "interval": {"required": "At least one interval is required"}}, "responses": {"global": "An error occurred. Please try again later.", "paymentConfirmed": "Payment confirmed successfully.", "paymentRefused": "Payment refused successfully.", "paymentConfirmFailed": "Failed to confirm payment. Please try again.", "paymentRefuseFailed": "Failed to refuse payment. Please try again."}}, "form": {"attendance": {"error": {"employee-id-required": "Employee ID is required", "activity-type-required": "Activity type is required", "invalid-activity-type": "Invalid activity type selected", "invalid-date": "Invalid date format", "note-min": "Note should be at least 3 characters", "note-max": "Note should not exceed 500 characters", "future-date": "Date cannot be in the past", "event-type-required": "Event type is required", "location-required": "Location is required", "invalid-location": "Invalid location"}}, "date-range": {"placeholder": "Select date range", "modal": {"title": "Select Date Range", "description": "Choose start and end dates", "confirm": "Confirm", "cancel": "Cancel"}, "start-date": "Start Date", "end-date": "End Date", "selected-range": "Selected Range", "none": "None", "errors": {"dates-required": "Please select both start and end dates", "past-date": "Start date cannot be in the past", "min-date": "Start date is too early", "max-date": "End date is too late"}}, "profile-image": {"label": "Profile Picture", "buttons": {"upload": "Upload Picture", "delete": "Delete"}, "error": {"file": {"maxSize": "The image must be less than 2MB.", "acceptedTypes": "Only the formats: .jpg, .jpeg, .png, and .webp are supported."}}}, "select": {"placeholder": "Select an option", "yes": "Yes", "no": "No"}, "name": {"label": "Name", "update": {"placeholder": "Update your name", "label": "Email"}, "error": {"required": "Name is required", "maxLength": "Name must be 25 characters or less"}}, "email": {"placeholder": "Enter your email", "label": "Email", "error": {"required": "Email is required", "notValid": "Email is not valid"}, "tooltip": "Email should be a valid email"}, "employee": {"error": {"required": "Employee is required"}}, "currentPassword": {"label": "Current password", "error": {"required": "Current password is required"}}, "password": {"label": "Password", "currLabel": "Current password", "newLabel": "New password", "error": {"required": "Password is required", "currRequired": "Current password is required", "confirmRequired": "Password confirmation is required", "length": "Password must be at least six characters long", "match": "Passwords do not match"}}, "confirmPassword": {"label": "Confirm password", "newLabel": "Confirm new password", "error": {"required": "Password confirmation is required"}}, "verifyCode": {"label": "Enter verification code", "placeholder": "Enter your verification code", "error": {"required": "Verification code is required", "notNumber": "It must be a number"}}, "required": "This field is required", "url": {"error": {"notValid": "Please enter a valid URL"}}, "number": {"error": {"notNumber": "Must be a number", "positive": "Must be a positive number", "integer": "Must be an integer", "decimal": "Must have at most 2 decimal places"}}, "textarea": {"error": {"maxLength": "Must be less than 1000 characters"}}, "file": {"error": {"invalid": "Invalid file", "type": "Invalid file type"}}, "mobile": {"label": "Mobile Number", "placeholder": "Enter your mobile number", "error": {"required": "Mobile number is required", "notValid": "Please enter a valid phone number"}}, "department": {"label": "Department", "placeholder": "Select department", "error": {"required": "Department is required"}}, "jobTitle": {"label": "Job Title", "placeholder": "Select job title", "error": {"required": "Job title is required"}}, "attachment": {"label": "Attachment", "placeholder": "Upload attachment", "error": {"file": {"maxSize": "The attachment must be less than 5MB."}, "name": {"required": "File name is required", "maxLength": "File name must be 100 characters or less"}}}, "date": {"error": {"required": "Date is required", "invalid": "Please enter a valid date", "future": "Date cannot be in the future", "must_be_current_or_future_month": "Date must be in the current month or future", "must_be_today_or_future": "Date must be today or in the future"}}, "leave": {"error": {"id-required": "Leave ID is required", "employee-id-required": "Employee ID is required", "start-date-required": "Start date is required", "end-date-required": "End date is required", "type-required": "Leave type is required", "future-date": "End date must be today or in the future", "reason-min": "Reason must be at least 3 characters", "reason-max": "Reason cannot exceed 500 characters", "invalid-date": "Invalid date format", "start-date-past": "Start date cannot be more than 3 months (90 days) in the past", "start-after-end": "Start date must be before end date", "half-day-single-day": "Half-day leave must be for a single day (start date must equal end date)", "marriage-no-half-day": "Marriage leave cannot be taken as half-day", "maternity-no-half-day": "Maternity leave cannot be taken as half-day", "paternity-no-half-day": "Paternity leave cannot be taken as half-day", "overlapping-leave": "The leave period overlaps with another pending or approved leave"}}, "holiday": {"error": {"name-required": "Holiday name is required", "name-max": "Holiday name cannot exceed 100 characters", "start-date-required": "Start date is required", "end-date-required": "End date is required", "invalid-date-range": "End date must be after start date"}}, "attachments": {"buttons": {"upload": "Upload Files"}, "error": {"maxFiles": "You can upload a maximum of {count} files.", "maxSize": "The file size must be less than 5MB.", "totalMaxSize": "The total size of all files must be less than 5MB.", "invalidType": "Some files were not accepted due to invalid file type."}}, "assignment": {"title": "Assignment", "add": "Add Assignment", "empty": "No assignments added yet", "error": {"oneDefaultRequired": "One default assignment is required"}, "project": {"label": "Project Name", "placeholder": "Enter project name", "required": "Project name is required"}, "role": {"label": "Role", "placeholder": "Enter role", "required": "Role is required"}, "default": {"label": "<PERSON><PERSON><PERSON>"}}, "updateSalary": {"salary-required": "Salary is required.", "note-required": "Note is required."}, "salary": {"base_salary": {"required": "Base salary is required", "label": "Base Salary", "placeholder": "4000"}, "housing_allowance": {"label": "Housing Allowance", "placeholder": "0"}, "transportation_allowance": {"label": "Transportation Allowance", "placeholder": "0"}, "other_allowances": {"label": "Other Allowances", "placeholder": "0"}, "effective_date": {"label": "Effective Date"}, "end_date": {"label": "End Date"}, "status": {"label": "Status", "placeholder": "Select status"}, "notes": {"required": "Notes are required", "label": "Notes", "label2": "Reason for the edit", "placeholder": "Add any additional notes here", "placeholder2": "Enter the reason for the edit"}}, "status": {"required": "Status is required", "options": {"active": "Active", "inactive": "Inactive"}}}, "buttonText": {"ok": "OK", "submit": "Submit", "cancel": "Cancel", "next": "Next", "previous": "previous", "Save": "Save", "save": "Save", "saved": "Saved", "saving": "Saving...", "deleting": "Deleting...", "upload": "Upload", "uploading": "Uploading...", "add": "Add", "update": "Update", "cancel2": "Cancel", "payment-confirm": "Confirm Payment", "download": "Download", "delete": "Delete", "back": "Back", "create": "Create"}, "actions": {"save": "Save", "cancel": "Cancel", "saving": "Saving...", "edit": "Edit", "delete": "Delete", "approve-success": "تمت الموافقة على الطلب بنجاح", "approve-error": "فشل في الموافقة على الطلب", "reject-success": "تم رفض الطلب بنجاح", "reject-error": "فشل في رفض الطلب", "approve-final-success": "تمت الموافقة على الطلب بنجاح", "withdraw-success": "تم سحب الطلب بنجاح", "withdraw-error": "فشل في سحب الطل]"}, "delete-dialog": {"title": "Delete Item", "description": "Are you sure you want to delete {itemName} ? This action cannot be undone.", "item-details": "<PERSON><PERSON>"}, "delete": "Delete", "deleting": "Deleting...", "confirm": "Confirm", "cancel": "Cancel", "modals": {"datetime": {"title": "Select Date & Time", "description": "Choose the date and time.", "date": "Date", "time": "Time", "selected": "Selected Date & Time"}}, "navbar": {"userProfile": {"editProfile": "Edit Profile", "editPassword": "Edit Password", "settings": "Settings", "logout": "Logout"}, "notification": {"title": "Notifications", "markAsRead": "<PERSON> as read", "markAllAsRead": "Mark all as read", "newNotification": "New appointment", "seeMore": "See more"}, "search": {"placeholder": "Search"}, "mobileMenu": {"title": "Mobile Menu", "language": "Language"}}, "sidebar": {"settings": "Settings", "links": {"procure": "Home", "people": "Home", "cm": "Home", "patients": "Beneficiaries", "beneficiaries": "Beneficiaries", "cases": "Cases", "employees": "Employees", "requests": "Requests", "salaries": "Salaries", "devices": "Devices", "products": "Products", "projects": "Projects", "appointments": "Appointments", "settings": "Settings", "notifications": "Notifications", "attendance": "Attendance", "attendance-log": "Attendance Log"}}, "settings": {"tabs": {"global": "Global Settings", "change-password": "Change Password", "notifications": "Notifications", "holidays": "Holidays", "company": "Company Data", "salary": "Salary <PERSON>s", "attendance": "Attendance Settings"}, "company": {"description": "Manage company information and basic details", "noSettings": "No company settings available", "fields": {"name": "Company Name", "email": "Company Email", "phone": "Company Phone", "address": "Company Address", "website": "Company Website", "tax_number": "Tax Number", "registration_number": "Registration Number", "industry": "Company Industry Type", "logo_path": "Company Logo Path"}}, "salary": {"description": "Manage salary settings, currencies, and payment methods", "noSettings": "No salary settings available", "fields": {"currency_code": "Default Currency Code", "currency_symbol": "Currency Symbol for Display", "default_payment_method": "Default Payment Method for Salaries", "overtime_rate": "Overtime Rate", "bonus_calculation": "Bonus Calculation Method"}}, "payroll": {"description": "Manage salary settings, currencies, and payment methods", "noSettings": "No salary settings available", "fields": {"currency_code": "Default Currency Code", "currency_symbol": "Currency Symbol for Display", "default_payment_method": "Default Payment Method for Salaries", "overtime_rate": "Overtime Rate", "bonus_calculation": "Bonus Calculation Method"}}, "attendance": {"description": "Manage attendance settings, work hours, and time thresholds", "noSettings": "No attendance settings available", "fields": {"work_start_time": "Default Work Start Time", "work_end_time": "Default Work End Time", "required_work_minutes": "Required Work Minutes Per Day (8 hours)", "break_threshold_minutes": "Maximum Break Duration Before Flagging (2 hours)", "duplicate_threshold_seconds": "<PERSON><PERSON><PERSON><PERSON> for Identifying Duplicate Events in Seconds", "late_threshold_minutes": "Late Arrival <PERSON> (Minutes)", "early_departure_threshold_minutes": "Early Departure <PERSON><PERSON><PERSON><PERSON> (Minutes)", "accumulated_hours_threshold": "Accumulated Missing Hours Threshold for Leave Conversion", "auto_leave_counts_against_balance": "Counting automatically generated leave against the leave balance.", "auto_leave_enabled": "Enable Automatic Leave Detection", "minimum_hours_per_day": "Minimum Hours Per Day to Avoid Leave Detection", "expected_working_hours_per_month": "Expected Working Hours Per Month", "notify_auto_leave_created": "Send Notifications When Auto-Generated Leaves are Created", "notify_employees_auto_leave": "Notify Employees When Auto-Generated Leaves are Created", "notify_managers_auto_leave": "Notify Managers When Auto-Generated Leaves are Created", "weekend_days": "Weekend Days (0=Sunday, 1=Monday, ..., 6=Saturday)", "enable_automatic_leave_detection": "Enable Automatic Leave Detection", "exclude_weekends": "Exclude Weekends from Working Days", "exclude_holidays": "Exclude Holidays from Working Days", "deductions_enabled": "Enable Attendance Deductions", "daily_expected_hours": "Daily Expected Working Hours", "minimum_daily_hours": "Minimum Hours Per Day to Avoid Leave Detection", "monthly_expected_hours": "Expected Working Hours Per Month", "send_auto_leave_notifications": "Send Notifications When Auto-Generated Leaves are Created", "auto_leave_notifications": "Send Notifications When Auto-Generated Leaves are Created", "daily_work_threshold": "Daily Work Threshold"}}, "readOnlyMessage": "You don't have permission to modify these settings"}, "footer": {"description": "All rights reserved © 2025"}, "fields": {"name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "website": "Website", "tax_number": "Tax Number", "registration_number": "Registration Number", "description": "Description", "value": "Value", "enabled": "Enabled", "disabled": "Disabled", "status": "Status", "type": "Type", "category": "Category", "priority": "Priority", "notes": "Notes", "comments": "Comments"}, "placeholders": {"enter": "Enter", "name": "Enter company name", "email": "Enter email address", "phone": "Enter phone number", "address": "Enter address", "website": "Enter website URL", "tax_number": "Enter tax number", "registration_number": "Enter registration number"}, "cm": {"notification": {"message": "New appointment"}}, "procure": {"notification": {"message": ""}}, "people": {"notification": {"message": ""}}, "Table": {"searchPlaceholder": "Search here...", "filter": "Filter", "visibility": "Columns", "addRule": "Add Rule", "clear": "Clear", "apply": "Apply Filter", "rules": "Rules", "noResults": "No results.", "filter-options": {"title": "Filter Options", "selectField": "Select field", "selectOperator": "Select operator", "enterValue": "Enter value", "selectDate": "Select date"}, "filter-operators": {"eq": "Equals", "not_eq": "Not equals", "cont": "Contains", "not_cont": "Does not contain", "i_cont": "Contains (case insensitive)", "not_i_cont": "Does not contain (case insensitive)", "start": "Starts with", "not_start": "Does not start with", "end": "Ends with", "not_end": "Does not end with", "matches": "Matches", "does_not_match": "Does not match", "gt": "Greater than", "gteq": "Greater than or equal", "lt": "Less than", "lteq": "Less than or equal", "null": "Is empty", "not_null": "Is not empty", "present": "Is present", "blank": "Is blank", "true": "Is true", "false": "Is false"}, "body": {"beneficiary-status": {"incoming": "Incoming", "pending": "Pending", "waiting": "Waiting", "completed": "Completed"}}, "selectAll": ":اختيار الكل", "selectRow": ":اختيار الصف"}, "filter": {"rule": "Rule"}, "pagination": {"show": "Show", "previous": "Previous", "next": "Next", "from": "From", "to": "To", "outOf": "Out of", "result": "Result", "page": "Page"}, "days": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "status": {"request-status": {"accepted": "Accepted", "approved": "Approved", "rejected": "Rejected", "waiting": "Waiting", "pending": "Waiting", "completed": "Completed", "withdrawn": "withdrawn"}, "salary-status": {"approved": "Approved", "rejected": "Rejected", "waiting": "Pending", "draft": "Draft", "submitted": "Submitted", "paid": "Paid"}, "device-status": {"active": "Active", "inactive": "Inactive", "maintenance": "Maintenance", "error": "Error"}}, "fileSize": {"bytes": "B", "kilobytes": "KB", "megabytes": "MB", "gigabytes": "GB"}, "toast": {"success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "validation-error": "Invalid field value"}, "success": {"generic": "Operation completed successfully"}, "error": {"generic": "An error occurred. Please try again.", "loadingFailed": "Loading failed"}, "preview": {"unsupported": {"title": "Preview not available", "description": "This file type cannot be previewed. Please download the file to view it."}, "error": {"title": "Error loading preview", "description": "There was an error loading the file preview. Please try downloading the file instead."}, "pdf": {"fallback": "Your browser doesn't support PDF preview"}, "text": {"empty": "No content to display"}}, "currencyConfig": {"desktop": {"currency": "JOD", "symbol": "JOD"}, "mobile": {"currency": "JOD", "symbol": "JOD"}}, "true": "True", "false": "False", "created-at": "Created At", "updated-at": "Updated At", "export": {"export": "Export", "exporting": "Exporting...", "csv": "CSV", "pdf": "PDF", "xlsx": "Excel", "csvDescription": "Comma-separated values for data analysis", "pdfDescription": "Formatted document for reports", "xlsxDescription": "Excel spreadsheet for analysis", "exportComplete": "Export completed successfully", "exportFailed": "Export failed", "exportingFormat": "Exporting {format}...", "title": "Export File", "steps": {"selectFormat": "Select Format", "selectFields": "Select Fields"}, "chooseFormat": "Choose File Format", "continue": "Continue", "selectedColumns": "Selected", "of": "of", "columns": "columns", "selectAll": "Select All", "deselectAll": "Deselect All", "required": "Required", "exportingAs": "Exporting as", "dragToReorder": "Drag to reorder fields"}}, "auth": {"login": {"title": "Welcome 👋", "description": "Enter your username and password to continue.", "buttons": {"login": "<PERSON><PERSON>", "forgotPass": "Forgot Password?"}}, "forgotPass": {"title": "Forgot Password", "description": "Enter your email to send a link to reset your password.", "buttons": {"resetPass": "Reset Password"}}, "resetPass": {"title": "Set a New Password", "description": "The new password must be different from the passwords you have used before.", "buttons": {"resetPass": "Reset Password", "login": "<PERSON><PERSON>"}}, "checkEmail": {"title": "Check your inbox", "description": "We have sent a password reset <NAME_EMAIL>", "buttons": {"goLogin": "Back to Login", "editEmail": "<PERSON> Email"}}, "verificationCode": {"title": "Verify your code", "description": "Enter the verification code sent to your email to reset your password.", "buttons": {"verify": "Verify"}}, "passwordUpdated": {"title": "You're all set", "description": "Your password has been successfully updated, you are now logged in.", "buttons": {"login": "<PERSON><PERSON>"}}, "selectSystem": {"headerTitle": "Select a system to continue.", "headerLabel": "Select a system from the list to continue. This option is required.", "people": {"title": "AtharPeople", "description": "Employee Affairs System", "alt": "Icon representing human resources management"}, "procure": {"title": "AtharProcure", "description": "Procurement Management System", "alt": "Icon representing financial management"}, "cm": {"title": "AtharCaseManager", "description": "Case Management System", "alt": "Icon illustrating mental health care system"}, "noSystems": "No systems available for selection."}, "sessionExpired": {"title": "Session Expired", "description": "Your session has expired for security reasons. Please log in again to continue using the application.", "securityNotice": {"title": "Security Notice", "description": "For your protection, you'll need to authenticate again to access your account."}, "buttons": {"continueToLogin": "Continue to Login"}}}, "settings": {"title": "Settings", "global": {"title": "General", "buttons": {"profile": {"change-image": "Change Picture", "delete": "Delete"}}}, "notifications": {"title": "Notifications", "muteSound": "Mute sound", "disableNotificationSound": "Disable notification sound"}, "holidays": {"title": "Holidays", "calendar": {"title": "Holiday Calendar", "add-holiday": "Add Holiday", "no-holidays": "No holidays on this date"}, "form": {"add-title": "Add New Holiday", "add-description": "Select the date and specify the holiday name and type", "edit-title": "Edit Holiday", "edit-description": "Edit holiday details", "add-button": "Add", "update-button": "Update", "cancel-button": "Cancel", "name": {"label": "Holiday Name", "placeholder": "Enter holiday name"}, "start-date": {"label": "Start Date", "placeholder": "Select start date"}, "end-date": {"label": "End Date", "placeholder": "Select end date"}, "date": {"label": "Select Days", "placeholder": "Choose holiday date"}, "dates": {"label": "Select Holiday Dates", "placeholder": "Choose multiple holiday dates", "modal-title": "Select Holiday Dates", "modal-description": "Choose the start and end dates for this holiday", "submit-button": "Confirm Dates"}, "type": {"label": "Holiday Type", "placeholder": "Select holiday type"}}, "types": {"national": "National Holiday", "religious": "Religious Holiday", "company": "Company Holiday", "other": "Other"}, "details": {"title": "Holiday Details", "name": "Holiday Name", "date": "Date", "dates": "Dates"}, "delete-dialog": {"title": "Delete Holiday", "delete": "Delete", "deleting": "Deleting...", "cancel": "Cancel", "success-message": "Holiday deleted successfully", "description": " هل أنت متأكد من حذف {itemName} ؟ لا يمكن التراجع عن هذا الإجراء.]"}, "messages": {"added": "Holiday added successfully", "added-description": "New holiday has been added to the calendar", "updated": "Holiday updated successfully", "updated-description": "Holiday details have been updated", "error": "An error occurred while saving the holiday"}}, "buttons": {"update": "Update", "back": "back"}, "editProfile": {"mesaages": {"edit": "Please edit at least one field before submitting."}}}, "buttons": {"update": "Update", "back": "Back"}, "editProfile": {"messages": {"edit": "Please edit at least one field before submitting."}}, "people": {"attendance-events-page": {"create": {"title": "Create Attendance Event", "description": "Add a new attendance event for an employee"}, "edit": {"title": "Edit Attendance Event", "description": "Update the attendance event details"}, "table": {"title": "Attendance Events", "columns": {"id": "ID", "employee": "Employee", "employeeName": "Employee Name", "timestamp": "Date & Time", "event_type": "Event Type", "activity_type": "Activity Type", "location": "Location", "notes": "Notes", "inferred_type": "Inferred Type", "created_at": "Created At", "updated_at": "Updated At", "actions": "Actions", "requestDate": ":تاريخ الطلب", "status": ":الحالة"}, "status": {"check_in": "Check In", "check_out": "Check Out"}, "actions": {"view": "View Details", "edit": "Edit", "delete": "Delete"}}, "edit-modal": {"title": "Edit Attendance Event", "description": "Update the attendance event details below.", "save": "Save Changes"}, "view": {"title": "Attendance Event Details", "description": "View attendance event information", "employee-info": "Employee Information", "event-details": "Event Details", "metadata": "Record Information", "email": "Email"}, "delete": {"title": "Delete Attendance Event", "description": "Remove attendance event from records", "confirm-title": "Are you sure?", "confirm-message": "This action cannot be undone. The attendance event will be permanently deleted."}, "create-modal": {"datetime-title": "Select Date & Time", "datetime-description": "Choose when this attendance event occurred."}, "datetime-modal": {"title": "Select Date & Time", "description": "Choose the date and time for the attendance event.", "date": "Date", "time": "Time", "selected": "Selected Date & Time"}, "fields": {"employee": "Employee", "employee-placeholder": "Select employee", "event_type": "Event Type", "activity_type": "Activity Type", "location": "Location", "timestamp": "Date & Time", "note": "Notes", "note-placeholder": "Add any additional notes...", "activity_types": {"regular_time": "Regular Time", "overtime": "Overtime", "break": "Break"}, "locations": {"office": "Office", "remote": "Remote", "client_site": "Client Site"}}, "timestamp": "Date & Time", "select-dates": "Select date"}, "salary-calculation": {"calculate-period": {"title": "Calculate Salary Period", "description": "Select a month to calculate salaries for that period", "button": "Calculate Month", "select-month": "Select Month", "placeholder": "Select a month", "help-text": "The calculation will be processed for the selected month"}, "no-permission": "You don't have permission to calculate salary periods"}, "card": {"totalEmployees": {"title": "Number of Employees", "subTitle": "employees", "detail": "Total number of employees in the company."}, "attendanceRate": {"title": "Daily Attendance Rate", "subTitle": "employees", "detail": "Number of employees present today relative to the total."}, "averageDailyHours": {"title": "Average Daily Working Hours", "subTitle": "hours", "detail": "Average number of working hours per employee."}, "pendingRequests": {"title": "Pending Requests", "subTitle": "requests", "detail": "Number of leave or overtime requests awaiting review."}}, "daily-attendance-chart": {"daily-attendance-title": "Daily Attendance Percentage Throughout the Month", "attendance-percentage": "Attendance Percentage"}, "distribution-leaves-chart": {"title": "Distribution of Leaves by Type.", "tooltipFormatter": {"count": "Number of Leaves"}, "leave-distribution-xAxis-data": {"Annual": "Annual", "Sick": "Sick", "Marriage": "Marriage", "Maternity": "Maternity", "Paternity": "Paternity", "Unpaid": "Unpaid"}}, "salary-package": {"create": {"title": "Create Salary Package", "description": "Fill in the details to create a new salary package for this employee.", "button": "Create Salary Package"}, "update": {"title": "Update Salary Package", "description": "Update the salary package details for this employee.", "button": "Edit Salary Package", "button2": "Edit Package"}, "current-package": "Current Salary Package", "total-package": "Total Package", "effective-period": "Effective Period", "ongoing": "Ongoing", "no-package": "There is no salary package yet. You can add a new package.", "no-package-description": "You can specify the basic salary, allowances, and effective date.", "no-permission": "You don't have permission to manage salary packages", "history": {"title": "Salary Package History", "no-history": "No salary package history available.", "columns": {"period": "Period", "total": "Total"}}, "changes-log": {"title": "Changes Log", "button": "Changes Log", "no-changes": "No salary changes available.", "total-salary": "Total Salary"}, "status": {"draft": "Draft", "pending_approval": "Pending Approval", "approved": "Approved", "active": "Active", "rejected": "Rejected", "cancelled": "Cancelled"}, "cancel-dialog": {"title": "Cancel Salary Package", "description": "Are you sure you want to cancel {itemName}? This action cannot be undone."}}, "employees-page": {"index": {"title": "Manage employees and profiles efficiently"}, "table": {"title": "All Employees", "add-employee-btn": "New Employee", "columns": {"employeeName": "Employee Name", "jobTitle": "Job Title", "start_date": "Registration Date", "department": "Department", "actions": "Actions"}, "actions": {"Action1": "View Profile"}}, "profile": {"send-email": "Send Email", "email": {"tooltip": {"with-email": "Send email to {email}", "no-email": "No email available for this employee"}}, "stats": {"late-arrival": "Late Arrival", "late-leave": "Late Leave", "vacation-days": "Vacation Days", "absence": "Absence", "compared-to-last-month": "compared to last month", "leave-requests": "Leave Requests", "approved-leaves": "Approved Leaves", "rejected-leaves": "Rejected Leaves", "pending-leaves": "Pending Leaves", "overtime-requests": "Overtime Requests", "approved-overtime": "Approved Overtime", "rejected-overtime": "Rejected Overtime", "pending-overtime": "Pending Overtime", "absences": "Absences", "average_daily_work_hours": "Average Daily Work Hours", "daily_attendance_rate": "Daily Attendance Rate", "early_departures": "Early Departures", "late_arrivals": "Late Arrivals", "leaves": "Leaves", "pending_requests": "Pending Requests", "total_employees": "Total Employees", "work_hours": "Work Hours"}, "tabs": {"attendance": "Attendance", "attendance-log": "Attendance Log", "salary": "Salary", "attachments": "Attachments", "leaves": "Leaves", "overtime": "Overtime", "roles-projects": "Projects & Roles", "salary-package": "Salary Package"}, "attendance": {"title": "Attendance & Leave Records", "description": "View detailed attendance and leave records for this employee.", "timeCard": {"checkIn": "Check In", "checkOut": "Check Out", "period": "Total Time", "hour": "hour", "today": "today", "periodKeys": {"earlyArrival": "Early Arrival", "break": "Break", "late": "Late", "regular": "Regular", "lunch": "Lunch", "meeting": "Meeting", "business_trip": "Business Trip", "work_from_home": "Work From Home", "remote": "Remote", "training": "Training", "default": "Vacation"}}, "table": {"title": "Attendance log"}}, "attendance-log": {"update-dates": {"title": "Update Date Range", "description": "Select the range of dates you want to update.", "update-button": "Update", "errors": {"dates-required": "Both start and end dates are required.", "past-date": "The start date cannot be in the past.", "invalid-range": "Please select a valid range.", "start-after-end": "Start date must be before end date.", "half-day-single-day": "Half-day leave must be for a single day.", "marriage-no-half-day": "This leave type cannot be taken as half-day."}, "selected-range": "Selected Date Range", "start-date": "Start Date", "end-date": "End Date", "none": "No date selected", "time": ":الوقت", "start-after-end": ":يجب أن يكون تاريخ البدء قبل تاريخ الانتهاء.", "half-day-single-day": ":يجب أن يكون الإجازة نصف اليوم ليوم واحد فقط.", "marriage-no-half-day": ":لا يمكن أخذ هذا النوع من الإجازة بنصف يوم]"}, "create-attendance-modal": {"select-dates-button": "Select Dates", "select-dates": "Select dates", "select-dates-description": "Select the date range for attendance registration.", "title": "New Attendance", "description": "Enter attendance details for this employee.", "submit": "Submit", "timestamp": "Timestamp", "success": ":تم إضافة تعيين حضور بنجاح"}, "placeholder": {"note": "Note"}, "fields": {"activity_type": "Activity Type", "timestamp": "Timestamp", "note": "Note", "employee_id": "Employee", "event_type": "Event Type", "location": "Location"}, "attendance": {"check_in": "Check In", "check_out": "Check Out", "regular": "Regular", "lunch_break": "Lunch Break", "overtime": "Overtime", "vacation": "Vacation", "sick_leave": "Sick Leave", "work_from_home": "Work From Home", "meeting": "Meeting", "office": "Office", "remote": "Remote", "site": "Site"}, "errors": {"type-required": ":ير<PERSON>ى اختيار نوع النشاط.", "timestamp-required": ":ير<PERSON>ى تحديد التاريخ والوقت.", "note-too-long": ":الملاحظة لا يمكن أن تتجاوز 500 حرفاً]"}}, "errors": {"type-required": "Please select an activity type.", "timestamp-required": "Please provide a timestamp.", "note-too-long": "Note cannot exceed 500 characters."}, "salary-history": {"title": "Salary History", "all-salaries": "All Employee Salaries", "month": "Month", "total-hours": "Total Hours", "total-salary": "Total Salary", "status": "Status", "payment-date": "Payment Date", "not-available": "Not Available", "view-details": "View Details", "no-permission": "You don't have permission to view salary history", "details-dialog": {"title": "Salary Details", "description": "View detailed information about this salary payment."}, "columns": {"month": "Month", "totalHours": "Total Hours", "totalSalary": "Total Salary", "status": "Status", "paymentDate": "Payment Date", "actions": "Actions", "select": "Select"}}, "attachments": {"title": "Attachments", "description": "View and manage employee documents and attachments.", "error": "Failed to load attachments. Please try again.", "add-button": "Add Document", "empty": {"title": "No Documents Found", "description": "This employee doesn't have any documents yet."}, "actions": {"view": "View", "rename": "<PERSON><PERSON>"}, "add-dialog": {"title": "Add Document", "description": "Upload a new document for this employee"}, "rename-dialog": {"title": "<PERSON><PERSON>", "placeholder": "Enter new name"}, "delete-dialog": {"title": "Delete", "description": "Are you sure you want to delete {fileName}? This action cannot be undone."}}, "leaves": {"title": "Leave Requests", "description": "View and manage leave requests for this employee.", "table": {"title": "All Leave Requests", "columns": {"requestDate": "Request Date", "leaveType": "Leave Type", "approvalWorkflow": "ِApproval status", "dateRange": "Start & End Date", "status": "Status", "actions": "Actions"}, "leave-types": {"annual": "Annual Leave", "sick": "Sick Leave", "marriage": "Marriage Leave", "maternity": "Maternity Leave", "paternity": "Paternity Leave", "unpaid": "Unpaid Leave"}, "leave-durations": {"full_day": "Full Day", "half_day_morning": "Half Day (Morning)", "half_day_afternoon": "Half Day (Afternoon)"}, "actions": {"withdraw": "Withdraw Request"}}, "update-dates": {"title": "Update Leave Dates", "description": "Select new start and end dates for your leave request.", "updating": "Updating...", "update-button": "Update Dates", "selected-range": "Selected Date Range", "start-date": "Start Date", "end-date": "End Date", "none": "None", "errors": {"dates-required": "Both start and end dates are required", "past-date": "Start date cannot be in the past"}}, "create-leave": "Create Leave", "create-leave-modal": {"title": "Create Leave Request", "description": "Fill in the details to create a new leave request.", "submit": "Submit Request", "success": "Leave request created successfully", "error": "Failed to create leave request", "leave-type": "Leave Type", "leave-duration": "Leave Duration", "select-leave-type": "Select leave type", "select-leave-duration": "Select leave duration", "start-date": "Start Date", "end-date": "End Date", "reason": "Reason", "reason-placeholder": "Enter reason for leave request", "attachments": "Attachments", "attachments-placeholder": "Upload supporting documents", "upload-attachments": "Upload Attachments", "select-dates": "Select Leave Dates", "select-dates-description": "Choose the start and end dates for your leave request.", "select-dates-button": "Confirm Dates"}, "types": {"annual": "Annual Leave", "sick": "Sick Leave", "unpaid": "Unpaid Leave", "other": "Other Leave"}}, "overtime": {"title": "Overtime Records", "description": "View overtime records and requests for this employee."}, "roles-projects": {"table": {"title": "Roles & Projects", "description": "View and manage roles and projects for this employee.", "empty": {"title": "No projects", "description": "This employee doesn't have any projects or roles yet."}, "columns": {"projectName": "Project Name", "role": "Role", "joinDate": "Join Date", "actions": "Actions"}}, "delete": {"title": "Delete Role Assignment", "confirm": "Are you sure you want to delete this role assignment?", "success": "Role and project assignment deleted successfully", "error": "Failed to delete role and project assignment"}, "add": {"success": "Role and project assignment added successfully", "error": "Failed to add role and project assignment"}, "add-role-project": "Add Role & Project", "add-dialog": {"title": "Add Role & Project", "description": "Assign a new role and project to this employee"}, "add-global-role": {"success": "Role assignment added successfully", "error": "Failed to add role assignment"}, "form": {"role": {"label": "Role", "placeholder": "Select a role", "required": "Role is required"}, "project": {"label": "Project", "placeholder": "Select a project", "required": "Project is required"}, "submit": "Add Assignment"}}, "projects-roles": ":المشاريع والأدوار"}, "add-employee-dialog": {"header": {"description": "This screen displays the form for adding new employee. Please enter the new employee, then click add to update the data.", "title": "Add New Employee"}, "form": {"employee-name": {"label": "Employee Name", "placeholder": "Enter employee name"}, "email": {"label": "Email Address", "placeholder": "Enter email address"}, "mobile": {"label": "Mobile Number", "placeholder": "Enter mobile number"}, "registration-date": {"label": "Join Date", "placeholder": "Select join date"}, "department": {"label": "Department", "placeholder": "Select department"}, "assignments": {"label": "Assignments"}, "job-title": {"label": "Job Title", "placeholder": "Select job title"}, "profile-image": {"placeholder": "Upload profile image"}, "attachments": {"label": "Employee Attachments", "placeholder": "Upload employee documents", "button": "Upload Files", "upload": "Upload Employee Attachments"}, "departments": {"admin": "Administration", "hr": "Human Resources", "it": "Information Technology", "finance": "Finance", "operations": "Operations", "programs": "Programs", "procurement": "Procurement"}, "projects": {"project1": "Project 1", "project2": "Project 2", "project3": "Project 3", "project4": "Project 4", "project5": "Project 5"}, "roles": {"manager": "Project Manager", "developer": "Developer", "designer": "Designer", "analyst": "Analyst", "coordinator": "Coordinator", "consultant": "Consultant"}, "jobTitles": {"manager": "Manager", "developer": "Developer", "designer": "Designer", "analyst": "Analyst", "coordinator": "Coordinator"}, "success": "Employee added successfully", "error": "Failed to add employee"}}, "edit-employee-dialog": {"header": {"title": "Edit Employee", "description": "Update employee profile information including name, email, phone, and profile image."}}, "salary": {"title": "Salary Information", "description": "View salary history and payment details for this employee.", "moved-message": "The salary table has been moved to the Salary Package tab for better organization.", "table": {"title": "Salary History", "columns": {"period": "Period", "grossSalary": "Gross Salary", "totalDeductions": "Total Deductions", "netSalary": "Net Salary", "totalHours": "Total Hours", "status": "Status", "actions": "Actions"}, "deductions": {"title": "Deduction Details", "leave_deductions": "Leave Deductions", "salary_advances": "Salary Advances", "other_deductions": "Other Deductions", "medical_insurance": "Medical Insurance", "income_tax": "Income Tax", "employee_social_security": "Employee Social Security", "total": "Total Deductions"}, "actions": {"submit": "Submit", "approve": "Approve", "reject": "Reject", "pay": "Pay", "view-details": "View Details", "edit-note": "Edit Note", "submit-success": "Salary submitted successfully", "submit-error": "Failed to submit salary", "approve-success": "Salary approved successfully", "approve-error": "Failed to approve salary", "reject-success": "Salary rejected successfully", "reject-error": "Failed to reject salary", "pay-success": "Salary paid successfully", "pay-error": "Failed to pay salary"}}, "slip": {"preview": "Preview Slip", "download": "Download Slip", "downloading": "Downloading...", "regenerate": "Regenerate Slip", "regenerating": "Regenerating...", "preview-error": "Failed to preview salary slip", "download-success": "Salary slip downloaded successfully", "download-error": "Failed to download salary slip", "regenerate-success": "Slip regenerated successfully", "regenerate-error": "Failed to regenerate slip"}, "calculation-details": {"title": "Calculation Breakdown", "no-details": "No detailed calculation information available", "net-salary": "Net Salary", "types": {"base": "Base Components", "deduction": "Deductions", "allowance": "Allowances", "bonus": "Bonuses"}}}}, "leaves-requests-component": {"title": "Latest Requests", "allRequests": "All Requests", "tabs": {"all": "All", "leave": "Leaves", "overtime": "Overtime"}, "actions": {"accept": "Accept", "approve": "Approve", "reject": "Reject", "writeNote": "Write a note", "approve-success": ":تمت الموافقة على طلب الإجازة بنجاح", "approve-error": ":فشل في الموافقة على طلب الإجازة", "reject-success": ":تم رفض طلب الإجازة بنجاح", "reject-error": ":فشل في رفض طلب الإجازة", "approve-final-success": ":تمت الموافقة النهائية على طلب الإجاز"}, "badge": {"accepted": "Accepted", "approved": "Approved", "rejected": "Rejected"}, "leaves-requests": {"categories": {"leave": "Leave", "overtime": "Overtime", "annual": "Annual Leave", "sick": "Sick Leave", "marriage": "Marriage Leave", "maternity": "Maternity Leave", "paternity": "Paternity Leave", "unpaid": "Unpaid Leave"}}, "noRequests": "No requests"}, "leaves-requests-page": {"table": {"header": {"title": "Requests Record"}, "title": "All Employee Requests", "columns": {"number": "Number", "employee_name": "Employee Name", "leave_type": "Request Type", "start_date": "Start Date", "end_date": "End Date", "status": "Status", "actions": "Actions"}}, "actions": {"Action1": "Show Details", "accept": "Accept", "reject": "Reject", "accepted": "Accepted", "rejected": "Rejected", "approve-success": "Leave request approved successfully.", "approve-error": "Failed to approve leave request.", "reject-success": "Leave request rejected successfully.", "reject-error": "Failed to reject leave request.", "withdraw-success": "Leave request withdrawn successfully.", "withdraw-error": "Failed to withdraw leave request.", "approve-final-success": "Leave request fully approved.", "alreadyApproved": "You have already approved this request", "alreadyRejected": "You have already rejected this request", "notAuthorized": "You are not authorized to perform this action"}, "request-details": {"title": "Request Details", "description": "Request details and conditions for approval or rejection", "employee-name": "Employee Name:", "request-type": "Request Type:", "start-date": "Start Date:", "end-date": "End Date:", "request-reason": "Reason for Request:", "attachments": "Attachments:", "note": "Note:", "default-note": "Please provide feedback or additional comments regarding this leave request.", "no-notes": "No notes available", "no-attachments": "No attachments available", "approval-status": "Request Status", "approval-time": "Approval Time", "rejection-time": "Rejection Time", "approval-workflow": {"approved": "Approved", "rejected": "Rejected", "pending": "Pending Approval...", "no-steps": "No approval steps available", "title": "Request Approval Details"}, "no-reason": ":لم يتم تقديم سبب", "steps": {"direct-manager": ":المدير المباشر", "hr-manager": ":مدير الموارد البشرية", "executive-manager": ":الرئيس التنفيذي", "general-manager": ":المدير العام"}}, "dialog-common": {"note-label": "Note", "note-placeholder": "Write a note on the request"}, "employee-note-dialog": {"header-title": "Note on Request/Leave"}, "employee-approval-dialog": {"title": "Request Approved!", "description": "The request has been successfully approved.", "note-hint": "Optional"}}, "devices-page": {"title": "Devices", "table": {"title": "All Devices", "add-device-btn": "Add <PERSON>", "search-placeholder": "Search devices...", "columns": {"name": "Device Name", "ip_address": "IP Address", "adapter_type": "Device Type", "location": "Location", "port": "Port Number", "created_at": "Date Added", "status": "Status", "health_score": "Health Score", "last_seen_at": "Last Seen", "actions": "Actions", "activityType": ":نوع النشاط", "employeeName": ":اسم الموظف", "requestDate": ":تاريخ الطلب", "updated_at": ":تاريخ التحديث"}, "status": {"active": "Active", "inactive": "Inactive", "unknown": "Unknown", "check_in": ":دخول", "check_out": ":<PERSON><PERSON><PERSON><PERSON>", "maintenance": ":صيانة", "error": ":خ<PERSON>أ"}, "actions": {"view": "View", "edit": "Edit", "delete": "Delete"}, "empty-state": {"title": "No devices found", "description": "No devices have been added yet."}, "error-loading": "Error loading devices", "no-data": "No devices found"}, "view-toggle": {"table": "Table View", "cards": "Card View"}, "add-device-dialog": {"header": {"title": "Add New Device", "description": "Add a new device to the system"}, "form": {"device-name": {"label": "Device Name", "placeholder": "Enter device name", "error": {"required": "Device name is required"}}, "device-type": {"label": "Device Type", "placeholder": "Select device type", "error": {"required": "Device type is required"}}, "ip-address": {"label": "IP Address", "placeholder": "*************", "error": {"required": "IP address is required", "invalid": "Please enter a valid IP address"}}, "port": {"label": "Port Number", "placeholder": "4370", "error": {"required": "Port number is required", "invalid": "Port must be a number", "range": "Port must be between 1 and 65535"}}, "location": {"label": "Location", "placeholder": "Select location", "error": {"required": "Location is required"}}, "add-other-settings": "Add Other Settings", "new-settings-title": "New Settings", "settings-type-label": "Settings Type", "settings-type-placeholder": "Select settings type", "settings-value-label": "Settings", "settings-value-placeholder": "Enter value"}}, "edit-device-dialog": {"header": {"title": "<PERSON>", "description": "Update device information"}}, "device-details": {"error-loading": "Failed to load device details", "status": "Status", "creation-date": "Creation Date", "port-number": "Port Number", "ip-address": "IP Address", "fingerprint-status": "Fingerprint Status", "fingerprint-enabled": "Enabled", "fingerprint-disabled": "Disabled", "connection-status": {"online": "Online", "offline": "Offline", "unknown": "Unknown"}, "tabs": {"commands": "Commands & Procedures", "users": "Users List"}, "metrics": {"ip-address": "IP Address", "port": "Port Number", "created-date": "Created Date", "status": "Status"}}, "device-commands": {"form": {"command": {"label": "Command", "placeholder": "Select command", "no-commands": "No commands available for this device", "error": {"required": "Command is required", "loading": "Failed to load available commands"}}, "parameters": {"title": "Parameters", "item-title": "New Parameter", "key": {"label": "Key", "placeholder": "Enter key", "error": {"required": "Key is required"}}, "value": {"label": "Value", "placeholder": "Enter value", "error": {"required": "Value is required"}}, "add-button": "Add Another Parameter"}, "execute": "Execute", "executing": "Executing...", "result": {"title": "Result", "placeholder": "Command execution result will appear here...", "executing": "Executing command..."}}}, "users-table": {"title": "Device Users", "columns": {"userName": "User Name", "userId": "System Code", "deviceUserId": "Device Code", "joinDate": "Join Date", "actions": "Actions"}, "actions": {"fixCode": "Fix Code"}, "fix-code-dialog": {"title": "Fix Code", "deviceCode": "Device Code", "systemCode": "System Code", "cancel": "Cancel", "confirm": "Confirm"}}, "delete-dialog": {"title": "Are you sure you want to delete the device?", "description": "The device will be permanently deleted from the system and cannot be recovered. Make sure this action is required before proceeding.", "device-name-label": "Device Name", "device-name-placeholder": "Enter device name", "cancel": "Cancel", "delete": "Delete", "deleting": "Deleting...", "success-message": "<PERSON>ce deleted successfully"}}, "employees-salaries-page": {"table": {"title": "All employees' salaries", "employee-title": "Salary history for", "columns": {"employeeName": "Employee Name", "period": "Period", "month": "Month", "grossSalary": "Gross Salary", "totalDeductions": "Total Deductions", "netSalary": "Net Salary", "totalHours": "Total Hours", "totalSalary": "Total Salary", "approvalWorkflow": "Approval Status", "status": "Status", "paymentDate": "Payment Date", "actions": "Actions"}, "actions": {"view-details": "View Details", "edit-note": "Edit Note", "submit": "Submit", "approve": "Approve", "reject": "Reject"}, "deductions": {"title": "Deduction Details", "leave_deductions": "Leave Deductions", "salary_advances": "Salary Advances", "other_deductions": "Other Deductions", "medical_insurance": "Medical Insurance", "income_tax": "Income Tax", "employee_social_security": "Employee Social Security", "total": "Total Deductions"}, "toast": {"submit-success": "Salary submitted successfully", "submit-error": "Failed to submit salary", "approve-success": "Salary approved successfully", "approve-error": "Failed to approve salary", "reject-success": "Salary rejected successfully", "reject-error": "Failed to reject salary"}}, "edit-salary-dialog": {"header": {"description": "This screen displays the form for editing the total salary. Please enter the new value and notes, then click Confirm to update the data.", "title": "Edit Total Salary"}, "content": {"input-label": "Total Salary", "note-placeholder": "Enter a note on the salary"}}, "employee-salary-dialog": {"header": {"title": "Confirm Payment", "description": "Payment details for this month are available here."}, "info": {"clientNameLabel": "Client Name", "monthLabel": "Month", "leavesLabel": "Number of Leaves"}, "data": {"workHoursLabel": "Total Work Hours", "overtimeLabel": "Total Overtime Hours", "hourlyRateLabel": "Hourly Rate", "discountLabel": "Total Discount", "taxLabel": "Tax"}, "total": {"label": "Total"}}}, "orders-percentage-over-year-chart": {"title": "Orders Percentage Over the Year", "tooltip": {"title": "Orders Percentage"}}}, "procure": {"HomePage": {"cards": {"request": "Request"}, "approvals": {"title": "Latest Request Approvals", "viewAll": "All Requests", "itemTitle": "Lenovo Laptop", "itemCategory": "Electronics"}, "items": {"title": "Highest Cost Items", "viewAll": "All Items", "currencySuffix": "JOD", "orderSuffix": "Order"}, "table": {"header": "Latest Note For Rejected Requests", "button": "Edit", "columns": {"id": "order number", "itemName": "element name ", "rejectedBy": "Rejected By", "reason": "Reason", "actions": "action"}}}}, "cm": {"card": {"appointments": "Appointments", "patients": "Total Beneficiaries", "videoConsultations": "Video Consultations"}, "table": {"title": "All Appointments", "columns": {"number": "Number", "beneficiaryName": "Beneficiary Name", "time": "Time", "description": "Description", "status": "Status", "actions": "Actions"}, "actions": {"Action1": "Show Details"}}, "cases": {"table": {"title": "All Cases", "columns": {"number": "Number", "caseNumber": "Case Number", "caseName": "Case Name", "age": "Age", "gender": "Gender", "registrationDate": "Registration Date", "userId": "User ID", "startDate": "Start Date", "department": "Department", "departmentName": "Department Name", "status": "Status", "actions": "Actions"}, "actions": {"view": "View Details", "edit": "Edit", "delete": "Delete"}}, "actions": {"createCase": "Create Case"}}, "appointmentchart": {"appointments": "Appointments", "selectMode": "Select Mode", "monthly": "Monthly", "yearly": "Yearly", "confirmed": "Confirmed", "canceled": "Canceled", "january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December", "annual": "Annual"}, "caseManagerHomePage": {"charts": {"titles": {"cases": "الحالات", "group-cases": "حالات مجموعتي", "shared-with-others": "تم تشاركها مع الاخرين"}}, "placeholder": {"yearly": "سنوي"}}, "appointment-calendar": {"availabilitySelectTitle": "Select your available times", "your-availability-btn": "Your availability times", "not-available": "Not-available", "dialog-description": "Please select the times you are available for scheduling appointments."}}, "projects": {"page": {"title": "Projects", "description": "Manage and organize your projects", "table": {"title": "All Projects", "empty": {"title": "No projects found", "description": "Create your first project to get started"}, "columns": {"id": "ID", "name": "Project Name", "description": "Description", "status": "Status", "actions": "Actions"}, "status": {"active": "Active", "inactive": "Inactive"}, "actions": {"edit": "Edit", "delete": "Delete"}}, "create": {"title": "Create Project", "success": "Project created successfully", "error": "Failed to create project", "description": "Add a new project to the system", "dialog": {"title": "Create New Project", "description": "Add a new project to the system"}}, "update": {"title": "Update Project", "success": "Project updated successfully", "error": "Failed to update project", "description": "Update project information"}, "delete": {"title": "Delete Project", "success": "Project deleted successfully", "error": "Failed to delete project", "confirm": "Are you sure you want to delete this project?"}, "form": {"name": {"label": "Project Name", "placeholder": "Enter project name", "required": "Project name is required"}, "description": {"label": "Description", "placeholder": "Enter project description"}, "status": {"label": "Status", "placeholder": "Select status", "active": "Active", "inactive": "Inactive"}}}}}