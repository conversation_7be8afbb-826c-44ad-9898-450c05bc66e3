import useSWR, { mutate, SWRConfiguration } from "swr";

interface MutationConfig<T, K, P = undefined> {
  updateFn: (data: T | undefined, key: K, payload?: P) => T | undefined;
  mutationFn: (key: K, payload?: P) => Promise<{ data?: T; error?: Error }>; // Return updated data or error
}

interface OptimisticMutationConfig<T> extends SWRConfiguration<T> {
  revalidateAfterMutation?: boolean;
}

interface OptimisticMutationResponse<T, K, P = undefined> {
  data: T;
  error: Error | null;
  isLoading: boolean;
  mutateData: (
    mutationKey: string,
    mutationArg: K,
    payload?: P,
  ) => Promise<void>;
}

export function useOptimisticMutation<T, K = string, P = undefined>({
  key,
  fetcher,
  mutations,
  defaultData,
  swrOptions,
}: {
  key: string | ((args: any) => string); // Support dynamic keys
  fetcher: (key: string) => Promise<T>;
  mutations: Record<string, MutationConfig<T, K, P>>;
  defaultData?: T;
  swrOptions?: OptimisticMutationConfig<T>;
}): OptimisticMutationResponse<T, K, P> {
  const resolvedKey = typeof key === "function" ? key({}) : key; // Handle dynamic keys
  const { data, error, isLoading } = useSWR<T>(resolvedKey, fetcher, {
    revalidateOnFocus: false,
    ...swrOptions,
  });

  const mutateData = async (
    mutationKey: string,
    mutationArg: K,
    payload?: P,
  ) => {
    const { updateFn, mutationFn } = mutations[mutationKey];
    try {
      await mutate(
        resolvedKey,
        async (currentData: T | undefined) => {
          const optimisticData = updateFn(currentData, mutationArg, payload);
          const serverResponse = await mutationFn(mutationArg, payload);
          if (serverResponse.error) throw serverResponse.error;
          // Use server response if available, otherwise fall back to optimistic data
          return serverResponse.data ?? optimisticData;
        },
        {
          optimisticData: (currentData) =>
            updateFn(currentData, mutationArg, payload),
          rollbackOnError: true,
          revalidate: false,
        },
      );
      if (swrOptions?.revalidateAfterMutation !== false) {
        await mutate(resolvedKey);
      }
    } catch (err) {
      console.error(`Mutation "${mutationKey}" failed:`, err);
      throw err;
    }
  };

  return {
    data: data ?? defaultData ?? ({} as T),
    error,
    isLoading,
    mutateData,
  };
}
