import useSWR from "swr";
import { fetcher } from "@/services/fetcher";
import { FormSubmissionsResponse } from "@/types/cm";

type UseFormSubmissionsParams = {
  caseId?: string | number;
  formSectionId?: string | number;
};

export const useFormSubmissions = ({
  caseId,
  formSectionId,
}: UseFormSubmissionsParams = {}) => {
  // Build URL with query parameters
  let apiUrl = "/api/cm/form-submissions";
  const params = new URLSearchParams();

  if (caseId) {
    params.append("case_id", caseId.toString());
  }

  if (formSectionId) {
    params.append("form_section_id", formSectionId.toString());
  }

  if (params.toString()) {
    apiUrl += `?${params.toString()}`;
  }

  console.log("🚀 ~ useFormSubmissions ~ apiUrl:", apiUrl);

  const { data, error, isLoading, mutate } = useSWR<FormSubmissionsResponse>(
    // Only fetch if at least one parameter is provided
    caseId ? apiUrl : null,
    fetcher,
  );

  return {
    submissions: data?.data || [],
    meta: data?.meta,
    isLoading,
    error,
    mutate,
  };
};
