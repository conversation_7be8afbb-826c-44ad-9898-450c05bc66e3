"use client";
import { Dialog, DialogClose, DialogContent } from "@/components/ui/dialog";
import { Drawer, DrawerClose, DrawerContent } from "@/components/ui/drawer";
import useMediaQuery from "@/hooks/use-media-query";
import { X } from "lucide-react";
import { ReactNode, useEffect } from "react";
import { ScrollArea } from "./ui/scroll-area";
import { cn } from "@/lib/utils";
import { useLocale } from "next-intl";
import { LANGUAGES } from "@/constants/enum";
import { Locale } from "@/i18n/routing";
import { useCenteredDraggable } from "@/hooks/useDraggable";

interface ResponsiveDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  closeBtnStyle?: string;
  children: ReactNode;
  header?: ReactNode;
  className?: string;
  dismissible?: boolean;
}

interface Position {
  x: number;
  y: number;
}

const ResponsiveDialog = ({
  open,
  onOpenChange,
  closeBtnStyle,
  children,
  header,
  className,
  dismissible = true,
}: ResponsiveDialogProps) => {
  const isSmallScreen = useMediaQuery("(max-width:768px)");
  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const { ref, isDragging, handleMouseDown, resetPosition } =
    useCenteredDraggable({
      enabled: !isSmallScreen,
      containWithinViewport: true,
    });
  useEffect(() => {
    if (!isSmallScreen) {
      resetPosition();
    }
  }, [open, isSmallScreen]);

  if (isSmallScreen) {
    return (
      <Drawer open={open} onOpenChange={onOpenChange} dismissible={dismissible}>
        <DrawerContent
          className={cn("!rounded-t-[20px] max-h-[97vh]", className)}
        >
          <DrawerClose
            className={cn(
              "absolute z-20 end-6 top-6 border rounded-lg w-fit text-[#727A90]",
              closeBtnStyle,
            )}
          >
            <X className="stroke-[1px]" />
          </DrawerClose>
          {header}
          <ScrollArea
            lang={locale}
            dir={isAr ? "rtl" : "ltr"}
            className="custom-scroll max-md:py-4 !px-2 flex-1"
          >
            {children}
          </ScrollArea>
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        ref={ref}
        className={cn(
          "!rounded-[20px] gap-0 !p-0 min-w-[561px] flex flex-col h-auto transition-none ",
          "top-1/2 left-1/2",
          className,
        )}
        style={{
          cursor: isDragging ? "grabbing" : "default",
        }}
        hideClose
      >
        <DialogClose
          className={cn(
            "absolute z-20 end-6 top-6 border rounded-lg text-[#727A90]",
            closeBtnStyle,
          )}
        >
          <X className="stroke-[1px] !w-7 !h-7" />
        </DialogClose>

        <div
          onMouseDown={handleMouseDown}
          className="cursor-grab active:cursor-grabbing select-none w-full"
          style={{ minHeight: header ? "auto" : "60px" }}
        >
          {header}
        </div>

        <ScrollArea className="custom-scroll flex-1 p-6">{children}</ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

export default ResponsiveDialog;
