import { NextRequest, NextResponse } from "next/server";
import { peopleService } from "@/services/api/people";
import { createErrorResponse } from "@/utils/api-error-extractor";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: "Missing salary calculation ID" },
        { status: 400 },
      );
    }

    try {
      const response = await peopleService.regenerateSalarySlip(id);
      return NextResponse.json(response);
    } catch (error) {
      console.error("Error regenerating salary slip:", error);
      throw error;
    }
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "POST regenerate slip route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
