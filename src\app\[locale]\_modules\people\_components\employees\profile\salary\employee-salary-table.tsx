"use client";

import { DataTable } from "@/components/table";
import React, { useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import { PaginationWithLinks } from "@/components/pagination-with-links";
import { RowSelectionState } from "@tanstack/react-table";
import { useSalaryMutations } from "../../../../hooks/useSalaryMutations";
import { employeeSalaryColumns } from "../../../shared/salary-columns";
import ApprovalWorkflowModal from "../../../approval-workflow-modal";
import { SalaryCalculation } from "../../../../type/employees-salaries";
import { useSearchParams } from "next/navigation";
import { ApprovalRequestData } from "../../../../type/approval-request";
import { TIncludedEmployee } from "../../../../type/employee-leaves";
import { useTableRegistration } from "@/hooks/useTableRegistration";

type EmployeeSalaryTableProps = {
  employeeId: string;
};

export const EmployeeSalaryTable = ({
  employeeId,
}: EmployeeSalaryTableProps) => {
  const t = useTranslations();
  const locale = useLocale();
  const searchParams = useSearchParams();
  const limit = searchParams.get("limit") ?? "5";
  const page = searchParams.get("page") ?? "1";
  const sort = searchParams.get("sort") ?? "-period_start_date";
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const {
    includedData,
    calculationDetailsData,
    salaryCalculations,
    totalCount,
    pagination,
    isLoading,
    error,
    submitSalary,
    approveSalary,
    rejectSalary,
    paySalary,
    regenerateSlip,
    isSubmitting,
    isApproving,
    isRejecting,
    isPaying,
    isRegenerating,
  } = useSalaryMutations({
    employeeId,
    page: Number(page),
    limit: Number(limit),
    sort,
    tableId: "employee-salary",
  });

  const [selectedSalaryForWorkflow, setSelectedSalaryForWorkflow] =
    useState<SalaryCalculation | null>(null);

  // Register table for filtering
  useTableRegistration("employee-salary", employeeSalaryColumns);

  // Handle submit salary (change from draft to submitted)
  const handleSubmitSalary = (salaryId: string) => {
    submitSalary(salaryId);
  };

  // Handle approve salary
  const handleApproveSalary = (salaryId: string) => {
    approveSalary(salaryId);
  };

  // Handle reject salary
  const handleRejectSalary = (salaryId: string) => {
    rejectSalary(salaryId);
  };

  const handleShowWorkflowModal = (salaryCalculation: SalaryCalculation) => {
    setSelectedSalaryForWorkflow(salaryCalculation);
  };

  // Handle pay salary
  const handlePaySalary = (salaryId: string) => {
    paySalary(salaryId);
  };

  // Handle regenerate slip
  const handleRegenerateSlip = (salaryId: string) => {
    regenerateSlip(salaryId);
  };

  return (
    <>
      <DataTable
        data={salaryCalculations}
        dataCount={totalCount}
        columns={employeeSalaryColumns}
        title={t("people.employees-page.salary.table.title")}
        meta={{
          t,
          locale: locale,
          onSubmitSalary: handleSubmitSalary,
          onApproveSalary: handleApproveSalary,
          onRejectSalary: handleRejectSalary,
          onPaySalary: handlePaySalary,
          onRegenerateSlip: handleRegenerateSlip,
          isSubmitting,
          isApproving,
          isRejecting,
          calculationDetailsData: calculationDetailsData,
          includedData: includedData,
          onShowWorkflowModal: handleShowWorkflowModal,
          isPaying,
          isRegenerating,
        }}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        translationPrefix="people.employees-page.salary.table"
        tableId="employee-profile-salary"
        isLoading={isLoading}
        initialLimit={5}
        error={error}
      />

      {/* Pagination */}
      <div className="w-full pt-[18px]">
        <PaginationWithLinks
          page={Number(page)}
          pageSize={Number(limit)}
          totalCount={pagination?.count || 0}
          firstLastCounts={{
            firstCount: pagination?.from || 1,
            lastCount: pagination?.to || 5,
          }}
          isLoading={isLoading}
          isDisabled={!salaryCalculations?.length}
          pageSizeSelectOptions={{
            pageSizeOptions: [5, 10, 25, 30, 45, 50],
            pageSizeSearchParam: "limit",
          }}
        />
      </div>

      {/* Approval Workflow Modal */}
      {selectedSalaryForWorkflow && (
        <ApprovalWorkflowModal
          isOpen={!!selectedSalaryForWorkflow}
          onClose={() => setSelectedSalaryForWorkflow(null)}
          data={selectedSalaryForWorkflow}
          included={
            (includedData as (ApprovalRequestData | TIncludedEmployee)[]) || []
          }
          type="salary"
        />
      )}
    </>
  );
};
