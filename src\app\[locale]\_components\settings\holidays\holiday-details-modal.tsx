"use client";

import React from "react";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";
import { TFunction } from "@/types";
import { Locale } from "@/i18n/routing";

import dynamic from "next/dynamic";
import Loader from "@/components/loader";
import { formatDate } from "@/lib/dateFormatter";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { DialogTitle } from "@/components/ui/dialog";
import { THoliday } from "@/types/settings/holidays";
import { Edit2, Trash } from "../../../../../../public/images/icons";

const ResponsiveDialog = dynamic(
  () => import("@/components/responsive-dialog"),
  {
    ssr: false,
    loading: () => <Loader overlayColor="#000" overlayOpacity={0.6} />,
  },
);

type HolidayDetailsModalProps = {
  holiday: THoliday | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: (holiday: THoliday) => void;
  onDelete?: (holiday: THoliday) => void;
};

export default function HolidayDetailsModal({
  holiday,
  isOpen,
  onClose,
  onEdit,
  onDelete,
}: HolidayDetailsModalProps) {
  const t = useTranslations() as TFunction;
  const locale: Locale = useLocale() as Locale;

  if (!holiday) return null;

  const formatHolidayDates = () => {
    if (
      holiday &&
      holiday.attributes.start_date &&
      holiday.attributes.end_date
    ) {
      const startDate = new Date(holiday.attributes.start_date);
      const endDate = new Date(holiday.attributes.end_date);

      if (startDate.getTime() === endDate.getTime()) {
        // Single date
        return formatDate(startDate, locale ?? "ar");
      } else {
        // Date range
        return `${formatDate(startDate, locale ?? "ar")} - ${formatDate(
          endDate,
          locale ?? "ar",
        )}`;
      }
    }

    return "";
  };

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={onClose}
      header={
        <div className="flex items-center justify-between p-6 border-b">
          <DialogTitle className="text-xl font-semibold">
            {t("settings.holidays.details.title")}
          </DialogTitle>

          {/* Action Buttons */}
          <div className="flex items-center gap-2 me-12">
            {onEdit && (
              <Button
                variant="ghost"
                size="sm"
                className="w-10 text-gray-600 hover:text-gray-700 hover:bg-gray-50"
                onClick={() => onEdit(holiday)}
              >
                <Edit2 className="!w-6 !h-6" />
              </Button>
            )}
            {onDelete && (
              <Button
                variant="ghost"
                size="sm"
                className="w-10 text-red-600 hover:text-red-700 hover:bg-red-50"
                onClick={() => onDelete(holiday)}
              >
                <Trash className="!w-6 !h-6 text-error hover:text-error-600" />
              </Button>
            )}
          </div>
        </div>
      }
      className="rounded-2xl"
    >
      <div className="flex flex-col gap-6">
        <div className="flex justify-between items-center font-readex_pro gap-8">
          <Label className="text-base font-medium text-gray-500 block mb-2">
            {holiday.attributes.start_date &&
            holiday.attributes.end_date &&
            new Date(holiday.attributes.start_date).getTime() !==
              new Date(holiday.attributes.end_date).getTime()
              ? t("settings.holidays.details.dates") + ":"
              : t("settings.holidays.details.date") + ":"}
          </Label>
          <p className="text-base font-semibold text-gray-900">
            {formatHolidayDates()}
          </p>
        </div>

        <div className="flex justify-between items-center font-readex_pro gap-8">
          <Label className="text-base font-medium text-gray-500 block mb-2">
            {t("settings.holidays.details.name") + ":"}
          </Label>
          <p className="text-base font-semibold text-gray-900">
            {holiday.attributes.name}
          </p>
        </div>
      </div>
    </ResponsiveDialog>
  );
}
