// Employee-related types for the people module

export type TEmployeeAttributes = {
  name: string;
  email: string;
  user_id: number;
  permissions: Record<string, unknown>[];
  avatar_url: string | null;
  department: string;
  start_date: string;
  status: string | null;
  phone?: string;
  phone_intl?: string;
  department_name: string;
  job_title?: string;
};

export type TUserRoleRelationship = {
  id: string;
  type: string;
};

export type TEmployeeRelationships = {
  user_roles: {
    data: TUserRoleRelationship[];
  };
  salary_package?: {
    data: {
      id: string;
      type: string;
    } | null;
  };
};

export type TEmployee = {
  id: string;
  type: string;
  attributes: TEmployeeAttributes;
  relationships: TEmployeeRelationships;
};

export type TEmployeesResponse = {
  data: TEmployee[];
  meta: {
    pagination: {
      current_page: number;
      total_pages: number;
      total_count: number;
      per_page: number;
    };
  };
};

export type TEmployeeResponse = {
  data: TEmployee;
};

// User role types
export type TUserRoleAttributes = {
  id: string;
  is_default: boolean | null;
};

export type TRoleProjectRelationship = {
  data: {
    id: string;
    type: string;
  };
};

export type TUserRoleRelationships = {
  role: TRoleProjectRelationship;
  project: TRoleProjectRelationship;
};

export type TUserRoleIncluded = {
  id: string;
  type: string;
  attributes: TUserRoleAttributes;
  relationships: TUserRoleRelationships;
};

// Legacy types for backward compatibility
export type TUserRole = {
  role: {
    id: string;
    name: string;
    global: boolean;
  };
  project: string | null;
};

export type TEmployeeLegacy = {
  name: string;
  email: string;
  user_id: number;
  id?: string | number;
  start_date: string;
  status: string | null;
  department: string;
  department_name: string;
  job_title?: string;
  phone?: string;
  phone_intl?: string;
  avatar_url?: string | null;
  permissions?: Record<string, unknown>;
  role_name?: string;
  user_roles?: TUserRole[];
};

// Form types
export type TEmployeeFormData = {
  name: string;
  email: string;
  phone?: string;
  department: string;
  job_title?: string;
  start_date: Date;
  avatar?: File;
};

export type TEmployeeUpdateFormData = Partial<TEmployeeFormData>;
