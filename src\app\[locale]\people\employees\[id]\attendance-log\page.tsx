import { EmployeeAttendanceTable } from "@/app/[locale]/_modules/people/_components/employees/profile/attendance-log";

export default async function EmployeeAttendanceLogPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id: employeeId } = await params;

  return (
    <div className="space-y-6 flex-1 flex flex-col">
      <EmployeeAttendanceTable employeeId={employeeId} />
    </div>
  );
}
