"use client";

import React from "react";
import { DateRangeModal } from "./date-range-modal";
import { SingleDateTimeModal } from "./single-datetime-modal";

export type UpdateDatesModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onUpdate: (startDate: Date, endDate: Date) => void;
  initialStartDate?: string | Date;
  initialEndDate?: string | Date;
  isLoading: boolean;

  // Modal content
  title?: string;
  description?: string;
  submitButtonText?: string;

  mode?: "single" | "range";
  includeTime?: boolean;

  // Validation options
  allowPastDates?: boolean;
  minDate?: Date;
  maxDate?: Date;

  // Labels for date range mode
  startDateLabel?: string;
  endDateLabel?: string;
  selectedRangeLabel?: string;
  noneLabel?: string;
};

export function UpdateDatesModal({
  isOpen,
  onClose,
  onUpdate,
  initialStartDate,
  initialEndDate,
  isLoading,
  title = "Select Dates",
  description,
  submitButtonText = "Update",
  mode = "range",
  includeTime = false,
  allowPastDates = true,
  minDate,
  maxDate,
  startDateLabel = "Start Date",
  endDateLabel = "End Date",
  selectedRangeLabel = "Selected Range",
  noneLabel = "None",
}: UpdateDatesModalProps) {
  // For single date with time, use SingleDateTimeModal
  if (mode === "single" && includeTime) {
    const handleSingleDateTimeConfirm = (dateTime: Date) => {
      // For single mode, both start and end date are the same
      onUpdate(dateTime, dateTime);
    };

    return (
      <SingleDateTimeModal
        isOpen={isOpen}
        onClose={onClose}
        onConfirm={handleSingleDateTimeConfirm}
        initialDateTime={initialStartDate}
        isLoading={isLoading}
        title={title}
        description={description}
        allowPastDates={allowPastDates}
        minDate={minDate}
        maxDate={maxDate}
      />
    );
  }

  // For single date without time or date range, use DateRangeModal
  return (
    <DateRangeModal
      isOpen={isOpen}
      onClose={onClose}
      onConfirm={onUpdate}
      initialStartDate={initialStartDate}
      initialEndDate={mode === "single" ? initialStartDate : initialEndDate}
      isLoading={isLoading}
      title={title}
      description={description}
      confirmButtonText={submitButtonText}
      startDateLabel={startDateLabel}
      endDateLabel={endDateLabel}
      selectedRangeLabel={selectedRangeLabel}
      noneLabel={noneLabel}
      allowPastDates={allowPastDates}
      minDate={minDate}
      maxDate={maxDate}
    />
  );
}
