import useSWR from "swr";

import { fetcher } from "@/services/fetcher";
import { TEmployeesResponse } from "../../type/employee";
import { useSearchParams } from "next/navigation";
import { useFilterParams } from "@/hooks/filters";
import { useApiUrl } from "@/hooks/useApiUrl";

export const useEmployees = (
  page: number,
  limit: number,
  sortBy: string = "-start_date",
) => {
  const searchParams = useSearchParams();
  const { filters } = useFilterParams("employees");
  const searchQuery = searchParams.get("search") || "";

  const apiUrl = useApiUrl({
    baseUrl: "/api/employees",
    page: Number(page),
    limit: Number(limit),
    sort: String(sortBy),
    search: searchQuery || undefined,
    filters,
    tableId: "employees",
  });

  const { data, error, isLoading, mutate } = useSWR<TEmployeesResponse>(
    apiUrl,
    fetcher,
  );

  const from = data?.meta?.pagination?.from;
  const to = data?.meta?.pagination?.to;

  return {
    employees: data?.data ?? [],
    totalCount: data?.meta?.pagination?.count ?? 0,
    isLoading,
    error,
    mutate,
    pagination: {
      firstResult: from,
      lastResult: to,
      limit: Number(limit),
      page: Number(page),
    },
  };
};
