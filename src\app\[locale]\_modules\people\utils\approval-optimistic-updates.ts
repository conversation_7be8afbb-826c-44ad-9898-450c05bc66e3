import { ApprovalStep, ApprovalRequestData } from "../type/approval-request";

/**
 * Generic utility for creating optimistic approval workflow updates
 * This can be used across different entities (leaves, salaries, expenses, etc.)
 */

export type OptimisticApprovalAction = "approve" | "reject";

export interface OptimisticUpdateParams<T> {
  currentData: T;
  itemId: string;
  action: OptimisticApprovalAction;
  comment?: string;
  getApprovalRequestId: (item: any) => string | undefined;
  updateItemStatus?: (
    item: any,
    isWorkflowComplete: boolean,
    action: OptimisticApprovalAction,
  ) => any;
}

/**
 * Creates optimistic updates for approval workflow steps
 */
export function createOptimisticApprovalUpdate<
  T extends { data: any[]; included?: any[] },
>(params: OptimisticUpdateParams<T>): T {
  const {
    currentData,
    itemId,
    action,
    comment = "",
    getApprovalRequestId,
    updateItemStatus,
  } = params;

  if (!currentData) return currentData;

  // Find the target item and its approval request ID
  const targetItem = currentData.data.find((item) => item.id === itemId);
  const approvalRequestId = getApprovalRequestId(targetItem);

  if (!approvalRequestId) {
    return currentData;
  }

  // Find the approval request in included data
  const approvalRequest = currentData.included?.find(
    (item) => item.id === approvalRequestId && item.type === "approval_request",
  ) as ApprovalRequestData | undefined;

  if (!approvalRequest) {
    return currentData;
  }

  const currentStep = approvalRequest.attributes.current_step;
  const steps = approvalRequest.attributes.steps || [];
  const currentStepIndex = steps.findIndex(
    (step: ApprovalStep) => step.id === currentStep?.id,
  );

  // Create temporary action
  const newAction = {
    id: Math.floor(Math.random() * 1000000), // Temporary ID
    user_id: "current_user",
    action: action,
    comment: comment,
    created_at: "pending",
  };

  // Update steps based on action
  const updatedSteps = steps.map((step: ApprovalStep) => {
    if (step.id === currentStep?.id) {
      return {
        ...step,
        complete: action === "approve",
        rejected: action === "reject",
        actions: [...(step.actions || []), newAction],
      };
    }
    return step;
  });

  // Determine next step and workflow completion
  const nextStepIndex = currentStepIndex + 1;
  const nextStep =
    action === "approve" && nextStepIndex < steps.length
      ? steps[nextStepIndex]
      : null;
  const isWorkflowComplete = action === "approve" ? nextStep === null : false;

  // Update the approval request
  const updatedApprovalRequest = {
    ...approvalRequest,
    attributes: {
      ...approvalRequest.attributes,
      status:
        action === "reject"
          ? "rejected"
          : isWorkflowComplete
            ? "approved"
            : "pending",
      current_step: action === "reject" ? currentStep : nextStep,
      steps: updatedSteps,
    },
  } as ApprovalRequestData;

  return {
    ...currentData,
    data: currentData.data.map((item) => {
      if (item.id === itemId && updateItemStatus) {
        return updateItemStatus(item, isWorkflowComplete, action);
      }
      return item;
    }),
    included:
      currentData.included?.map((item) => {
        if (item.id === approvalRequestId && item.type === "approval_request") {
          return updatedApprovalRequest;
        }
        return item;
      }) || [],
  } as T;
}
