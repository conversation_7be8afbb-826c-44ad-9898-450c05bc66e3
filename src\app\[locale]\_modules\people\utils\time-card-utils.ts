import { format, isToday, parseISO } from "date-fns";
import { ar, enUS } from "date-fns/locale";
import { LANGUAGES } from "@/constants/enum";
import { AttendancePeriod } from "@/types/employee/daySummary";
import { getPeriodColor } from "./color-utils";
import { TFunction } from "@/types";

export const formatDateDisplay = (
  dateStr: string | number | undefined,
  t: TFunction,
  locale: string,
  formatStr: string = "d MMM",
) => {
  if (!dateStr) return "";
  let dateObj;
  if (typeof dateStr === "number") {
    // If it's a 10-digit number, treat as seconds, otherwise as ms
    dateObj =
      dateStr.toString().length === 10
        ? new Date(dateStr * 1000)
        : new Date(dateStr);
  } else {
    dateObj = parseISO(dateStr);
  }
  const localeObj = locale === LANGUAGES.ARABIC ? ar : enUS;

  return isToday(dateObj)
    ? t("people.employees-page.profile.attendance.timeCard.today")
    : format(dateObj, formatStr, { locale: localeObj });
};

export const formatTimeDisplay = (
  dateStr: string | undefined,
  timeStr: string | undefined,
  locale: string,
) => {
  if (!dateStr || !timeStr) return "-";
  try {
    const dateTimeStr = `${dateStr}T${timeStr}`;
    const dateObj = parseISO(dateTimeStr);
    const localeObj = locale === LANGUAGES.ARABIC ? ar : enUS;

    return format(dateObj, "hh:mm a", { locale: localeObj });
  } catch (error) {
    return "-";
  }
};

export const getDefaultPeriod = () => {
  return {
    attributes: {
      period_type: "default",
      activity_type: null,
    },
  } as AttendancePeriod;
};

export const getPeriodInfo = (period: AttendancePeriod) => {
  const periodColor = getPeriodColor(period);
  return {
    background: periodColor.background,
    border: periodColor.border,
    translationKey: periodColor.translationKey,
  };
};
