import { NextRequest, NextResponse } from "next/server";
import { cmAPI } from "@/services/api/cm";
import { createErrorResponse } from "@/utils/api-error-extractor";
import { UpdateFormSubmissionData } from "@/types/cm";

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: "Form submission ID is required" },
        { status: 400 },
      );
    }

    const body: UpdateFormSubmissionData = await request.json();

    const response = await cmAPI.updateFormSubmission(id, body);
    return NextResponse.json(response);
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "PATCH Form Submission route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: "Form submission ID is required" },
        { status: 400 },
      );
    }

    // Note: This would require implementing getFormSubmission in cmAPI
    // For now, we'll return a not implemented error
    return NextResponse.json(
      { error: "Get single form submission not implemented yet" },
      { status: 501 },
    );
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET Form Submission route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
