import { Table } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { TFunction } from "@/types";
import { TFilterGroup, FilterRule } from "../types";
import { FilterRuleComponent } from "./filter-rule";

type FilterRuleListProps<TData> = {
  group: TFilterGroup;
  table: Table<TData>;
  translationPrefix: string;
  onAddRule: () => void;
  onUpdateRule: (
    groupId: string,
    ruleId: string,
    field: keyof FilterRule,
    value: string | Date,
  ) => void;
  onRemoveRule: (ruleId: string) => void;
  t: TFunction;
  ruleRefs: React.RefObject<{ [key: string]: HTMLDivElement | null }>;
};

export function FilterRuleList<TData>({
  group,
  table,
  translationPrefix,
  onAddRule,
  onUpdateRule,
  onRemoveRule,
  t,
  ruleRefs,
}: FilterRuleListProps<TData>) {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-sm font-medium text-gray-700">
          {t("common.Table.filter-options.title")}
        </h3>
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-1.5 text-sm font-medium px-3 py-1.5 h-auto rounded-md border-gray-300 hover:bg-gray-50 transition-colors"
          onClick={onAddRule}
        >
          <Plus className="h-3.5 w-3.5" />
          <span>{t("common.filter.rule")}</span>
        </Button>
      </div>

      <div className="space-y-4">
        {group.rules.map((rule) => (
          <div
            key={rule.id}
            ref={(el) => {
              ruleRefs.current[rule.id] = el;
            }}
          >
            <FilterRuleComponent
              key={rule.id}
              rule={rule}
              table={table}
              translationPrefix={translationPrefix}
              onRemove={() => onRemoveRule(rule.id)}
              onFieldChange={(value) =>
                onUpdateRule(group.id, rule.id, "field", value)
              }
              onOperatorChange={(value) =>
                onUpdateRule(group.id, rule.id, "operator", value)
              }
              t={t}
              group={group}
              updateRule={onUpdateRule}
            />
          </div>
        ))}
      </div>
    </div>
  );
}
