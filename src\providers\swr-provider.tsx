"use client";

import { SWRConfig } from "swr";
import { fetcher } from "@/services/fetcher";
import {
  notifyTokenExpired,
  isTokenExpired,
} from "@/lib/token-expiration-manager";
import { isTokenExpirationError } from "@/utils/auth";

const SWRProvider = ({ children }: { children: React.ReactNode }) => {
  return (
    <SWRConfig
      value={{
        fetcher,
        revalidateOnReconnect: true,
        dedupingInterval: 60000,
        revalidateOnFocus: false,
        errorRetryCount: 3,
        onError: (error) => {
          // Check for token expiration
          if (error.status === 401 || isTokenExpirationError(error)) {
            // Only trigger if not already triggered
            if (!isTokenExpired()) {
              notifyTokenExpired();
            }
          }
        },
        onErrorRetry: (error, _key, _config, revalidate, { retryCount }) => {
          if (error.status === 401 || isTokenExpirationError(error)) {
            if (!isTokenExpired()) {
              notifyTokenExpired();
            }
            return false;
          }
          if (retryCount >= 3) return;
          setTimeout(() => revalidate({ retryCount }), 1000);
        },
      }}
    >
      {children}
    </SWRConfig>
  );
};

export default SWRProvider;
