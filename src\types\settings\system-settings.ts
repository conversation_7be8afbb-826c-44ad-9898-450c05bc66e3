export type SystemSetting = {
  id: string;
  type: "setting";
  attributes: {
    namespace: string;
    key: string;
    logical_key: string;
    value: string | number | boolean;
    description: string;
    is_editable: boolean;
    setting_type: string;
    type_info: {
      type: string;
      display_name: string;
      validation_rules?: Record<string, unknown>;
    };
  };
};

export type SystemSettingsResponse = {
  data: SystemSetting[];
  meta: {
    pagination: {
      count: number;
      page: number;
      limit: number;
      from: number;
      to: number;
    };
  };
};
