import { LANGUAGES } from "@/constants/enum";
import { getLocale } from "next-intl/server";
import Image from "next/image";
import { Locale } from "@/i18n/routing";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Authentication | Athar EMS",
};

export default async function AuthLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale: Locale = (await getLocale()) as Locale;
  const isAr = locale === LANGUAGES.ARABIC;

  return (
    <div className="flex justify-between w-full h-full min-h-screen bg-[#F7F9FB]">
      <BackgroundSide
        src={isAr ? "/images/health/cover.png" : "/images/health/cover.png"}
      />
      <div className="flex items-center justify-center container">
        {children}
      </div>
    </div>
  );
}

const BackgroundSide = ({ src }: { src: string }) => {
  return (
    <div className="relative h-auto w-full max-w-[720px] max-md:d max-md:hidden min-w-[1000px]:shrink-0">
      <Image
        src={src}
        className="min-[1200px]:object-contain max-lg:object-cover max-lg:object-right"
        fill
        alt="athar for youth development"
        priority={true}
        sizes="(max-width: 1200px) 50vw, 635px"
      />
    </div>
  );
};
