"use client";

import React, { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { useStatistics } from "../../hooks/useStatistics";
import { MetricCardSkeleton } from "../../skeletons/metric-card-skeleton";
import { MetricCard } from "../common/metric-card";
import { MetricCardTypes } from "@/enums/statistics";

const HomepageMetricCards: React.FC = () => {
  const t = useTranslations();

  // Ensure client-only rendering to avoid hydration mismatch
  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
  }, []);

  // Fetch specific metrics for the homepage - Backend supports global statistics!
  const { metricCards, isLoading, error } = useStatistics(undefined, [
    MetricCardTypes.TotalEmployees,
    MetricCardTypes.DailyAttendanceRate,
    MetricCardTypes.AverageDailyWorkHours,
    MetricCardTypes.PendingRequests,
  ]);

  if (!mounted || isLoading) {
    return (
      <>
        <MetricCardSkeleton />
        <MetricCardSkeleton />
        <MetricCardSkeleton />
        <MetricCardSkeleton />
      </>
    );
  }

  if (error) {
    return (
      <div className="col-span-4 text-error p-4 min-h-[148px] border rounded-[20px] flex justify-center items-center">
        {error.message || t("common.errorLoadingStatistics")}
      </div>
    );
  }

  if (!metricCards || metricCards.length === 0) {
    return (
      <div className="col-span-4 p-4 min-h-[148px] border rounded-[20px] flex justify-center items-center text-muted">
        {t("common.noStatisticsAvailable")}
      </div>
    );
  }

  // Create a map for easy lookup
  const metricMap = metricCards.reduce(
    (acc, metric) => {
      acc[metric.id] = metric;
      return acc;
    },
    {} as Record<string, (typeof metricCards)[0]>,
  );

  // Define the order and ensure we have the metrics we want
  const orderedMetrics = [
    MetricCardTypes.TotalEmployees,
    MetricCardTypes.DailyAttendanceRate,
    MetricCardTypes.AverageDailyWorkHours,
    MetricCardTypes.PendingRequests,
  ];

  return (
    <>
      {orderedMetrics.map((metricType) => {
        const metric = metricMap[metricType];
        if (!metric) return null;

        return (
          <MetricCard
            key={metric.id}
            title={metric.attributes.title}
            value={metric.attributes.value}
            percentageChange={metric.attributes.comparison.percentage}
            comparisonText={metric.attributes.comparison.text}
            valueSuffix={
              metric.attributes.unit ? ` ${metric.attributes.unit}` : ""
            }
          />
        );
      })}
    </>
  );
};

export default HomepageMetricCards;
