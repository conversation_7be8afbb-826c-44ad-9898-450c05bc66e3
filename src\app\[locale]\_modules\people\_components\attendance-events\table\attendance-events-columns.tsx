"use client";
import { ColumnDef, Table, TableMeta } from "@tanstack/react-table";
import { Attendance_Event_type } from "@/constants/enum";
import { Locale } from "@/i18n/routing";
import {
  AttendanceEvent,
  TIncludedEmployee,
} from "../../../type/employee-leaves";
import { TFunction } from "@/types";
import { findEmployeeById } from "../../../utils/find-employee";
import CheckinStatus from "@/components/status/checkin-status";
import { mapCheckinStatusToCanonical } from "@/constants/translations-mapping";
import { formatDate } from "@/lib/dateFormatter";
import { AttendanceActions } from "./attendance-actions";

// Define the translation function type
interface TableMetaWithTranslation extends TableMeta<AttendanceEvent> {
  t: TFunction;
  locale?: Locale;
  employeeData: TIncludedEmployee[];
  onEdit?: (event: AttendanceEvent) => void;
  onViewDetails?: (event: AttendanceEvent) => void;
  isUpdating?: boolean;
}

// Helper function to get meta data
const getMeta = (table: Table<AttendanceEvent>) =>
  table.options.meta as TableMetaWithTranslation;

export const columns: ColumnDef<AttendanceEvent>[] = [
  // ID (hidden column for export)
  {
    accessorKey: "id",
    header: ({ table }) => {
      return (
        <div className="text-start">
          {getMeta(table).t("people.attendance-events-page.table.columns.id") || "ID"}
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <p className="text-sm font-semibold text-start text-gray-500">
          {row.original.id}
        </p>
      );
    },
    meta: { exportOnly: true }, // This column is only for export, not displayed in table
  },

  // employee Name
  {
    accessorKey: "employeeName",

    header: ({ table }) => {
      return (
        <div className="text-start">
          {getMeta(table).t(
            "people.attendance-events-page.table.columns.employeeName",
          )}
        </div>
      );
    },
    cell: ({ row, table }) => {
      const { employeeData } = getMeta(table);
      const employeeId = row.original.relationships.employee.data.id;
      const employee = findEmployeeById(employeeData, employeeId);
      return (
        employee && (
          <p className="text-sm font-semibold text-start text-gray-500">
            {employee.name}
          </p>
        )
      );
    },
    meta: { filterVariant: "select" },
  },

  // activity Type
  {
    accessorKey: "attributes.activity_type",
    id: "activity_type",
    enableColumnFilter: true,
    meta: {
      filterType: "text",
    },
    header: ({ table }) => {
      return (
        <div className="text-start">
          {getMeta(table).t(
            "people.attendance-events-page.table.columns.activity_type",
          )}
        </div>
      );
    },
    cell: ({ row, table }) => {
      const { t } = getMeta(table);
      const type = row.original.attributes.activity_type || "regular";
      return (
        <p className="text-sm font-semibold text-start text-gray-500">
          {t(
            `people.employees-page.profile.attendance.timeCard.periodKeys.${type}`,
          )}
        </p>
      );
    },
  },

  // timestamp
  {
    accessorKey: "attributes.timestamp",
    id: "timestamp",
    enableColumnFilter: true,
    meta: {
      filterType: "date",
    },
    header: ({ table }) => {
      return (
        <div>
          {getMeta(table).t(
            "people.attendance-events-page.table.columns.timestamp",
          )}
        </div>
      );
    },
    cell: ({ row, table }) => {
      const { locale } = getMeta(table);
      const dateStr = row.original.attributes.timestamp;
      const stringFormated = formatDate(dateStr, locale ?? "ar", "dd-MM hh:mm");

      return (
        <p className="text-sm font-semibold text-gray-500">{stringFormated}</p>
      );
    },
  },
  {
    accessorKey: "attributes.event_type",
    id: "event_type",
    enableColumnFilter: true,
    meta: {
      filterType: "text",
    },
    header: ({ table }) =>
      getMeta(table).t("people.attendance-events-page.table.columns.event_type"),
    cell: ({ row, table }) => {
      const { t } = getMeta(table);
      const status = row.original.attributes
        .event_type as Attendance_Event_type;
      const final = mapCheckinStatusToCanonical(status);
      return (
        <div className="mx-auto flex justify-center">
          <CheckinStatus
            status={final}
            label={t(
              `people.attendance-events-page.table.status.${status.toLowerCase()}`,
            )}
          />
        </div>
      );
    },
  },
  {
    accessorKey: "attributes.location",
    id: "location",
    enableColumnFilter: true,
    meta: {
      filterType: "text",
    },
    header: ({ table }) => (
      <div className="text-center">
        {getMeta(table).t("people.attendance-events-page.table.columns.location")}
      </div>
    ),
    cell: ({ row }) => {
      const location = row.original.attributes.location;
      return (
        <div className="text-center">
          <span className="text-sm text-gray-700">
            {location || "-"}
          </span>
        </div>
      );
    },
  },

  // Actions Column
  {
    id: "actions",
    enableSorting: false,
    enableHiding: false,
    header: ({ table }) => (
      <div className="text-center">
        {getMeta(table).t("people.attendance-events-page.table.columns.actions") || "Actions"}
      </div>
    ),
    cell: ({ row, table }) => {
      const { onEdit, onViewDetails, isUpdating, employeeData } = getMeta(table);
      return (
        <div className="flex justify-center">
          <AttendanceActions
            row={row}
            onEdit={onEdit}
            onViewDetails={onViewDetails}
            isUpdating={isUpdating}
            employeeData={employeeData}
          />
        </div>
      );
    },
  },
];
