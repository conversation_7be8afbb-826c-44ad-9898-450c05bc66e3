"use client";

import { But<PERSON> } from "@/components/ui/button";
import { TFunction } from "@/types";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { PlusCircle } from "../../../../../../../../public/images/icons";
import { usePermission } from "@/contexts/PermissionContext";
import { PermissionEnum } from "@/enums/Permission";
import ProjectDialog from "../project-dialog";

type ProjectsHeaderProps = {
  title: string;
  onProjectCreated?: () => void;
};

const ProjectsHeader = ({ title, onProjectCreated }: ProjectsHeaderProps) => {
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
      <h2 className="leading-[120%] text-base text-secondary mt-1 mb-2 sm:mb-0.5 max-w-full sm:max-w-md md:max-w-lg lg:max-w-xl">
        {title}
      </h2>
      <AddNewProjectDialog onProjectCreated={onProjectCreated} />
    </div>
  );
};

const AddNewProjectDialog = ({
  onProjectCreated,
}: {
  onProjectCreated?: () => void;
}) => {
  const t = useTranslations() as TFunction;
  const [isAddNewProject, setIsAddNewProject] = useState<boolean>(false);
  const { hasPermission } = usePermission();

  const canCreateProject = hasPermission(PermissionEnum.CREATE_PROJECT);

  // Don't render the button if user doesn't have permission
  if (!canCreateProject) {
    return null;
  }

  return (
    <div className="max-md:pt-2 md:pb-5 flex max-md:flex-col items-start md:items-center md:justify-between gap-4">
      <Button
        onClick={() => setIsAddNewProject((prev) => !prev)}
        className="sm:min-w-[169px] min-h-12 max-h-12 shadow-none font-semibold text-base max-md:mb-8 flex gap-4 items-center"
      >
        <PlusCircle />
        {t("projects.page.create.title")}
      </Button>
      <ProjectDialog
        mode="add"
        isOpen={isAddNewProject}
        onClose={() => setIsAddNewProject(false)}
        onSuccess={() => {
          setIsAddNewProject(false);
          if (onProjectCreated) {
            onProjectCreated();
          }
        }}
      />
    </div>
  );
};

export default ProjectsHeader;
