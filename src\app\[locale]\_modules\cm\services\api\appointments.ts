import { AppointmentData } from "../../types/appointment-chart";

// 3. Dummy yearly data
const dummyYearlyData: AppointmentData[] = [
  { month: "January", confirmed: 8, canceled: 2 },
  { month: "February", confirmed: 4, canceled: 3 },
  { month: "March", confirmed: 5, canceled: 1 },
  { month: "April", confirmed: 7, canceled: 2 },
  { month: "May", confirmed: 3, canceled: 5 },
  { month: "June", confirmed: 6, canceled: 1 },
  { month: "July", confirmed: 8, canceled: 1 },
  { month: "August", confirmed: 5, canceled: 2 },
  { month: "September", confirmed: 4, canceled: 4 },
  { month: "October", confirmed: 6, canceled: 3 },
  { month: "November", confirmed: 2, canceled: 5 },
  { month: "December", confirmed: 5, canceled: 2 },
];

export async function fetchYearlyData(): Promise<AppointmentData[]> {
  return dummyYearlyData;
}
