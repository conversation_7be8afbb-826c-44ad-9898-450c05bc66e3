"use client";

import { TDevice } from "../../../type/devices/device";
import dynamic from "next/dynamic";
import LoaderPortal from "@/components/loader/loader-portal";

const DeleteDeviceDialog = dynamic(() => import("../delete-device-dialog"), {
  ssr: false,
  loading: () => <LoaderPortal overlayColor="#000" overlayOpacity={0.6} />,
});
const EditDeviceDialog = dynamic(() => import("../edit-device-dialog"), {
  ssr: false,
  loading: () => <LoaderPortal overlayColor="#000" overlayOpacity={0.6} />,
});

type DeviceActionsState = {
  isEditDialogOpen: boolean;
  isDeleteDialogOpen: boolean;
  selectedDevice: TDevice | null;
};

type DeviceActionsHandlers = {
  handleEdit: (device: TDevice) => void;
  handleDelete: (device: TDevice) => void;
  handleConfirmDelete?: () => Promise<void>;
  handleUpdateSuccess?: (updatedDevice: TDevice) => void;
  closeEditDialog: () => void;
  closeDeleteDialog: () => void;
};

type DeviceActionsDialogsProps = {
  state: DeviceActionsState;
  handlers: DeviceActionsHandlers;
};

const DeviceActionsDialogs = ({
  state,
  handlers,
}: DeviceActionsDialogsProps) => {
  return (
    <>
      {/* Edit Device Dialog */}
      {state.isEditDialogOpen && state.selectedDevice && (
        <EditDeviceDialog
          device={state.selectedDevice}
          isOpen={state.isEditDialogOpen}
          onClose={handlers.closeEditDialog}
          onSuccess={handlers.handleUpdateSuccess}
        />
      )}

      {state.isDeleteDialogOpen && state.selectedDevice && (
        <DeleteDeviceDialog
          device={state.selectedDevice}
          isOpen={state.isDeleteDialogOpen}
          onClose={handlers.closeDeleteDialog}
          {...(handlers.handleConfirmDelete && {
            onConfirmDelete: handlers.handleConfirmDelete,
          })}
        />
      )}
    </>
  );
};

export default DeviceActionsDialogs;
