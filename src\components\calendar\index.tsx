"use client";

import FullCalendar from "@fullcalendar/react";
import { EventContentArg } from "@fullcalendar/core";
import dayGridPlugin from "@fullcalendar/daygrid";
import interactionPlugin from "@fullcalendar/interaction";
import timeGridPlugin from "@fullcalendar/timegrid";
import { Button } from "@/components/ui/button";
import useMediaQuery from "@/hooks/use-media-query";
import CalendarSkeleton from "../skeletons";

interface CalendarProps {
  events: import("@fullcalendar/core").EventInput[];
  locale: string;
  isRtl?: boolean;
  loading?: boolean;
  onEventClick?: (
    eventData: import("@fullcalendar/core").EventApi["extendedProps"],
  ) => void;
  // Generic day cell customization
  dayData?: DayDataItem[];
  onDayClick?: (date: Date, dayData: DayDataItem[]) => void;
  // Custom day cell renderer function
  customDayCellContent?: (
    arg: import("@fullcalendar/core").DayCellContentArg,
    dayData: DayDataItem[],
  ) => React.ReactNode;
  shoudldLimitEventRows?: boolean;
  // Draggable functionality
  enableDragging?: boolean;
  onEventDrop?: (info: import("@fullcalendar/core").EventDropArg) => void;
}

type DayDataItem = {
  start?: string | Date;
  end?: string | Date;
  from?: string | Date;
  to?: string | Date;
  // Add other fields as needed
};

export default function Calendar({
  events,
  locale,
  isRtl = false,
  loading = false,
  onEventClick,
  dayData = [],
  customDayCellContent,
  shoudldLimitEventRows = false,
  enableDragging = false,
  onEventDrop,
}: CalendarProps) {
  const isSmallScreen = useMediaQuery("(max-width:768px)");

  // Helper function to get data for a specific date
  const getDataForDate = (date: Date) => {
    const libdate = new Date(date.setHours(0, 0, 0, 0)); // normalize to start of day

    return (dayData as DayDataItem[]).filter((item) => {
      const startValue = item.start ?? item.from;
      const endValue = item.end ?? item.to;

      if (!startValue || !endValue) return false;

      const startDate = new Date(startValue);
      const endDateRaw = new Date(endValue);

      // Normalize range endpoints to midnight (optional but safer)
      const start = new Date(startDate.setHours(0, 0, 0, 0));
      const end = new Date(endDateRaw.setHours(0, 0, 0, 0));

      // Include the end date (make end inclusive)
      return libdate >= start && libdate <= end;
    });
  };

  const renderEventContent = (eventInfo: EventContentArg) => (
    <Button
      className="h-full w-full text-start justify-start event-spacing"
      variant="ghost"
      onClick={() =>
        onEventClick && onEventClick(eventInfo.event._def.extendedProps)
      }
    >
      <span className="block mt-1">{eventInfo.event.title}</span>
    </Button>
  );

  return (
    <div className="relative min-h-dvh overflow-x-auto custom-scroll">
      {loading ? (
        <CalendarSkeleton />
      ) : (
        <FullCalendar
          plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
          editable={enableDragging}
          eventStartEditable={enableDragging}
          eventDurationEditable={enableDragging}
          droppable={enableDragging}
          height="auto"
          contentHeight="auto"
          aspectRatio={5}
          eventClassNames={() => "event-spacing"}
          eventContent={renderEventContent}
          {...(shoudldLimitEventRows ? { dayMaxEventRows: 3 } : {})}
          eventOrder="title"
          eventDisplay="block"
          eventDrop={onEventDrop}
          moreLinkText={(num) =>
            isSmallScreen
              ? isRtl
                ? `${num}+`
                : `+${num}`
              : isRtl
                ? `${num} إضافي...`
                : `${num} more...`
          }
          views={{
            dayGridMonth: {
              dayCellContent: (arg) => {
                // If custom day cell content is provided, use it
                if (customDayCellContent) {
                  const dayItems = getDataForDate(arg.date);
                  return customDayCellContent(arg, dayItems);
                }

                // Default day cell content
                return (
                  <div
                    className={`flex items-center justify-center text-[#727A90] w-8 h-8 mx-auto border rounded-full
                    ${
                      arg.isToday
                        ? "bg-secondary !text-white"
                        : "bg-transparent"
                    }`}
                  >
                    {String(arg.date.getDate()).padStart(2, "0")}
                  </div>
                );
              },
            },
          }}
          initialView="dayGridMonth"
          events={events}
          locale={locale}
          direction={isRtl ? "rtl" : "ltr"}
          headerToolbar={{
            start: "placeholder",
            center: "prev title next",
            end: "today",
          }}
          buttonText={{ today: isRtl ? "اليوم" : "Today" }}
        />
      )}
    </div>
  );
}
