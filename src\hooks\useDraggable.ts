import { useEffect, useRef, useState } from "react";

type Position = {
  x: number;
  y: number;
};

type UseCenteredDraggableOptions = {
  enabled?: boolean;
  topPadding?: number;
  containWithinViewport?: boolean;
};

export const useCenteredDraggable = ({
  enabled = true,
  containWithinViewport = true,
}: UseCenteredDraggableOptions = {}) => {
  const ref = useRef<HTMLDivElement>(null);
  const position = useRef<Position>({ x: 0, y: 0 });
  const dragStart = useRef<Position>({ x: 0, y: 0 });
  const animationFrame = useRef<number | null>(null);
  const [isDragging, setIsDragging] = useState(false);

  const applyTransform = () => {
    if (ref.current) {
      ref.current.style.transform = `translate(calc(-50% + ${Math.round(position.current.x)}px), calc(-50% + ${Math.round(position.current.y)}px))`;
    }
  };

  useEffect(() => {
    if (ref.current) {
      if (isDragging) {
        ref.current.style.willChange = "transform";
      } else {
        ref.current.style.willChange = "";
      }
    }
    return () => {
      if (ref.current) {
        ref.current.style.willChange = "";
      }
    };
  }, [isDragging]);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!enabled || !ref.current) return;

    const centerX = window.innerWidth / 2;
    const centerY = window.innerHeight / 2;

    dragStart.current = {
      x: e.clientX - centerX - position.current.x,
      y: e.clientY - centerY - position.current.y,
    };

    setIsDragging(true);
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || !enabled || !ref.current) return;
    if (animationFrame.current !== null) {
      cancelAnimationFrame(animationFrame.current);
      animationFrame.current = null;
    }

    animationFrame.current = requestAnimationFrame(() => {
      const centerX = window.innerWidth / 2;
      const centerY = window.innerHeight / 2;
      const modal = ref.current;

      let newX = e.clientX - centerX - dragStart.current.x;
      let newY = e.clientY - centerY - dragStart.current.y;

      if (containWithinViewport && modal) {
        const maxX = window.innerWidth / 2;
        const minX = -maxX;

        const maxY = window.innerHeight / 2;
        const minY = -centerY + modal.offsetHeight / 2;

        newX = Math.max(minX, Math.min(maxX, newX));
        newY = Math.max(minY, Math.min(maxY, newY));
      }

      position.current = { x: Math.round(newX), y: Math.round(newY) };
      applyTransform();
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    if (animationFrame.current !== null) {
      cancelAnimationFrame(animationFrame.current);
      animationFrame.current = null;
    }
  };

  const resetPosition = () => {
    if (position.current.x !== 0 || position.current.y !== 0) {
      position.current = { x: 0, y: 0 };
      applyTransform();
    }
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
      document.body.style.userSelect = "none";
    }

    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
      document.body.style.userSelect = "";
    };
  }, [isDragging]);

  return {
    ref,
    position: position.current,
    isDragging,
    handleMouseDown,
    resetPosition,
  };
};
