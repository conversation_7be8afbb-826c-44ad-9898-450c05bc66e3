import ResponsiveDialog from "@/components/responsive-dialog";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import React from "react";
import {
  CopyLink,
  EmptyCircleAlert,
} from "../../../../../../../../public/images/icons";
import {
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Separator } from "@radix-ui/react-dropdown-menu";
import { useCopyToClipboard } from "@/hooks/use-copy-clipboard";
import { EventData } from "../../../types/appointment-calendar";

type TEventDetailsDialogProps = {
  selectedEvent: EventData;
  onClose: () => void;
};

const EventDetailsDialog = ({
  selectedEvent,
  onClose,
}: TEventDetailsDialogProps) => {
  const { copiedText, copyToClipboard } = useCopyToClipboard();
  return (
    <ResponsiveDialog
      open={!!selectedEvent}
      onOpenChange={onClose}
      closeBtnStyle="border-none"
      header={<AppointmentsCalendarHeader selectedEvent={selectedEvent} />}
    >
      <div className="space-y-4">
        <div className="flex items-start gap-4">
          <TooltipProvider>
            <Tooltip delayDuration={100}>
              <TooltipTrigger type="button">
                <EmptyCircleAlert className="stroke-2 !w-5 !h-5 group-[.error]:text-red-500" />
              </TooltipTrigger>
              <TooltipContent className="bg-primary text-secondary-2 rounded-sm p-3 mb-1">
                <p>for test</p>
                <div className="border-[10px] absolute left-1/2 -translate-x-1/2 -bottom-3 w-4 h-4 -z-10 border-r-transparent border-b-transparent border-l-transparent border-t-primary "></div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <div className="flex flex-col border border-[#F2F4F7] rounded-[10px] py-[22px] px-[29.5px]">
            <h2>{selectedEvent.title}</h2>
            <h3 className="text-[#6B7280] font-medium text-sm leading-5">
              {selectedEvent.statusTitle}
            </h3>
            <p>{selectedEvent.status}</p>
            <Separator className="h-[.5px] my-[13px] bg-[#E4E9EF]" />
            <h3 className="text-[#6B7280] font-medium text-sm leading-5">
              {selectedEvent.descriptionTitle}
            </h3>
            <p>{selectedEvent.description}</p>
          </div>
        </div>
        <DialogFooter className="flex !flex-col !justify-start !items-start gap-1">
          <div className="flex justify-center items-center gap-4 w-full">
            <EmptyCircleAlert className="stroke-2 !w-5 !h-5" />
            <div className="flex justify-between items-center w-full">
              <div className="flex flex-col">
                <Button className="min-h-10 rounded-xl">
                  {selectedEvent.joinMeetingText}
                </Button>
              </div>
              {copiedText !== selectedEvent.meetingLinkText ? (
                <Button
                  variant={"ghost"}
                  onClick={() =>
                    copyToClipboard(selectedEvent?.meetingLinkText || "")
                  }
                >
                  <CopyLink />
                </Button>
              ) : (
                <span>تم النسخ!</span>
              )}
            </div>
          </div>
          <div className="text-gray-500 justify-start text-sm font-normal px-8 ">
            {selectedEvent.meetingLinkText}
          </div>
        </DialogFooter>
        <DialogDescription className="sr-only"></DialogDescription>
      </div>
    </ResponsiveDialog>
  );
};

export default EventDetailsDialog;

const AppointmentsCalendarHeader = ({
  selectedEvent,
}: {
  selectedEvent: any;
}) => {
  return (
    <DialogHeader className="p-6 pb-0 mt-[34px]">
      <DialogTitle className="flex justify-start items-center gap-[18px] font-semibold text-[20px] leading-[] tracking-[0.5%]">
        <span className="inline-block w-4 h-4 bg-secondary rounded-sm"></span>
        <span>{"اسم المريض"}</span>
      </DialogTitle>
      <DialogDescription className="text-gray-500 flex justify-start text-sm leading-5 px-[29.5px] !m-0">
        {selectedEvent.date} | {selectedEvent.time}
      </DialogDescription>
    </DialogHeader>
  );
};
