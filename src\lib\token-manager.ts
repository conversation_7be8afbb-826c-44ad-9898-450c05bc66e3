// utils/token-expiration-monitor.ts
import { isDevelopment } from "@/utils/env";
import { notifyTokenExpired, isTokenExpired } from "./token-expiration-manager";

type DecodedToken = {
  exp: number;
  [key: string]: any;
};

const TOKEN_EXPIRATION = {
  SESSION_LIFETIME: 30 * 60, // 30 minutes in seconds
  SESSION_WARNING: 5 * 60, // 5 minutes before expiration

  MAIN_LIFETIME: 30 * 24 * 60 * 60, // 30 days in seconds
  MAIN_WARNING: 7 * 24 * 60 * 60, // 7 days before expiration

  CHECK_INTERVAL: 2 * 60 * 1000, // 2 minutes in milliseconds
} as const;

// --- Token Parsing & Utility Functions ---

export const parseJwt = (token: string): DecodedToken | null => {
  try {
    const base64 = token.split(".")[1].replace(/-/g, "+").replace(/_/g, "/");
    const payload = decodeURIComponent(
      atob(base64)
        .split("")
        .map((char) => "%" + ("00" + char.charCodeAt(0).toString(16)).slice(-2))
        .join(""),
    );
    return JSON.parse(payload);
  } catch (err) {
    console.error("Failed to parse JWT token:", err);
    return null;
  }
};

export const getTokenExpirationTime = (token: string): number | null => {
  const decoded = parseJwt(token);
  return decoded?.exp ? decoded.exp * 1000 : null;
};

export const isTokenExpiredOrExpiringSoon = (
  token: string,
  bufferMs: number = 60_000,
): boolean => {
  const expTime = getTokenExpirationTime(token);
  return !expTime || Date.now() + bufferMs > expTime;
};

// --- Main Monitor ---

export const setupTokenExpirationMonitor = () => {
  if (typeof window === "undefined") return () => {};

  const checkTokenStatus = async () => {
    if (isTokenExpired()) return;

    try {
      const res = await fetch("/api/token-status");
      if (!res.ok) return;

      const { tokens } = await res.json();

      const hasTokens = Object.values(tokens).some(
        (token: any) => token.present,
      );
      if (!hasTokens) return;

      const hasExpired = Object.values(tokens).some(
        (token: any) => token.present && token.expired,
      );

      if (hasExpired) {
        notifyTokenExpired();
        return;
      }

      const sessionExpiringSoon = Object.entries(tokens).some(
        ([name, token]: [string, any]) =>
          name.includes("session_token") &&
          token.present &&
          token.expiresIn !== null &&
          token.expiresIn < TOKEN_EXPIRATION.SESSION_WARNING,
      );

      const mainExpiringSoon = Object.entries(tokens).some(
        ([name, token]: [string, any]) =>
          name === "main_token" &&
          token.present &&
          token.expiresIn !== null &&
          token.expiresIn < TOKEN_EXPIRATION.MAIN_WARNING,
      );

      if (sessionExpiringSoon || mainExpiringSoon) {
        // Optional: Trigger token refresh mechanism here
      }

      if (isDevelopment()) {
        console.log("[TokenMonitor] Checked at", new Date().toISOString());
      }
    } catch (error) {
      if (isDevelopment()) {
        console.warn("[TokenMonitor] Error checking token:", error);
      }
    }
  };

  checkTokenStatus();
  const intervalId = setInterval(
    checkTokenStatus,
    TOKEN_EXPIRATION.CHECK_INTERVAL,
  );

  return () => clearInterval(intervalId);
};
