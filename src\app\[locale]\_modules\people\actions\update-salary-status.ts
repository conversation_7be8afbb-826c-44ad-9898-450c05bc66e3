"use server";

import { SALARY_STATUS } from "@/constants/enum";
import { handleError } from "@/lib/utils";
import { ActionState } from "@/types";
import { peopleService } from "@/services/api/people";

export async function updateSalaryStatus(
  salaryId: string,
  status: SALARY_STATUS,
): Promise<ActionState<{ success: boolean }>> {
  try {
    if (!salaryId) {
      return {
        error: "Salary ID is required",
        success: "",
        issues: ["Please provide a valid salary ID"],
        data: null,
      };
    }

    // Call the API to update the salary status
    await peopleService.updateSalaryStatus(salaryId, status);

    return {
      success: "Salary status updated successfully",
      error: "",
      issues: [],
      data: { success: true },
    };
  } catch (err) {
    return handleError(err, "Failed to update salary status", []);
  }
}
