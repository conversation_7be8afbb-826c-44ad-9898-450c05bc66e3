"use client";

import ResponsiveDialog from "@/components/responsive-dialog";
import { useTranslations, useLocale } from "next-intl";
import { TFunction } from "@/types";
import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import {
  AttendanceEvent,
  TIncludedEmployee,
} from "../../../type/employee-leaves";
import { formatDate } from "@/lib/dateFormatter";
import { Locale } from "@/i18n/routing";
import { Badge } from "@/components/ui/badge";
import CheckinStatus from "@/components/status/checkin-status";
import { mapCheckinStatusToCanonical } from "@/constants/translations-mapping";
import { Attendance_Event_type } from "@/constants/enum";
import { findEmployeeById } from "../../../utils/find-employee";

type ViewAttendanceDialogProps = {
  attendanceEvent: AttendanceEvent | null;
  employeeData: TIncludedEmployee[];
  isOpen: boolean;
  onClose: () => void;
};

export default function ViewAttendanceDialog({
  attendanceEvent,
  employeeData,
  isOpen,
  onClose,
}: ViewAttendanceDialogProps) {
  const t = useTranslations() as TFunction;
  const locale = useLocale() as Locale;

  if (!attendanceEvent) return null;

  const employeeId = attendanceEvent.relationships.employee.data.id;
  const employee = findEmployeeById(employeeData, employeeId);
  const employeeName = employee?.name || "Unknown Employee";
  const employeeEmail = employee?.email || "";
  const eventType = attendanceEvent.attributes
    .event_type as Attendance_Event_type;
  const activityType = attendanceEvent.attributes.activity_type;
  const location = attendanceEvent.attributes.location;
  const notes = attendanceEvent.attributes.notes;
  const timestamp = attendanceEvent.attributes.timestamp;
  const createdAt = attendanceEvent.attributes.created_at;
  const updatedAt = attendanceEvent.attributes.updated_at;

  const formattedTimestamp = formatDate(
    timestamp,
    locale,
    "EEEE, MMMM dd, yyyy 'at' hh:mm a",
  );
  const formattedCreatedAt = formatDate(
    createdAt,
    locale,
    "MMM dd, yyyy hh:mm a",
  );
  const formattedUpdatedAt = formatDate(
    updatedAt,
    locale,
    "MMM dd, yyyy hh:mm a",
  );

  const statusCanonical = mapCheckinStatusToCanonical(eventType);

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={onClose}
      closeBtnStyle="top-[21px]"
      header={
        <>
          <div className="px-6 py-[22px] border-b">
            <DialogTitle className="font-semibold text-[18px] leading-[28px]">
              {t("people.attendance-events-page.view.title")}
            </DialogTitle>
            <DialogDescription className="text-sm text-gray-600 mt-1 sr-only">
              {t("people.attendance-events-page.view.description")}
            </DialogDescription>
          </div>
        </>
      }
    >
      <div className="space-y-6">
        {/* Employee Information */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
            {t("people.attendance-events-page.view.employee-info")}
          </h3>
          <div className="grid grid-cols-1 gap-3">
            <div className="flex justify-between">
              <span className="font-medium text-gray-700">
                {t("people.attendance-events-page.table.columns.employee")}:
              </span>
              <span className="text-gray-900">{employeeName}</span>
            </div>
            {employeeEmail && (
              <div className="flex justify-between">
                <span className="font-medium text-gray-700">
                  {t("people.attendance-events-page.view.email")}:
                </span>
                <span className="text-gray-900">{employeeEmail}</span>
              </div>
            )}
          </div>
        </div>

        {/* Event Details */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
            {t("people.attendance-events-page.view.event-details")}
          </h3>
          <div className="grid grid-cols-1 gap-3">
            <div className="flex justify-between items-center">
              <span className="font-medium text-gray-700">
                {t("people.attendance-events-page.table.columns.event_type")}:
              </span>
              <CheckinStatus
                status={statusCanonical}
                label={t(
                  `people.attendance-events-page.table.status.${eventType.toLowerCase()}`,
                )}
              />
            </div>

            <div className="flex justify-between">
              <span className="font-medium text-gray-700">
                {t("people.attendance-events-page.table.columns.activity_type")}
                :
              </span>
              <span className="text-gray-900">{activityType || "-"}</span>
            </div>

            <div className="flex justify-between">
              <span className="font-medium text-gray-700">
                {t("people.attendance-events-page.table.columns.location")}:
              </span>
              <span className="text-gray-900">{location || "-"}</span>
            </div>

            <div className="flex justify-between">
              <span className="font-medium text-gray-700">
                {t("people.attendance-events-page.table.columns.timestamp")}:
              </span>
              <span className="text-gray-900">{formattedTimestamp}</span>
            </div>

            {notes && (
              <div className="space-y-2">
                <span className="font-medium text-gray-700">
                  {t("people.attendance-events-page.table.columns.notes")}:
                </span>
                <div className="bg-gray-50 rounded-lg p-3">
                  <p className="text-gray-900 text-sm">{notes}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Metadata */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
            {t("people.attendance-events-page.view.metadata")}
          </h3>
          <div className="grid grid-cols-1 gap-3 text-sm">
            <div className="flex justify-between">
              <span className="font-medium text-gray-700">ID:</span>
              <span className="text-gray-900 font-mono">
                {attendanceEvent.id}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium text-gray-700">
                {t("common.created-at")}:
              </span>
              <span className="text-gray-900">{formattedCreatedAt}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium text-gray-700">
                {t("common.updated-at")}:
              </span>
              <span className="text-gray-900">{formattedUpdatedAt}</span>
            </div>
          </div>
        </div>
      </div>
    </ResponsiveDialog>
  );
}
