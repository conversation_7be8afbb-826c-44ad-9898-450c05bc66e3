import { Button } from "@/components/ui/button";
import { ChevronsUpDown } from "lucide-react";
import { FaSortDown, FaSortUp } from "react-icons/fa";

// Helper function to handle sorting logic
const handleSorting = (column: any, table: any) => {
  const isSorted = column.getIsSorted(); // "asc", "desc", or false
  if (isSorted === "asc") {
    column.toggleSorting(true);
  } else if (isSorted === "desc") {
    column.clearSorting();
  } else {
    column.toggleSorting(false);
  }
};

// Reusable sorting button component
const SortableHeader = ({
  column,
  table,
  title,
}: {
  column: any;
  table: any;
  title: string;
}) => (
  <Button
    variant="ghost"
    title={
      column.getNextSortingOrder() === "asc"
        ? "Sort ascending"
        : column.getNextSortingOrder() === "desc"
          ? "Sort descending"
          : "Clear sort"
    }
    onClick={() => handleSorting(column, table)}
  >
    <span>{title}</span>
    {column.getIsSorted() === "desc" && (
      <FaSortDown className="h-3 w-3 mb-2 text-[#6C7278]" />
    )}
    {column.getIsSorted() === "asc" && (
      <FaSortUp className="h-3 w-3 mt-1 text-[#6C7278]" />
    )}
    {!column.getIsSorted() && (
      <ChevronsUpDown className="h-3 w-3 text-[#6C7278]" />
    )}
  </Button>
);

export default SortableHeader;
