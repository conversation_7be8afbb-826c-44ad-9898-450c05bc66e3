"use client";

import { useTranslations } from "next-intl";
import { MetricCard } from "../../../people/_components/common/metric-card";
import { MetricCardsSkeleton } from "../../../people/skeletons/metric-card-skeleton";
import { useEffect, useState } from "react";

export const StatisticsCardWrapper = () => {
  const t = useTranslations();

  // Dummy API loading state
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 1500); // Simulate API delay
    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <MetricCardsSkeleton count={4} />;
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 max-md:mt-4 font-readex_pro">
      <MetricCard
        title="الطلبات قيد الانتظار"
        value={15}
        valueSuffix={t("procure.HomePage.cards.request")}
        titleStyle="!text-black"
        classesSuffix="text-gray-400 text-2xl "
        className="!min-h-[124px] md:max-h-[124px] justify-between p-6"
      />
      <MetricCard
        title="الطلبات الموافق عليها"
        value={10}
        valueSuffix={t("procure.HomePage.cards.request")}
        titleStyle="!text-black"
        classesSuffix="text-gray-400 text-2xl "
        className="!min-h-[124px] md:max-h-[124px] justify-between p-6"
      />
      <MetricCard
        title="الطلبات المرفوضة"
        value={2}
        valueSuffix={t("procure.HomePage.cards.request")}
        titleStyle="!text-black"
        classesSuffix="text-gray-400 text-2xl "
        className="!min-h-[124px] md:max-h-[124px] justify-between p-6"
      />
      <MetricCard
        title="إجمالي الطلبات المقدمة"
        value={48}
        valueSuffix={t("procure.HomePage.cards.request")}
        titleStyle="!text-black"
        classesSuffix="text-gray-400 text-2xl  "
        className="!min-h-[124px] md:max-h-[124px] justify-between p-6"
      />
    </div>
  );
};
