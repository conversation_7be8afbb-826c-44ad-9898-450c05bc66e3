import { NextRequest, NextResponse } from "next/server";
import { cmAPI } from "@/services/api/cm";
import { createErrorResponse } from "@/utils/api-error-extractor";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const caseId = searchParams.get("case_id");

    if (!caseId) {
      return NextResponse.json(
        { error: "case_id parameter is required" },
        { status: 400 },
      );
    }

    const response = await cmAPI.getCaseProgressOverview(caseId);
    return NextResponse.json(response);
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET Case Progress Overview route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
