import { NextRequest, NextResponse } from "next/server";
import { cmAPI } from "@/services/api/cm";
import { createErrorResponse } from "@/utils/api-error-extractor";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const caseId = searchParams.get("case_id");
    const templateId = searchParams.get("template_id");

    if (!caseId || !templateId) {
      return NextResponse.json(
        { error: "case_id and template_id parameters are required" },
        { status: 400 },
      );
    }

    const response = await cmAPI.getTemplateProgress({
      case_id: caseId,
      template_id: templateId,
    });
    return NextResponse.json(response);
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET Template Progress route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
