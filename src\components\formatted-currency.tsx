"use client";

import useMediaQuery from "@/hooks/use-media-query";
import { useLocale, useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { LANGUAGES } from "@/constants/enum";
import { Locale } from "@/i18n/routing";

interface ExtendedNumberFormatOptions extends Intl.NumberFormatOptions {
  numberingSystem?: string;
}

export const formatCurrencyParts = (
  amount: number,
  locale: Locale = LANGUAGES.ENGLISH,
  currency: string,
  symbol: string,
) => {
  const options: ExtendedNumberFormatOptions = {
    style: "currency",
    currency,
    currencyDisplay: "symbol",
    minimumFractionDigits: 0,
    maximumFractionDigits: 3,
    numberingSystem: "latn",
  };

  const formatter = new Intl.NumberFormat(locale, options);
  const parts = formatter.formatToParts(amount);
  const numberPart = parts
    .filter((part) =>
      ["integer", "group", "decimal", "fraction"].includes(part.type),
    )
    .map((part) => part.value)
    .join("");

  const currencyPart = symbol;
  return {
    number: amount === 0 ? "00.00" : numberPart,
    currency: currencyPart,
  };
};

type TFormattedCurrencyProps = {
  amount: number;
  numberStyle?: string;
  currencyStyle?: string;
  wrapperStyle?: string;
};

function FormattedCurrency({
  amount,
  numberStyle,
  currencyStyle,
  wrapperStyle,
}: TFormattedCurrencyProps) {
  const isMobile = useMediaQuery("(max-width: 767px)");
  const locale: Locale = useLocale() as Locale;
  const t = useTranslations();
  const prefixCurrencyConfig = `common.currencyConfig.${
    isMobile ? "mobile" : "desktop"
  }`;

  const { number, currency } = formatCurrencyParts(
    amount,
    locale,
    t(`${prefixCurrencyConfig}.currency`),
    t(`${prefixCurrencyConfig}.symbol`),
  );

  return (
    <span
      className={cn(
        `font-semibold text-sm leading-[140%] tracking-[-2%] text-gray-500 flex justify-center items-center gap-1 ${wrapperStyle}`,
      )}
    >
      <span className={cn(numberStyle)}>{number}</span>
      <span className={cn(currencyStyle)}>{currency}</span>
    </span>
  );
}

export default FormattedCurrency;
