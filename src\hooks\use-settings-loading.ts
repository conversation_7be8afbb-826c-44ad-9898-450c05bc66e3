import { useCompanySettings, useAttendanceSettings, useSalarySettings } from "./use-system-settings";

export const useSettingsLoading = () => {
  const { isLoading: companyLoading } = useCompanySettings();
  const { isLoading: attendanceLoading } = useAttendanceSettings();
  const { isLoading: salaryLoading } = useSalarySettings();

  return {
    companyLoading,
    attendanceLoading,
    salaryLoading,
    isAnyLoading: companyLoading || attendanceLoading || salaryLoading,
  };
};
