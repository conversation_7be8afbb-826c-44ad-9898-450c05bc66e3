// src/app/[locale]/_modules/people/schemas/employeeSchema.ts
import { z } from "zod";
import { TFunction } from "@/types";
import { validatePhoneNumber } from "@/lib/utils";
import {
  emailValidation,
  phoneValidation,
  stringValidation,
} from "@/schemas/common-validations";
import {
  DEPARTMENT_OPTIONS,
  ACCEPTED_IMAGE_TYPES,
  MAX_FILE_SIZE,
} from "../constants/employee";
import { assignmentSchema } from "./assignmentSchema";

export const employeeSchema = (
  t: TFunction,
  getScopeByRoleId?: (roleId: string) => string | undefined,
) => {
  const imageFileSchema = z
    .instanceof(File)
    .refine((file) => file.size < MAX_FILE_SIZE, {
      message: t("common.form.profile-image.error.file.maxSize"),
    })
    .refine((file) => ACCEPTED_IMAGE_TYPES.includes(file.type), {
      message: t("common.form.profile-image.error.file.acceptedTypes"),
    });

  const attachmentFileSchema = z
    .instanceof(File)
    .refine((file) => file.size < MAX_FILE_SIZE, {
      message: t("common.form.attachment.error.file.maxSize"),
    });

  // Use the imported assignment schema

  return z.object({
    avatar: z.union([z.string().optional(), imageFileSchema]).optional(),

    name: stringValidation(t, "Name").min(1, {
      message: t("common.form.name.error.required"),
    }),

    email: emailValidation(t).min(1, {
      message: t("common.form.email.error.required"),
    }),

    phone: phoneValidation(t).refine((value) => validatePhoneNumber(value), {
      message: t("common.form.mobile.error.notValid"),
    }),

    department: z.enum(DEPARTMENT_OPTIONS, {
      errorMap: () => ({ message: t("common.form.department.error.required") }),
    }),

    start_date: z.preprocess(
      // Convert string to Date if needed
      (val) => (typeof val === "string" ? new Date(val) : val),
      // Then validate as date
      z
        .date()
        .min(new Date("1900-01-01"), {
          message: t("common.form.date.error.invalid"),
        })
        .max(new Date(), { message: t("common.form.date.error.future") }),
    ),

    assignments: z
      .array(assignmentSchema(t, getScopeByRoleId))
      .optional()
      .refine(
        (assignments) => {
          if (!assignments || assignments.length === 0) return true;
          const defaultCount = assignments?.filter(
            (a) => a.default == true,
          ).length;

          return defaultCount === 1;
        },
        { message: t("common.form.assignment.error.oneDefaultRequired") },
      ),

    attachments: z.array(attachmentFileSchema).optional(),
  });
};

export type EmployeeSchemaType = z.infer<ReturnType<typeof employeeSchema>>;
