import { NextRequest, NextResponse } from "next/server";
import { getEntityConfig, isValidEntity } from "@/config/export-entities";
import { buildApiUrl } from "@/utils/api";
import { cookies } from "next/headers";
import type { ExportFormat } from "@/config/export-entities";
import { isDevelopment } from "@/utils/env";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ entity: string }> },
) {
  try {
    const { entity } = await params;
    const { searchParams } = new URL(request.url);
    const format = searchParams.get("format") as ExportFormat;

    // Validate entity
    if (!isValidEntity(entity)) {
      return NextResponse.json(
        { error: `Invalid entity: ${entity}` },
        { status: 400 },
      );
    }

    // Validate format
    if (!format || !["csv", "pdf", "xlsx"].includes(format)) {
      return NextResponse.json(
        { error: "Invalid or missing format parameter" },
        { status: 400 },
      );
    }

    // Get entity configuration
    const config = getEntityConfig(entity)!;

    // Check if format is supported for this entity
    if (!config.supportedFormats?.includes(format)) {
      return NextResponse.json(
        { error: `Format ${format} not supported for ${entity}` },
        { status: 400 },
      );
    }

    // Get the session token (server-side, can access httpOnly cookies)
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get(config.sessionTokenKey)?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: "Authentication required. Please log in again." },
        { status: 401 },
      );
    }

    // Build query string from search params (excluding format)
    const queryParams = new URLSearchParams();

    // Apply default filters first
    if (config.defaultFilters) {
      Object.entries(config.defaultFilters).forEach(([key, value]) => {
        if (key === "search") {
          queryParams.append("filter[search]", encodeURIComponent(value));
        } else if (key === "page") {
          queryParams.append("page[number]", value.toString());
        } else if (key === "limit") {
          queryParams.append("page[size]", value.toString());
        } else if (key === "sort") {
          queryParams.append("sort", value);
        } else {
          queryParams.append(key, value.toString());
        }
      });
    }

    // Handle column selection - backend expects 'columns' parameter, not 'fields'
    const columns = searchParams.get("columns");
    if (columns) {
      queryParams.set("columns", columns);
    }

    // Override with request parameters
    for (const [key, value] of searchParams.entries()) {
      if (
        key !== "format" &&
        key !== "subsystem" &&
        key !== "columns" &&
        value
      ) {
        // Remove existing parameter if it exists
        queryParams.delete(key);
        queryParams.delete(`filter[${key}]`);
        queryParams.delete("page[number]");
        queryParams.delete("page[size]");

        if (key === "search") {
          queryParams.set("filter[search]", encodeURIComponent(value));
        } else if (key === "page") {
          queryParams.set("page[number]", value);
        } else if (key === "limit") {
          queryParams.set("page[size]", value);
        } else if (key === "sort") {
          queryParams.set("sort", value);
        } else {
          queryParams.set(key, value);
        }
      }
    }

    // Build the API URL
    const baseURL = buildApiUrl(
      config.subsystem as "people" | "cm" | "procure",
    );
    const url = `${baseURL}${config.apiPath}.${format}${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;

    // Make the request to the backend API
    const response = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${sessionToken}`,
        Accept: getAcceptHeader(format),
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      // Log error for monitoring (consider using proper logging service in production)
      if (isDevelopment()) {
        console.error(
          `Export failed for ${entity}:`,
          response.status,
          errorText,
        );
      }
      return NextResponse.json(
        {
          error: `Export failed: ${response.status} ${response.statusText}`,
        },
        { status: response.status },
      );
    }

    // Get the file data
    const fileData = await response.arrayBuffer();

    // Create the filename
    const timestamp = new Date().toISOString().split("T")[0];
    const filename = `${config.fileNamePrefix}-${timestamp}.${format}`;

    // Return the file with appropriate headers
    return new NextResponse(fileData, {
      status: 200,
      headers: {
        "Content-Type": getContentType(format),
        "Content-Disposition": `attachment; filename="${filename}"`,
        "Cache-Control": "no-cache, no-store, must-revalidate",
        Pragma: "no-cache",
        Expires: "0",
      },
    });
  } catch (error) {
    // Log error for monitoring (consider using proper logging service in production)
    if (isDevelopment()) {
      console.error("Export API error:", error);
    }
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Export failed" },
      { status: 500 },
    );
  }
}

// Helper function to get the appropriate Accept header
function getAcceptHeader(format: ExportFormat): string {
  switch (format) {
    case "csv":
      return "text/csv";
    case "pdf":
      return "application/pdf";
    case "xlsx":
      return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    default:
      return "application/json";
  }
}

// Helper function to get the appropriate Content-Type header
function getContentType(format: ExportFormat): string {
  switch (format) {
    case "csv":
      return "text/csv";
    case "pdf":
      return "application/pdf";
    case "xlsx":
      return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    default:
      return "application/octet-stream";
  }
}
