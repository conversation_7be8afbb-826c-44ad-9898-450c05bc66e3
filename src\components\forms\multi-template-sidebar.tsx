"use client";

import React from "react";
import { FormTemplate } from "@/types/cm";
import { useWorkflowSteps } from "@/app/[locale]/_modules/cm/hooks/useWorkflowSteps";
import { useFormSections } from "@/app/[locale]/_modules/cm/hooks/useFormSections";
import {
  CheckCircle,
  Circle,
  Clock,
  ChevronRight,
  FileText,
} from "lucide-react";
import { cn } from "@/lib/utils";

type MultiTemplateSidebarProps = {
  caseId?: string;
  templates: FormTemplate[];
  currentTemplateIndex: number;
  currentSectionIndex: number;
  completedTemplates: Set<number>;
  completedSections: Set<string>;
  onTemplateClick?: (templateIndex: number) => void;
  onSectionClick?: (sectionIndex: number) => void;
  isReadOnly?: boolean;
};

type StepStatus = "completed" | "current" | "pending" | "locked";

const getTemplateStatus = (
  index: number,
  currentIndex: number,
  completedTemplates: Set<number>,
): StepStatus => {
  if (completedTemplates.has(index)) return "completed";
  if (index === currentIndex) return "current";
  if (index < currentIndex) return "completed";
  return "locked";
};

const getSectionStatus = (
  sectionId: string,
  sectionIndex: number,
  currentSectionIndex: number,
  currentTemplateIndex: number,
  templateIndex: number,
  completedSections: Set<string>,
): StepStatus => {
  if (completedSections.has(sectionId)) return "completed";

  // If this is the current template
  if (templateIndex === currentTemplateIndex) {
    if (sectionIndex === currentSectionIndex) return "current";
    if (sectionIndex < currentSectionIndex) return "completed";
    return "locked";
  }

  // If this is a previous template, all sections should be accessible
  if (templateIndex < currentTemplateIndex) return "completed";

  // Future templates are locked
  return "locked";
};

const StatusIcon = ({ status }: { status: StepStatus }) => {
  switch (status) {
    case "completed":
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    case "current":
      return <Clock className="h-4 w-4 text-blue-600" />;
    case "pending":
    case "locked":
    default:
      return <Circle className="h-4 w-4 text-gray-400" />;
  }
};

export const MultiTemplateSidebar = ({
  caseId,
  templates,
  currentTemplateIndex,
  currentSectionIndex,
  completedTemplates,
  completedSections,
  onTemplateClick,
  onSectionClick,
  isReadOnly = false,
}: MultiTemplateSidebarProps) => {
  // Fetch workflow steps for dynamic sidebar data
  const { workflowSteps, isLoading: stepsLoading } = useWorkflowSteps({
    caseId,
  });

  // Get current template for sections loading
  const currentTemplate = templates[currentTemplateIndex];

  // Fetch sections for current template to show in sidebar
  const { sections: currentTemplateSections, isLoading: sectionsLoading } =
    useFormSections({
      formTemplateId: currentTemplate?.id,
      sort: "display_order",
    });

  // Calculate completed sections for current template only
  const currentTemplateCompletedSections = currentTemplateSections.filter(
    (section) => completedSections.has(section.id),
  ).length;

  const handleTemplateClick = (templateIndex: number) => {
    if (isReadOnly || !onTemplateClick) return;

    const templateStatus = getTemplateStatus(
      templateIndex,
      currentTemplateIndex,
      completedTemplates,
    );

    // Only allow clicking on current or completed templates
    if (templateStatus === "current" || templateStatus === "completed") {
      onTemplateClick(templateIndex);
    }
  };

  const handleSectionClick = (sectionIndex: number) => {
    if (isReadOnly || !onSectionClick) return;
    onSectionClick(sectionIndex);
  };

  if (stepsLoading) {
    return (
      <div className="w-80 h-full bg-white border-r border-gray-200 p-4">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-8 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-80 rounded-lg flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">سير العمل</h2>
        <p className="text-sm text-gray-600 mt-1">تقدم الحالة عبر النماذج</p>
      </div>

      {/* Workflow Steps */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-4">
          {templates.map((template, templateIndex) => {
            const templateStatus = getTemplateStatus(
              templateIndex,
              currentTemplateIndex,
              completedTemplates,
            );

            return (
              <div key={template.id} className="space-y-2">
                {/* Template Header */}
                <button
                  onClick={() => handleTemplateClick(templateIndex)}
                  disabled={templateStatus === "locked"}
                  className={cn(
                    "w-full flex items-center gap-3 p-3 rounded-lg text-left transition-colors",
                    {
                      "bg-blue-50 border border-blue-200":
                        templateStatus === "current",
                      "bg-green-50 border border-green-200":
                        templateStatus === "completed",
                      "bg-gray-50 border border-gray-200":
                        templateStatus === "locked",
                      "hover:bg-gray-100":
                        templateStatus !== "locked" && !isReadOnly,
                      "cursor-not-allowed opacity-50":
                        templateStatus === "locked",
                    },
                  )}
                >
                  <StatusIcon status={templateStatus} />
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-gray-900 truncate">
                      {template.attributes.title || template.attributes.name}
                    </h3>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-xs text-gray-500">
                        {template.attributes.sections_count} أقسام
                      </span>
                      <span className="text-xs text-gray-400">•</span>
                      <span className="text-xs text-gray-500">
                        {template.attributes.fields_count} حقل
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {templateStatus === "current" && (
                      <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                        جاري
                      </span>
                    )}
                    <ChevronRight className="h-4 w-4 text-gray-400" />
                  </div>
                </button>

                {/* Template Sections - Show for current template */}
                {templateStatus === "current" &&
                  templateIndex === currentTemplateIndex && (
                    <div className="ml-6 space-y-1">
                      {sectionsLoading ? (
                        <div className="text-xs text-gray-500 p-2">
                          جاري تحميل الأقسام...
                        </div>
                      ) : currentTemplateSections.length > 0 ? (
                        currentTemplateSections.map((section, sectionIndex) => {
                          const sectionStatus = getSectionStatus(
                            section.id,
                            sectionIndex,
                            currentSectionIndex,
                            currentTemplateIndex,
                            templateIndex,
                            completedSections,
                          );

                          return (
                            <button
                              key={section.id}
                              onClick={() => handleSectionClick(sectionIndex)}
                              disabled={sectionStatus === "locked"}
                              className={cn(
                                "w-full flex relative items-center gap-2 p-2 rounded text-left text-xs transition-colors",
                                {
                                  "bg-blue-50 text-blue-700":
                                    sectionStatus === "current",
                                  "bg-primary text-secondary":
                                    sectionStatus === "completed",
                                  "text-gray-600 hover:bg-gray-50":
                                    sectionStatus === "locked",
                                  "cursor-not-allowed opacity-50":
                                    sectionStatus === "locked",
                                },
                              )}
                            >
                              {sectionStatus === "current" && (
                                <div className="absolute bg-primary rounded-full w-2 h-2 right-2 top-1/2 -translate-y-1/2">
                                  <div className="bg-secondary rounded-full w-2 h-2"></div>
                                </div>
                              )}
                              <StatusIcon status={sectionStatus} />
                              <div className="flex-1 min-w-0">
                                <span className="truncate">
                                  {section.attributes.name}
                                </span>
                                <div className="text-xs text-gray-500 mt-1">
                                  {section.attributes.fields_count} حقل
                                </div>
                              </div>
                            </button>
                          );
                        })
                      ) : (
                        <div className="text-xs text-gray-500 p-2">
                          لا توجد أقسام متاحة
                        </div>
                      )}
                    </div>
                  )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
