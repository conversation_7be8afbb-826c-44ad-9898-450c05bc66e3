"use client";

import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";
import { Plus } from "lucide-react";
import { useFieldArray, useFormContext } from "react-hook-form";
import { EmployeeSchemaType } from "@/app/[locale]/_modules/people/schemas/employeeSchema";
import { Label } from "@/components/ui/label";
import { ControlledSelect } from "@/components/ui/controlled-select";
import { useProjects } from "@/hooks/useProjects";
import { useRoles } from "@/hooks/useRoles";
import { Trash } from "../../../../public/images/icons";
import { Checkbox } from "@/components/ui/checkbox";
import { useEffect } from "react";

type AssignmentsInputProps = {
  name: string;
  label?: string;
  isPending?: boolean;
  readOnly?: boolean;
  containerClassName?: string;
  labelClassName?: string;
};

export default function AssignmentsInput({
  name,
  label,
  isPending,
  readOnly,
  containerClassName,
  labelClassName,
}: AssignmentsInputProps) {
  const t = useTranslations();
  const form = useFormContext<EmployeeSchemaType>();
  const { projects, isLoading: isLoadingProjects } = useProjects();
  const { roles, isLoading: isLoadingRoles } = useRoles();

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: name as "assignments",
  });

  const handleAddAssignment = () => {
    if (projects && projects.length > 0 && roles && roles.length > 0) {
      const isFirstAssignment = fields.length === 0;
      append({
        project: "",
        role: "",
        default: isFirstAssignment,
      });
    }
  };

  const isLoading = isLoadingProjects || isLoadingRoles;

  // Trigger validation when fields array changes
  useEffect(() => {
    if (fields.length === 1) {
      // If there's only one assignment, make sure it's set as default
      const isDefault = form.watch(`assignments.0.default`);
      if (!isDefault) {
        form.setValue(`assignments.0.default`, true);
      }
    }
    if (fields.length > 0) {
      form.trigger("assignments");
    }
  }, [fields.length]);

  return (
    <div className={cn("space-y-4", containerClassName)}>
      {fields.length === 0 ? (
        <div className="text-sm text-gray-500 italic">
          {t("common.form.assignment.empty")}
        </div>
      ) : (
        <div className="space-y-4">
          {fields.map((field, index) => {
            const selectedRoleId = form.watch(`assignments.${index}.role`);
            const selectedRole = roles.find(
              (r) => r.id.toString() === selectedRoleId,
            );
            const isGlobalRole =
              selectedRole?.attributes.scope === "global_role";

            return (
              <div
                key={field.id}
                className="py-3 px-2 min-h-[136px] bg-gray-50 rounded-lg border border-gray-100"
              >
                <div className="flex justify-between items-center max-h-5 mb-6">
                  <div className="flex items-center gap-3">
                    <h3 className="text-sm font-medium text-gray-600">
                      {t("common.form.assignment.title")}
                      <span>{` ${index + 1}`}</span>
                    </h3>
                    <div className="flex items-center gap-2">
                      <Checkbox
                        id={`default-${index}`}
                        checked={
                          form.watch(`assignments.${index}.default`) ||
                          false ||
                          fields.length == 1
                        }
                        disabled={isPending || readOnly || fields.length == 1}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            // Uncheck all other default assignments
                            fields.forEach((_, i) => {
                              if (i !== index) {
                                form.setValue(
                                  `assignments.${i}.default`,
                                  false,
                                );
                              }
                            });
                          }
                          form.setValue(
                            `assignments.${index}.default`,
                            !!checked,
                          );
                          // Trigger validation for the entire assignments array
                          form.trigger("assignments");
                        }}
                      />
                      <Label
                        htmlFor={`default-${index}`}
                        className="text-gray-500 text-xs italic"
                      >
                        {t("common.form.assignment.default.label")}
                      </Label>
                    </div>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      const isDefaultAssignment = form.watch(
                        `assignments.${index}.default`,
                      );
                      remove(index);
                      // make the first remaining assignment the default after remove the default one
                      if (isDefaultAssignment && fields.length > 1) {
                        // Find the first remaining assignment (after removal)
                        const remainingIndex = index === 0 ? 0 : 0;
                        setTimeout(() => {
                          form.setValue(
                            `assignments.${remainingIndex}.default`,
                            true,
                          );
                          form.trigger("assignments");
                        }, 0);
                      } else {
                        // Trigger validation for the assignments array
                        setTimeout(() => {
                          form.trigger("assignments");
                        }, 0);
                      }
                    }}
                    disabled={isPending || readOnly || isLoading}
                    className="!h-auto self-end rounded-full hover:bg-red-50 hover:text-red-500 transition-colors"
                  >
                    <Trash className="!h-6 !w-6 text-error" />
                  </Button>
                </div>
                <div className="grid grid-cols-[1fr_1fr] gap-3">
                  <div>
                    <Label className="mb-2 block text-sm text-gray-500">
                      {t("common.form.assignment.role.label")}
                    </Label>
                    <ControlledSelect
                      onValueChange={(value) => {
                        const roleValue = value === "__empty" ? "" : value;
                        form.setValue(
                          `assignments.${index}.role` as const,
                          roleValue,
                        );

                        // Check if the selected role is global
                        const selectedRole = roles.find(
                          (r) => r.id.toString() === roleValue,
                        );
                        const isGlobalRole =
                          selectedRole?.attributes.scope === "global_role";

                        // If it's a global role, clear the project field
                        if (isGlobalRole) {
                          form.setValue(
                            `assignments.${index}.project` as const,
                            "",
                          );
                        }

                        // Trigger validation for both role and project
                        form.trigger(`assignments.${index}.role`);
                        form.trigger(`assignments.${index}.project`);
                      }}
                      value={form.watch(`assignments.${index}.role`)}
                      disabled={isPending || readOnly || isLoading}
                      placeholder={t("common.form.assignment.role.placeholder")}
                      triggerClassName="h-10 bg-white border-gray-200 rounded-md font-normal text-xs"
                      options={[
                        {
                          value: "__empty",
                          label: t("common.form.assignment.role.placeholder"),
                          disabled: true,
                        },
                        ...roles.map((role) => ({
                          value: role.id,
                          label: role.attributes.name,
                        })),
                      ]}
                    />
                    {form.formState.errors.assignments?.[index]?.role && (
                      <p className="text-xs text-red-500 mt-1">
                        {form.formState.errors.assignments[index]?.role
                          ?.message ||
                          t("common.form.assignment.role.required")}
                      </p>
                    )}
                  </div>
                  <div>
                    <Label className="mb-2 block text-sm font-medium text-gray-500">
                      {t("common.form.assignment.project.label")}
                    </Label>
                    <ControlledSelect
                      onValueChange={(value) => {
                        const projectValue = value === "__empty" ? "" : value;
                        form.setValue(
                          `assignments.${index}.project` as const,
                          projectValue,
                        );
                        // Trigger validation for the project field
                        form.trigger(`assignments.${index}.project`);
                      }}
                      value={form.watch(`assignments.${index}.project`)}
                      disabled={
                        isPending || readOnly || isLoading || isGlobalRole
                      }
                      placeholder={t(
                        "common.form.assignment.project.placeholder",
                      )}
                      triggerClassName="h-10 bg-white border-gray-200 rounded-md font-normal text-xs"
                      options={[
                        {
                          value: "__empty",
                          label: t(
                            "common.form.assignment.project.placeholder",
                          ),
                          disabled: true,
                        },
                        ...projects.map((project) => ({
                          value: project.id,
                          label: project.attributes.name,
                        })),
                      ]}
                    />
                    {form.formState.errors.assignments?.[index]?.project && (
                      <p className="text-xs text-red-500 mt-1">
                        {form.formState.errors.assignments[index]?.project
                          ?.message ||
                          t("common.form.assignment.project.required")}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
      <div className="flex justify-between items-center">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={handleAddAssignment}
          disabled={
            isPending ||
            readOnly ||
            isLoading ||
            projects.length === 0 ||
            roles.length === 0
          }
          className="flex items-center gap-1.5 text-sm font-normal px-3 py-1.5 h-auto rounded-md hover:bg-gray-50 transition-colors"
        >
          <Plus className="h-3.5 w-3.5 text-secondary" />
          <span className="text-secondary">
            {t("common.form.assignment.add")}
          </span>
        </Button>
      </div>
    </div>
  );
}
