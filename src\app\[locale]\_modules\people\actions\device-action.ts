"use server";

import { ActionState } from "@/types";
import { peopleService } from "@/services/api/people";
import { TDevice } from "../type/devices/device";
import { handleError } from "@/lib/utils";
import { handleApiError } from "@/utils/api-error-handler";
import { deviceSchema } from "../schemas/deviceSchema";
import { getTranslations } from "next-intl/server";

export async function deviceAction(
  mode: "create" | "update",
  deviceId: string | null,
  _prevState: ActionState<TDevice>,
  formData: FormData,
): Promise<ActionState<TDevice>> {
  const t = await getTranslations();

  try {
    // Convert FormData to object for validation
    const formObject: Record<string, string> = {};

    for (const [key, value] of formData.entries()) {
      // Skip configuration fields as they're handled separately
      if (key.includes("_config") || key === "capabilities") {
        continue;
      }
      formObject[key] = value as string;
    }

    // Validate the form data
    deviceSchema(t).parse(formObject);

    let data: TDevice;
    let successMessage: string;

    if (mode === "create") {
      data = await peopleService.addNewDevice(formData);
      successMessage = "Device added successfully";
    } else {
      if (!deviceId) {
        return {
          error: "Device ID is required for update",
          issues: null,
          data: null,
        };
      }
      data = await peopleService.updateDevice(deviceId, formData);
      successMessage = "Device updated successfully";
    }
    return {
      success: successMessage,
      error: null,
      issues: null,
      ...data,
    };
  } catch (error) {
    return handleError(error, "Failed to perform device action");
  }
}

export async function deleteDevice(
  deviceId: string,
): Promise<ActionState<null>> {
  try {
    await peopleService.deleteDevice(deviceId);

    return {
      success: "Device deleted successfully",
      error: null,
      issues: null,
      data: null,
    };
  } catch (error) {
    const handledResponse = handleApiError(error);
    if (handledResponse) {
      return handledResponse;
    }

    throw error;
  }
}
