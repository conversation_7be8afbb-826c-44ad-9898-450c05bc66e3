"use client";

import React, { useState } from "react";
import { useEmployeeDetails } from "../../../../hooks/employees/useEmployeeDetails";
import { SalaryPackageCard } from "./salary-package-card";
import { SalaryPackageDisplaySkeleton } from "../../../../skeletons/salary-package-skeleton";
import { EmployeeSalaryTable } from "../salary";
import { TSalaryPackageData } from "../../../../type/salary-package";
import ApprovalWorkflowModal from "../../../approval-workflow-modal";

type SalaryPackageDisplayProps = {
  employeeId: string;
};

const SalaryPackageDisplay = ({ employeeId }: SalaryPackageDisplayProps) => {
  const { salaryPackage, submittedSalaryPackage, included, isLoading, error } =
    useEmployeeDetails(employeeId);
  const [
    selectedSalaryPackageForWorkflow,
    setSelectedSalaryPackageForWorkflow,
  ] = useState<TSalaryPackageData | null>(null);

  // Handler for showing approval workflow modal
  const handleShowWorkflowModal = (salaryPackage: TSalaryPackageData) => {
    setSelectedSalaryPackageForWorkflow(salaryPackage);
  };

  if (isLoading) {
    return <SalaryPackageDisplaySkeleton />;
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col gap-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Main card error */}
            <div className="border border-gray-200 rounded-2xl shadow-sm p-5 min-h-[200px] flex justify-center items-center">
              <div className="text-center">
                <p className="text-error text-lg font-medium">
                  {error.message || "Error loading salary package data"}
                </p>
              </div>
            </div>
            {/* Allowances grid error */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {Array.from({ length: 4 }).map((_, index) => (
                <div
                  key={index}
                  className="shadow-sm max-h-[104px] border border-gray-200 rounded-lg p-5 flex justify-center items-center"
                >
                  <span className="text-gray-400 text-sm">-</span>
                </div>
              ))}
            </div>
          </div>
        </div>
        {/* History error */}
        <div className="border border-gray-200 rounded-2xl shadow-sm p-6 min-h-[200px] flex justify-center items-center">
          <p className="text-gray-500">Unable to load salary history</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        <SalaryPackageCard
          salaryPackage={salaryPackage}
          currentApprovedPackage={submittedSalaryPackage}
          included={included as any}
          onShowWorkflowModal={handleShowWorkflowModal}
        />
        <EmployeeSalaryTable employeeId={employeeId} />
      </div>

      {/* Approval Workflow Modal */}
      {selectedSalaryPackageForWorkflow && (
        <ApprovalWorkflowModal<TSalaryPackageData>
          isOpen={!!selectedSalaryPackageForWorkflow}
          onClose={() => setSelectedSalaryPackageForWorkflow(null)}
          data={selectedSalaryPackageForWorkflow}
          included={(included as any) || []}
          type="salary"
        />
      )}
    </>
  );
};

export default SalaryPackageDisplay;
