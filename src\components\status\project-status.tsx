const statusBackgroundColors = {
  active: "bg-success-50",
  inactive: "bg-neutral-100",
};

const statusTextColors = {
  active: "text-green-900",
  inactive: "text-neutral-600",
};

const ProjectStatus = ({
  status,
  label,
}: {
  status: "active" | "inactive";
  label: string;
}) => {
  // Default to neutral styling if status is not recognized
  const bgColor = statusBackgroundColors[status] || "bg-neutral-100";
  const textColor = statusTextColors[status] || "text-neutral-600";

  return (
    <div
      className={`min-w-24 max-w-24 min-h-[27px] text-center leading-[27px] px-3 rounded-sm text-sm font-semibold tracking-tight ${bgColor} ${textColor}`}
    >
      {label}
    </div>
  );
};

export default ProjectStatus;
