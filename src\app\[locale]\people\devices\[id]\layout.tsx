import { Suspense } from "react";
import DeviceDetailsHeader from "@/app/[locale]/_modules/people/_components/devices/details/device-details-header";
import DeviceDetailsTabs from "@/app/[locale]/_modules/people/_components/devices/details/device-details-tabs";

export default async function DeviceDetailsLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ id: string }>;
}) {
  const { id: deviceId } = await params;

  return (
    <div className="!mt-5">
      <Suspense fallback={<div>Loading device details...</div>}>
        <DeviceDetailsHeader deviceId={deviceId} />
      </Suspense>

      <div className="mt-6">
        <DeviceDetailsTabs deviceId={deviceId} />
        <div className="mt-6">{children}</div>
      </div>
    </div>
  );
}
