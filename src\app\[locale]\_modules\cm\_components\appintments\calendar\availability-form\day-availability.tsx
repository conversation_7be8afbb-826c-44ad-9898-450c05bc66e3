import {
  Control,
  Controller,
  FieldArrayWithId,
  UseFieldArrayReturn,
  UseFormRegister,
  useWatch,
} from "react-hook-form";
import { AvailabilityFormData } from "../../../../types/appointment-calendar";
import CustomTimePicker from "@/components/custom-time-picker";
import { useLocale, useTranslations } from "next-intl";
import { TFunction } from "@/types";
import { useEffect } from "react";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Trash } from "../../../../../../../../../public/images/icons";
import { Plus } from "lucide-react";
import { Label } from "@/components/ui/label";
import { getDayTranslations } from "@/constants/translations-mapping";
import { Locale } from "@/i18n/routing";

type DayAvailabilityProps = {
  dayField: FieldArrayWithId<AvailabilityFormData, "availability", "id">;
  dayIndex: number;
  control: Control<AvailabilityFormData>;
  register: UseFormRegister<AvailabilityFormData>;
  intervalArrayMethods: UseFieldArrayReturn<
    AvailabilityFormData,
    `availability.${number}.intervals`
  >;
};

const DayAvailability: React.FC<DayAvailabilityProps> = ({
  dayField,
  dayIndex,
  control,
  register,
  intervalArrayMethods,
}) => {
  const t = useTranslations() as TFunction;
  const locale: Locale = useLocale() as Locale;
  const { fields: intervalFields, append, remove } = intervalArrayMethods;
  const isEnabled = useWatch({
    control,
    name: `availability.${dayIndex}.isEnabled`,
  });

  useEffect(() => {
    if (isEnabled && intervalFields.length === 0) {
      append({ from: "09:00", to: "17:00" });
    }
    if (!isEnabled && intervalFields.length > 0) {
      for (let i = intervalFields.length - 1; i >= 0; i--) {
        remove(i);
      }
    }
  }, [isEnabled, append, intervalFields.length, remove]);

  const dayTranslations = getDayTranslations(t);
  const defaultInterval = intervalFields[0];
  const additionalIntervals = intervalFields.slice(1);

  return (
    <div className="border-b-2 border-[#F3F4F6] last:border-b-0 first:pt-0 py-5">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-5 sm:gap-6">
        <div className="flex items-center gap-4">
          <Controller
            control={control}
            name={`availability.${dayIndex}.isEnabled`}
            render={({ field }) => (
              <Switch
                dir="ltr"
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            )}
          />
          <Label className="font-semibold">
            {dayTranslations[dayField.day]}
          </Label>
        </div>

        {isEnabled && defaultInterval ? (
          <div className="flex items-center gap-2 sm:gap-6">
            <TimeIntervalPair
              control={control}
              dayIndex={dayIndex}
              intervalIndex={0}
              locale={locale}
            />
            <Button
              variant={"ghost"}
              type="button"
              onClick={() => append({ from: "09:00", to: "17:00" })}
              className="text-sm w-10 h-10 text-neutral-500 border border-neutral-200"
            >
              <Plus />
            </Button>
          </div>
        ) : (
          <div className="w-full max-w-[372px] bg-neutral-100 min-10 p-2 text-center leading-6 rounded">
            <span className="text-gray-500 font-semibold text-base">
              {t("cm.appointment-calendar.not-available")}
            </span>
          </div>
        )}
      </div>

      {isEnabled && additionalIntervals.length > 0 && (
        <div className="flex flex-col max-sm:items-start gap-4 sm:gap-5 mt-4 sm:mt-5">
          {additionalIntervals.map((interval, idx) => {
            const actualIndex = idx + 1;
            return (
              <div
                key={interval.id}
                className="flex items-center justify-end gap-2 sm:gap-6"
              >
                <TimeIntervalPair
                  control={control}
                  dayIndex={dayIndex}
                  intervalIndex={actualIndex}
                  locale={locale}
                />
                <Button
                  variant={"outline"}
                  type="button"
                  onClick={() => remove(actualIndex)}
                  className="text-sm w-10 h-10 text-neutral-500 border border-neutral-200"
                >
                  <Trash className="!size-auto" />
                </Button>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export { DayAvailability };

// -------------------------------------------------------------------
// TimeIntervalPair Component (Extracted)
// -------------------------------------------------------------------
interface TimeIntervalPairProps {
  control: Control<AvailabilityFormData>;
  dayIndex: number;
  intervalIndex: number;
  locale: string;
}

const TimeIntervalPair: React.FC<TimeIntervalPairProps> = ({
  control,
  dayIndex,
  intervalIndex,
  locale,
}) => (
  <div className="flex items-center gap-4">
    <Controller
      control={control}
      name={`availability.${dayIndex}.intervals.${intervalIndex}.from`}
      render={({ field: { onChange, value } }) => (
        <CustomTimePicker
          locale={locale}
          value={value}
          onChange={(newValue) => {
            console.log("time changed to", newValue);
            onChange(newValue);
          }}
        />
      )}
    />
    <span className="text-neutral-300 font-semibold text-[25px]">-</span>
    <Controller
      control={control}
      name={`availability.${dayIndex}.intervals.${intervalIndex}.to`}
      render={({ field: { onChange, value } }) => (
        <CustomTimePicker
          locale={locale}
          value={value}
          onChange={(newValue) => {
            console.log("time changed to", newValue);
            onChange(newValue);
          }}
        />
      )}
    />
  </div>
);
