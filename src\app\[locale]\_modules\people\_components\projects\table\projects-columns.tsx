"use client";

import { Checkbox } from "@/components/ui/checkbox";
import { TFunction } from "@/types";
import { ColumnDef, TableMeta, Table, Row } from "@tanstack/react-table";
import { TProject } from "@/types/core/project";
import { Locale } from "@/i18n/routing";
import ProjectActions from "./project-actions";
import ProjectStatus from "@/components/status/project-status";

interface TableMetaWithProjectActions extends TableMeta<TProject> {
  t: TFunction;
  locale?: Locale;
  onEditProject?: (project: TProject) => void;
  onDeleteProject?: (projectId: string) => void;
  isUpdating?: boolean;
  isDeleting?: boolean;
}

// Helper function to get meta data
const getMeta = (table: Table<TProject>) =>
  table.options.meta as TableMetaWithProjectActions;

export const columns: ColumnDef<TProject>[] = [
  {
    id: "select",
    enableSorting: false,
    enableHiding: false,
    header: ({ table }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#ACBCBB] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#1A1C1E] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
  },
  {
    accessorKey: "id",
    enableColumnFilter: true,
    meta: {
      filterType: "text",
    },
    header: ({ table }) => {
      const { t } = getMeta(table);
      return <div>{t("projects.page.table.columns.id")}</div>;
    },
    cell: ({ row }) => {
      const project = row.original;
      return <p className="text-sm font-mono text-gray-600">{project.id}</p>;
    },
  },
  {
    accessorKey: "name",
    enableColumnFilter: true,
    meta: {
      filterType: "text",
    },
    header: ({ table }) => {
      const { t } = getMeta(table);
      return <div>{t("projects.page.table.columns.name")}</div>;
    },
    cell: ({ row }) => {
      const project = row.original;
      return (
        <p className="text-sm font-semibold text-gray-900">
          {project.attributes.name}
        </p>
      );
    },
  },
  {
    accessorKey: "description",
    enableColumnFilter: true,
    meta: {
      filterType: "text",
    },
    header: ({ table }) => {
      const { t } = getMeta(table);
      return <div>{t("projects.page.table.columns.description")}</div>;
    },
    cell: ({ row }) => {
      const project = row.original;
      return (
        <p className="text-sm font-semibold text-gray-500 max-w-xs truncate">
          {project.attributes.description || "-"}
        </p>
      );
    },
  },
  {
    accessorKey: "status",
    enableColumnFilter: true,
    meta: {
      filterType: "select",
      filterOptions: [
        { label: "Active", value: "active" },
        { label: "Inactive", value: "inactive" },
      ],
    },
    header: ({ table }) => {
      const { t } = getMeta(table);
      return <div>{t("projects.page.table.columns.status")}</div>;
    },
    cell: ({ row, table }) => {
      const { t } = getMeta(table);
      const project = row.original;
      const status = project.attributes.status as "active" | "inactive";

      // Get the translated label
      const translatedLabel =
        status === "active"
          ? t("projects.page.table.status.active")
          : t("projects.page.table.status.inactive");

      return (
        <div className="flex items-center text-center justify-center">
          <ProjectStatus status={status} label={translatedLabel} />
        </div>
      );
    },
  },
  {
    id: "actions",
    enableSorting: false,
    enableHiding: false,
    header: ({ table }) => (
      <div className="text-center">
        {getMeta(table).t("projects.page.table.columns.actions")}
      </div>
    ),
    cell: ({ row, table }) => {
      const { onEditProject, onDeleteProject, isUpdating, isDeleting } =
        getMeta(table);
      return (
        <div className="flex justify-center">
          <ProjectActions
            row={row}
            onEditProject={onEditProject}
            onDeleteProject={onDeleteProject}
            isUpdating={isUpdating}
            isDeleting={isDeleting}
          />
        </div>
      );
    },
  },
];
