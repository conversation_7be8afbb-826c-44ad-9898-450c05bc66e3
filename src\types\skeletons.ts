// Props for individual CardSkeleton
export type CardSkeletonProps = {
  hasIcon?: boolean;
  titleClassName?: string;
  valueClassName?: string;
  subtextClassName?: string;
  detailClassName?: string;
  className?: string;
  shimmer?: string;
  maxHeight?: string;
  padding?: string;
  hasDetail?: boolean;
};

// Props for CardsSkeleton (extends CardSkeletonProps partially)
export interface CardsSkeletonProps extends Partial<CardSkeletonProps> {
  count?: number;
}

export interface CalendarSkeletonProps {
  className?: string;
}

// chart skeleton
export type ChartSkeleton = {
  skeletonWrapperStyle?: string;
  titleStyle?: string;
  showLabel?: boolean;
  showSelect?: boolean;
};

// table skeleton
export type SkeletonTableProps = {
  rowCount: number;
  rowHeight?: number;
  headerHeight?: number;
  paginationHeight?: number;
  wrapperClass?: string;
};
