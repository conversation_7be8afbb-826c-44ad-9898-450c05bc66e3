"use client";

import React from "react";
import { EmployeeRolesProjectsTable } from "./employee-roles-projects-table";
import { useEmployeeDetails } from "../../../../hooks/employees/useEmployeeDetails";
import { transformUserRolesToRoleProjectPairs } from "../../../../utils/transform-user-roles";

type EmployeeRolesProjectsProps = {
  employeeId: string;
};

const EmployeeRolesProjects = ({ employeeId }: EmployeeRolesProjectsProps) => {
  const {
    isLoading,
    error,
    userRoles,
    mutate: mutateData,
  } = useEmployeeDetails(employeeId);

  const roleProjectPairs = transformUserRolesToRoleProjectPairs(
    userRoles || [],
  );

  // Render roles and projects table
  return (
    <>
      <EmployeeRolesProjectsTable
        employeeId={employeeId}
        roleProjectPairs={roleProjectPairs}
        isLoading={isLoading}
        error={error}
        mutateData={mutateData}
      />
    </>
  );
};

export default EmployeeRolesProjects;
