import { ApprovalRequestData } from "../../type/approval-request";
import { TIncludedEmployee } from "../../type/employee-leaves";
import { SalaryCalculationDetail } from "../../type/employees-salaries";

export type IncludedItem = {
  id: string;
  type: string;
};

// Overloaded function signatures for backward compatibility
export function getIncludedItem(
  included: (TIncludedEmployee | ApprovalRequestData)[],
  type: string,
  id: string,
): (TIncludedEmployee | ApprovalRequestData) | null;

export function getIncludedItem(
  included: (
    | TIncludedEmployee
    | ApprovalRequestData
    | SalaryCalculationDetail
  )[],
  type: string,
  id: string,
): (TIncludedEmployee | ApprovalRequestData | SalaryCalculationDetail) | null;

// Implementation
export function getIncludedItem(
  included: (
    | TIncludedEmployee
    | ApprovalRequestData
    | SalaryCalculationDetail
  )[],
  type: string,
  id: string,
): (TIncludedEmployee | ApprovalRequestData | SalaryCalculationDetail) | null {
  return included.find((item) => item.type === type && item.id === id) ?? null;
}

export function filterIncludedByType<T extends IncludedItem>(
  included: IncludedItem[] | undefined,
  type: string,
): T[] {
  if (!included) return [];
  return included.filter((item): item is T => item.type === type);
}
