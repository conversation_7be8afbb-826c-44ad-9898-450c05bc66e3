export type TSalaryPackageAttributes = {
  id?: number;
  base_salary: string;
  transportation_allowance: string;
  housing_allowance?: string; // New optional field
  other_allowances: string;
  total_package_value: string;

  effective_date: string;
  end_date: string | null;

  status: "draft" | "pending_approval" | "approved" | "rejected" | "cancelled";
  active?: boolean;

  editable?: boolean;
  submitted?: boolean;
  submittable?: boolean;

  cancellable?: boolean;
  cancellation_reason?: string | null;
  cancelled_at?: string | null;

  adjustment_reason: string | null;
  previous_package_id: string | null;

  notes?: string;

  created_at: string;
  updated_at: string;

  created_by_id?: number;
  creator_name?: string;
  creator_department?: string;
};

export type TSalaryPackageRelationships = {
  employee: {
    data: {
      id: string;
      type: string;
    };
  };
  approval_request?: {
    data: {
      id: string;
      type: string;
    } | null;
  };
};

export type TSalaryPackageData = {
  id: string;
  type: string;
  attributes: TSalaryPackageAttributes;
  relationships: TSalaryPackageRelationships;
};

export type TSalaryPackageResponse = {
  data: TSalaryPackageData[];
  meta: {
    pagination: {
      count: number;
      page: number;
      limit: number;
      from: number;
      to: number;
    };
  };
};

export type TSalaryPackageFormData = {
  employee_id: string;
  base_salary: string;
  housing_allowance: string;
  transportation_allowance: string;
  other_allowances: string;
  effective_date: Date;
  notes?: string;
};
