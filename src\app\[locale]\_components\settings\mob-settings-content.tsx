"use client";

import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  Set<PERSON>s,
  BriefCase,
  DollarSquare,
  CPUSettings,
} from "../../../../../public/images/icons";
import { Button } from "@/components/ui/button";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import EditProfileForm from "@/app/[locale]/_components/settings/edit-profile";
import ChangePasswordForm from "../../_components/settings/change-password";
import { ChevronLeft } from "lucide-react";
import NotificationSound from "../../_components/settings/notification-sound";
import { useTranslations } from "next-intl";
import { usePermission } from "@/contexts/PermissionContext";
import { PermissionEnum } from "@/enums/Permission";
import { useSettingsLoading } from "@/hooks/use-settings-loading";
import { Loader2 } from "lucide-react";
import CompanySettings from "./company-settings";
import AttendanceSettings from "./attendance-settings";
import SalarySettings from "./salary-settings";
import { createPortal } from "react-dom";

// Floating Back Button Component
const FloatingBackButton = ({ onBack }: { onBack: () => void }) => {
  const t = useTranslations();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  const buttonElement = (
    <Button
      variant="ghost"
      onClick={onBack}
      className="absolute z-50 gap-2 flex items-center top-20 end-4 text-secondary hover:bg-background-v2 p-3 bg-white/95 backdrop-blur-sm rounded-full md:hidden"
    >
      <span className="text-sm font-medium">{t("settings.buttons.back")}</span>
      <ChevronLeft className="!w-4 !h-4 text-[#1c1c1c] ltr:rotate-180" />
    </Button>
  );

  // Use portal to render outside the component tree
  return typeof document !== "undefined"
    ? createPortal(buttonElement, document.body)
    : buttonElement;
};

const MobSettingsContent = () => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const t = useTranslations();
  const { hasPermission } = usePermission();
  const { companyLoading, attendanceLoading, salaryLoading } =
    useSettingsLoading();

  // Initialize activeTab from URL, defaulting to an empty string (no selection)
  const initialTab = searchParams.get("tab") || "";
  const [activeTab, setActiveTab] = useState(initialTab);

  // Update the URL whenever activeTab changes using shallow routing.
  useEffect(() => {
    const params = new URLSearchParams(searchParams);
    if (activeTab) {
      params.set("tab", `${activeTab}`);
    } else {
      params.delete("tab");
    }
    router.replace(`${pathname}?${params.toString()}`);
  }, [activeTab, router, pathname, searchParams]);

  // If a tab is active, render the full-page form with a floating back button.
  if (activeTab) {
    return (
      <>
        <FloatingBackButton onBack={() => setActiveTab("")} />
        <div className="flex flex-col min-h-[calc(100vh-225px)] max-md:pt-4 max-md:mt-0 md:mt-14">
          {activeTab === "global" && (
            <EditProfileForm
              backToGeneral={() => setActiveTab("change-password")}
            />
          )}
          {activeTab === "notifications" && <NotificationSound />}
          {activeTab === "change-password" && (
            <ChangePasswordForm backToGeneral={() => setActiveTab("global")} />
          )}
          {activeTab === "company" &&
            hasPermission(PermissionEnum.READ_SETTING) && <CompanySettings />}
          {activeTab === "salary" &&
            hasPermission(PermissionEnum.READ_SETTING) && <SalarySettings />}
          {activeTab === "attendance" &&
            hasPermission(PermissionEnum.READ_SETTING) && (
              <AttendanceSettings />
            )}
        </div>
      </>
    );
  }

  // Otherwise, render the settings options.
  return (
    <div className="flex flex-col min-h-[calc(100vh-225px)] max-md:pt-4">
      <div className="flex flex-1 flex-col gap-2">
        {/* Global Settings Button */}
        <Button
          onClick={() => setActiveTab("global")}
          className={`flex justify-start items-center gap-3 text-[#1c1c1c] bg-neutral-100 hover:bg-background-v2 h-[45px] shadow-none`}
        >
          <Settings className="text-icons-main" />
          <span>{t("settings.global.title")}</span>
        </Button>
        {/* Notifications Settings Button */}
        <Button
          onClick={() => setActiveTab("notifications")}
          className={`flex justify-start items-center gap-3 text-[#1c1c1c] bg-neutral-100 hover:bg-background-v2 h-[45px] shadow-none`}
        >
          <Bell className="stroke-icons-main" />
          <span>{t("settings.notifications.title")}</span>
        </Button>
        {/* Company Settings Button */}
        {hasPermission(PermissionEnum.READ_SETTING) && (
          <Button
            onClick={() => setActiveTab("company")}
            disabled={companyLoading}
            className={`flex justify-start items-center gap-3 text-[#1c1c1c] bg-neutral-100 hover:bg-background-v2 h-[45px] shadow-none disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            <BriefCase className="text-icons-main" />
            <span className="flex items-center gap-2">
              {t("common.settings.tabs.company")}
              {companyLoading && <Loader2 className="w-3 h-3 animate-spin" />}
            </span>
          </Button>
        )}
        {/* Salary Settings Button */}
        {hasPermission(PermissionEnum.READ_SETTING) && (
          <Button
            onClick={() => setActiveTab("salary")}
            disabled={salaryLoading}
            className={`flex justify-start items-center gap-3 text-[#1c1c1c] bg-neutral-100 hover:bg-background-v2 h-[45px] shadow-none disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            <DollarSquare className="text-icons-main" />
            <span className="flex items-center gap-2">
              {t("common.settings.tabs.salary")}
              {salaryLoading && <Loader2 className="w-3 h-3 animate-spin" />}
            </span>
          </Button>
        )}
        {/* Attendance Settings Button */}
        {hasPermission(PermissionEnum.READ_SETTING) && (
          <Button
            onClick={() => setActiveTab("attendance")}
            disabled={attendanceLoading}
            className={`flex justify-start items-center gap-3 text-[#1c1c1c] bg-neutral-100 hover:bg-background-v2 h-[45px] shadow-none disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            <CPUSettings className="text-icons-main" />
            <span className="flex items-center gap-2">
              {t("common.settings.tabs.attendance")}
              {attendanceLoading && (
                <Loader2 className="w-3 h-3 animate-spin" />
              )}
            </span>
          </Button>
        )}
      </div>
      {/* Change Password Button */}
      <Button onClick={() => setActiveTab("change-password")} className="h-12">
        {t("common.settings.tabs.change-password")}
      </Button>
    </div>
  );
};

export default MobSettingsContent;
