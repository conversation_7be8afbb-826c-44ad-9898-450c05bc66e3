# Dependencies
node_modules/
.pnp
.pnp.js

# Production builds
.next/
out/
build/
dist/

# Environment variables and config files
.env*
*.env
docker-compose.env
*.dockerfile
Dockerfile
.yarnrc.yml
mise.toml

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Generated files
*.tsbuildinfo

# Shell scripts and other non-JS/TS files
*.sh
*.bash
*.zsh
*.fish

# Lock files and configs that shouldn't be formatted
*.lock
*.iml
*.toml

# Binary and media files
*.ico
*.png
*.jpg
*.jpeg
*.gif
*.svg
*.woff
*.woff2
*.ttf
*.eot
*.pdf

# Documentation and text files
*.md
*.txt
*.rst

# YAML and other config files that we don't want to format
*.yml
*.yaml
*.xml

# CSS files (if you don't want Prettier to format them)
*.css
*.scss
*.sass
*.less

# Git and Docker files
.gitignore
.dockerignore
.gitattributes
.prettierignore

# Scripts and executables
scripts/
*.sh
*.bat
*.cmd

# Audio/Video files
*.mp3
*.mp4
*.wav
*.avi
*.mov

# Other files we don't want to format
public/sound/

