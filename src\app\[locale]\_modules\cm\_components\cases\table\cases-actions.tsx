"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Eye, Edit, Trash2, MoreVertical } from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import { LANGUAGES } from "@/constants/enum";

interface CasesActionsProps {
  caseId: string;
}

export const CasesActions: React.FC<CasesActionsProps> = ({ caseId }) => {
  const t = useTranslations();
  const locale = useLocale();
  const isAr = locale === LANGUAGES.ARABIC;

  const handleView = () => {
    // TODO: Implement view functionality
    console.log("View case:", caseId);
  };

  const handleEdit = () => {
    // TODO: Implement edit functionality
    console.log("Edit case:", caseId);
  };

  const handleDelete = () => {
    // TODO: Implement delete functionality
    console.log("Delete case:", caseId);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreVertical className="w-4 text-gray-500 group-hover:text-black" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align={isAr ? "start" : "end"}>
        <DropdownMenuItem onClick={handleView}>
          <Eye className="mr-2 h-4 w-4" />
          {t("cm.cases.table.actions.view")}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleEdit}>
          <Edit className="mr-2 h-4 w-4" />
          {t("cm.cases.table.actions.edit")}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleDelete} className="text-red-600">
          <Trash2 className="mr-2 h-4 w-4" />
          {t("cm.cases.table.actions.delete")}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
