import { SortingState } from "@tanstack/react-table";

export function convertSortingToApiFormat(sorting: SortingState): string {
  if (sorting.length === 0) {
    return "";
  }

  return sorting.map((sort) => (sort.desc ? `-${sort.id}` : sort.id)).join(",");
}
export function parseApiSortToSortingState(sortString: string): SortingState {
  if (!sortString || sortString.trim() === "") {
    return [];
  }

  return sortString.split(",").map((field) => {
    const trimmedField = field.trim();
    if (trimmedField.startsWith("-")) {
      return {
        id: trimmedField.substring(1),
        desc: true,
      };
    }
    return {
      id: trimmedField,
      desc: false,
    };
  });
}

// Validates if a sort string is in the correct API format
export function isValidApiSortFormat(sortString: string): boolean {
  if (!sortString || sortString.trim() === "") {
    return true; // Empty string is valid (no sorting)
  }

  // Check if it matches the pattern: field1,-field2,field3
  const sortPattern =
    /^-?[a-zA-Z_][a-zA-Z0-9_]*(?:,-?[a-zA-Z_][a-zA-Z0-9_]*)*$/;
  return sortPattern.test(sortString.trim());
}
