"use client";

import { LANGUAGES } from "@/constants/enum";
import { useLocale, useTranslations } from "next-intl";
import { useState } from "react";
import { ChartSkeleton } from "../../../../../../../components/skeletons";
import { TFunction } from "@/types";
import { useAppointmentsData } from "../../../services/queries";
import useMediaQuery from "@/hooks/use-media-query";

import CustomPieChart from "@/components/chart/pie-chart/pie-chart";

const CasesChart = () => {
  const lang = useLocale();
  const [mode, setMode] = useState("");
  const currentyear = new Date().getFullYear().toString();
  const effectiveMode = mode || currentyear;
  const t = useTranslations() as TFunction;
  const { appointmentsData, isError, isLoading } =
    useAppointmentsData(effectiveMode);
  // Set text direction based on language
  const textDirection = lang === LANGUAGES.ARABIC ? "rtl" : "ltr";

  const tempData = [
    { name: "مرتفع", value: 57 },
    { name: "متوسط", value: 41 },
    { name: "منخفض", value: 12 },
  ];

  if (isLoading) return <ChartSkeleton />;
  if (isError) {
    return (
      <div
        className="w-full mx-auto flex items-center justify-center bg-white p-4 rounded-2xl text-center border border-[#E5E6E6]"
        style={{ height: "407px" }}
      >
        <p className="text-error font-semibold">{isError.message}</p>
      </div>
    );
  }

  return (
    <div
      className={`w-full mx-auto bg-white rounded-2xl text-${textDirection} border border-neutral-100`}
    >
      <CustomPieChart
        title={t("cm.caseManagerHomePage.charts.titles.cases")}
        selectPlaceholder={t("cm.caseManagerHomePage.placeholder.yearly")}
        selectOptions={[
          { value: "2023", label: "2023" },
          { value: "2024", label: "2024" },
          { value: "2025", label: "2025" },
        ]}
        selectValue={mode}
        onSelectChange={(val) => setMode(val)}
        chartContainerClass="h-[366px]"
        data={tempData}
      />
    </div>
  );
};

export default CasesChart;
