"use server";

import { isValidationSuccess, validateFormData } from "@/lib/form-utils";
import { handleError } from "@/lib/utils";
import {
  changePasswordSchema,
  changePasswordSchemaType,
  editProfileSchema,
  editProfileSchemaType,
} from "@/schemas/settings";
import { CoreAPI } from "@/services/api/core";
import { ActionState } from "@/types";
import { TUser } from "@/types/auth";

export const onSubmitChangePassword = async (
  prevState: ActionState<null>,
  data: FormData,
): Promise<ActionState<null>> => {
  try {
    const validation = await validateFormData<changePasswordSchemaType>(
      data,
      changePasswordSchema,
    );

    if (!isValidationSuccess(validation)) {
      return {
        error:
          validation.error ||
          "The provided data is invalid. Please check your input.",
        issues: validation.issues,
      };
    }

    const { currentPassword, password, confirmPassword } = validation.data;

    const coreApi = new CoreAPI();
    const response = await coreApi.updatePassword(
      currentPassword,
      password,
      confirmPassword,
    );

    return {
      success:
        response.meta?.message ||
        "Your password has been changed successfully.",
      error: "",
      issues: [],
    };
  } catch (err) {
    return handleError(
      err,
      "Failed to change your password. Please try again later.",
    );
  }
};

export async function onSubmitEditForm(
  prevState: ActionState<TUser>,
  data: FormData,
): Promise<ActionState<TUser>> {
  try {
    const validation = await validateFormData<editProfileSchemaType>(
      data,
      editProfileSchema,
    );

    if (!isValidationSuccess(validation)) {
      return {
        error:
          validation.error ||
          "The provided data is invalid. Please review your profile information.",
        issues: validation.issues,
        success: "",
      };
    }

    const coreApi = new CoreAPI();
    const updatedUser = await coreApi.updateUserInfo(data);

    return {
      success: "Your profile has been updated successfully!",
      error: "",
      issues: [],
      data: updatedUser,
    };
  } catch (err) {
    return handleError(
      err,
      "Failed to update your profile. Please try again later.",
    );
  }
}
