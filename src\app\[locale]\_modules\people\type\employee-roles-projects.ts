import { ApiResponse } from "@/types";

export type TProject = {
  id: number | string;
  name: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
};

export type TRole = {
  id: number | string;
  name: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
};

export type TRoleProjectPair = {
  id: string;
  userRoleId: string;
  roleId: string;
  projectId: string;
  roleName: string;
  projectName: string;
  isDefault: boolean;
};

export type TRolesResponse = ApiResponse<TRole[]>;
export type TProjectsResponse = ApiResponse<TProject[]>;
