import { useEffect } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { filterTypeManager } from '@/lib/filter-type-manager';

export const useTableRegistration = <TData>(
  tableId: string,
  columns: ColumnDef<TData>[]
) => {
  // Register immediately, not in useEffect
  filterTypeManager.registerTable(tableId, columns as any);

  useEffect(() => {
    // Also register in useEffect to handle column changes
    filterTypeManager.registerTable(tableId, columns as any);
  }, [tableId, columns]);
};
