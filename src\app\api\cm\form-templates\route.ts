import { NextRequest, NextResponse } from "next/server";
import { cmAPI } from "@/services/api/cm";
import { createErrorResponse } from "@/utils/api-error-extractor";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    const sort = searchParams.get("sort");

    const params: {
      sort?: string;
    } = {};

    if (sort) {
      params.sort = sort;
    }

    const response = await cmAPI.getFormTemplates(params);
    return NextResponse.json(response);
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET Form Templates route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
