// app/lib/data.ts
import { normalizeError } from "@/lib/errors";
function delay(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export async function fetchPendingRequests(): Promise<number> {
  await delay(1000); // Simulate 1 second delay (optional)

  return 15; // Mock data
}

export async function fetchAverageDailyHours(): Promise<number> {
  await delay(2000); // Simulate 2 second delay (optional)
  // Uncomment below for real API fetching with axios
  // const response = await axios.get("https://api.example.com/average-daily-hours");
  // return response.data.averageDailyHours;
  return 6; // Mock data
}

export async function fetchAttendanceRate(): Promise<number> {
  await delay(1500); // Simulate 1.5 second delay (optional)
  // Uncomment below for real API fetching with axios
  // const response = await axios.get("https://api.example.com/attendance-rate");
  // return response.data.attendanceRate;
  return 213; // Mock data
}

export async function fetchTotalEmployees(): Promise<number> {
  await delay(10000); // Simulate 3 second delay (optional)
  // Uncomment below for real API fetching with axios
  // const response = await axios.get("https://api.example.com/total-employees");
  // return response.data.totalEmployees;
  return 250; // Mock data
}

export async function fetchCardData() {
  try {
    const [pendingRequests, averageDailyHours, attendanceRate, totalEmployees] =
      await Promise.all([
        fetchPendingRequests(),
        fetchAverageDailyHours(),
        fetchAttendanceRate(),
        fetchTotalEmployees(),
      ]);
    return {
      pendingRequests,
      averageDailyHours,
      attendanceRate,
      totalEmployees,
    };
  } catch (error) {
    throw normalizeError(error);
  }
}
