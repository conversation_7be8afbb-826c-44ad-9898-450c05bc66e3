"use server";

import { availabilitySchema } from "@/app/[locale]/_modules/cm/schemas/availability-schema";
import { AvailabilityFormData } from "@/app/[locale]/_modules/cm/types/appointment-calendar";
import { ActionState, TFunction } from "@/types";
import { getTranslations } from "next-intl/server";

// A fake "database" stored in-memory.
let fakeDatabase: AvailabilityFormData | null = null;

export async function submitAvailability(
  prevState: ActionState<AvailabilityFormData>,
  data: AvailabilityFormData,
): Promise<ActionState<AvailabilityFormData>> {
  // Simulate network latency
  await new Promise((resolve) => setTimeout(resolve, 1000));

  const t = (await getTranslations()) as TFunction;
  const results = availabilitySchema(t).safeParse(data);

  if (!results.success) {
    const errorMsg = results.error.message || "Invalid Schema validation";
    const issues = results.error.issues?.map((issue) => issue.message) || [];
    return { error: errorMsg, issues };
  }

  console.log("results", results);

  const { availability } = results.data;
  console.log("doctor availability times", availability);

  // Simulate saving data in the "backend"
  fakeDatabase = data;
  console.log("Fake backend saved data:", fakeDatabase);

  return {
    success: "Updated successfully 😊",
    error: null,
    issues: [],
    data: fakeDatabase,
  };
}
