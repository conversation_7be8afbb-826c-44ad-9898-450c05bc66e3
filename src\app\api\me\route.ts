// app/api/me/route.ts
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { peopleService } from "@/services/api/people";
import { createErrorResponse } from "@/utils/api-error-extractor";

export async function GET(_req: NextRequest) {
  try {
    const profile = await peopleService.getMe("user_roles.role");
    return NextResponse.json(profile);
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET user profile id route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
