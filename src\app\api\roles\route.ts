import { NextRequest, NextResponse } from "next/server";
import { coreAPI } from "@/services/api/core";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sort = searchParams.get("sort") || "-id";

    const response = await coreAPI.getRoles(sort);
    return NextResponse.json(response);
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";

    return NextResponse.json(
      { error: "Failed to fetch roles", message: errorMessage },
      { status: 500 },
    );
  }
}
