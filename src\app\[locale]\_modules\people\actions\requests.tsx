"use server";

import { peopleService } from "@/services/api/people";
import { ApprovalActionResponse } from "../type/approval-request";

export async function acceptRequest(
  id: string,
  comment: string = "",
): Promise<ApprovalActionResponse> {
  try {
    // The id here is the approval_request_id
    const result = await peopleService.approveLeaveRequest(id, comment);
    return { data: result.data };
  } catch (err) {
    return {
      error:
        err instanceof Error
          ? err
          : new Error("Unknown error during accept request"),
    };
  }
}

export async function rejectRequest(
  id: string,
  comment: string = "",
): Promise<ApprovalActionResponse> {
  try {
    // The id here is the approval_request_id
    const result = await peopleService._rejectLeaveRequest(id, comment);
    return { data: result.data };
  } catch (err) {
    return {
      error:
        err instanceof Error
          ? err
          : new Error("Unknown error during reject request"),
    };
  }
}
