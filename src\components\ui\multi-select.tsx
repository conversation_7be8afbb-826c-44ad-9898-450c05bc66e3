"use client";

import * as React from "react";
import { Check, ChevronDown, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { TFunction } from "@/types";

type MultiSelectOption = {
  value: string;
  label: string;
  disabled?: boolean;
};

type MultiSelectProps = {
  options: MultiSelectOption[];
  value?: string[];
  onValueChange: (value: string[]) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  maxSelected?: number;
  searchPlaceholder?: string;
  emptyText?: string;
  t?: TFunction;
};

export function MultiSelect({
  options,
  value = [],
  onValueChange,
  placeholder,
  disabled = false,
  className,
  maxSelected,
  searchPlaceholder,
  emptyText,
  t,
}: MultiSelectProps) {
  const [open, setOpen] = React.useState(false);

  // Use translations with fallbacks
  const finalPlaceholder =
    placeholder || t?.("common.form.select.placeholder") || "Select items...";
  const finalSearchPlaceholder =
    searchPlaceholder || t?.("common.navbar.search.placeholder") || "Search...";
  const finalEmptyText =
    emptyText || t?.("common.Table.noResults") || "No items found.";

  const handleSelect = (selectedValue: string) => {
    if (value.includes(selectedValue)) {
      onValueChange(value.filter((item) => item !== selectedValue));
    } else {
      if (maxSelected && value.length >= maxSelected) {
        return;
      }
      onValueChange([...value, selectedValue]);
    }
  };

  const handleRemove = (valueToRemove: string) => {
    onValueChange(value.filter((item) => item !== valueToRemove));
  };

  const selectedOptions = options.filter((option) =>
    value.includes(option.value),
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-full justify-between min-h-10 h-auto", className)}
          disabled={disabled}
        >
          <div className="flex flex-wrap gap-1 flex-1">
            {selectedOptions.length === 0 ? (
              <span className="text-muted-foreground">{finalPlaceholder}</span>
            ) : (
              selectedOptions.map((option) => (
                <Badge
                  key={option.value}
                  variant="secondary"
                  className="ms-1 mb-1 flex gap-2 text-white hover:bg-secondary"
                >
                  {option.label}
                  <span
                    role="button"
                    tabIndex={0}
                    className="ml-1 h-auto w-auto p-0 ring-offset-background rounded-full outline-none group"
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        handleRemove(option.value);
                      }
                    }}
                    onMouseDown={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                    onClick={() => handleRemove(option.value)}
                  >
                    <X className="h-3 w-3 text-muted-foreground group-hover:text-green-500" />
                  </span>
                </Badge>
              ))
            )}
          </div>
          <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <Command>
          <CommandInput placeholder={finalSearchPlaceholder} />
          <CommandList>
            <CommandEmpty>{finalEmptyText}</CommandEmpty>
            <CommandGroup>
              {options.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.label.toLocaleLowerCase()}
                  disabled={option.disabled}
                  onSelect={() => handleSelect(option.value)}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value.includes(option.value)
                        ? "opacity-100"
                        : "opacity-0",
                    )}
                  />
                  {option.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
