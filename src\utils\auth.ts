"use client";

interface ErrorWithStatus {
  status?: number;
  message?: string;
  data?: {
    error?: string;
  };
}

// Checks if an error indicates token expiration
export const isTokenExpirationError = (
  error: ErrorWithStatus | string | unknown,
): boolean => {
  // Handle string errors
  if (typeof error === "string") {
    return (
      error.includes("Expired access token") ||
      error.includes("jwt expired") ||
      error.includes("Token expired")
    );
  }

  // Handle error objects
  if (error && typeof error === "object") {
    const errorObj = error as ErrorWithStatus;

    // Check status code first (401 Unauthorized)
    if (errorObj.status === 401) {
      return true;
    }

    // Check error message
    const errorMessage = errorObj.message;
    if (errorMessage && typeof errorMessage === "string") {
      return (
        errorMessage.includes("Expired access token") ||
        errorMessage.includes("jwt expired") ||
        errorMessage.includes("invalid token") ||
        errorMessage.includes("Token expired") ||
        errorMessage.includes("Unauthorized")
      );
    }
  }

  // Check for error response data
  const errorWithData = error as ErrorWithStatus;
  if (errorWithData?.data?.error) {
    const dataError = errorWithData.data.error;
    if (
      typeof dataError === "string" &&
      (dataError.includes("Expired access token") ||
        dataError.includes("Unauthorized"))
    ) {
      return true;
    }
  }

  return false;
};
