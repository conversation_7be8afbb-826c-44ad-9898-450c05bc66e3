"use client";

import React, { startTransition, useActionState, useEffect, useState, useTransition } from "react";
import { useFormTemplates } from "@/app/[locale]/_modules/cm/hooks/useFormTemplates";
import { useFormSections } from "@/app/[locale]/_modules/cm/hooks/useFormSections";
import { useFormSubmissions } from "@/app/[locale]/_modules/cm/hooks/useFormSubmissions";
import { useCase } from "@/app/[locale]/_modules/cm/hooks/useCase";
import { DynamicForm } from "./dynamic-form";
import { StepTimelineSidebar } from "./step-timeline-sidebar";
import { createFormSubmissionAction } from "@/server/actions/form-submissions";
import { CreateFormSubmissionData, FormSubmissionResponse, UpdateFormSubmissionData } from "@/types/cm";
import { ActionState } from "@/types";

type FormsContainerProps = {
  caseId?: string;
  mode: "new" | "edit";
};

export function FormsContainer({ caseId, mode }: FormsContainerProps) {
  // Multi-template workflow state
  const [currentTemplateIndex, setCurrentTemplateIndex] = useState(0);
  const [currentSectionIndex, setCurrentSectionIndex] = useState(0);
  console.log(
    "🚀 ~ FormsContainer ~ currentSectionIndex:",
    currentSectionIndex,
  );
  const [completedTemplates, setCompletedTemplates] = useState<Set<number>>(
    new Set(),
  );
  const [completedSections, setCompletedSections] = useState<Set<string>>(
    new Set(),
  );

  const initialState: ActionState<FormSubmissionResponse> = {
    error: "",
    success: "",
    issues: [],
    data: null,
  };

  const [state, submitAction, isPending] = useActionState(
    createFormSubmissionAction,
    initialState,
  );

  // Fetch form templates using SWR (sorted by sequence_order)
  const {
    templates,
    meta,
    isLoading: templatesLoading,
    error: templatesError,
  } = useFormTemplates({
    sort: "sequence_order",
  });

  // Get current template based on workflow progression
  const currentTemplate = templates[currentTemplateIndex];
  const currentTemplateId = currentTemplate?.id;

  // Fetch form sections for the current template (sorted by display_order)
  const {
    sections,
    meta: sectionsMeta,
    isLoading: sectionsLoading,
    error: sectionsError,
  } = useFormSections({
    formTemplateId: currentTemplateId,
    sort: "display_order",
  });
  const currentSection = sections[currentSectionIndex];

  // Fetch form submissions for the current case and template
  const {
    submissions,
    mutate: mutateSubmissions,
    meta: submissionsMeta,
    isLoading: submissionsLoading,
    error: submissionsError,
  } = useFormSubmissions({
    caseId: mode === "edit" ? caseId : undefined,
    formSectionId: currentSectionIndex,
  });
  console.log("🚀 ~ FormsContainer ~ submissions:", submissions);

  // Fetch case data only if in edit mode and caseId is provided
  const {
    case: currentCase,
    isLoading: caseLoading,
    error: caseError,
  } = useCase(mode === "edit" ? caseId : undefined);

  // Determine overall loading state
  const isLoading =
    templatesLoading ||
    sectionsLoading ||
    submissionsLoading ||
    (mode === "edit" && caseId && caseLoading);

  // Handle errors
  if (templatesError) {
    return (
      <div className="p-4 text-red-600">
        Failed to load form templates: {templatesError.message}
      </div>
    );
  }

  if (sectionsError) {
    return (
      <div className="p-4 text-red-600">
        Failed to load form sections: {sectionsError.message}
      </div>
    );
  }

  if (submissionsError) {
    return (
      <div className="p-4 text-red-600">
        Failed to load form submissions: {submissionsError.message}
      </div>
    );
  }

  if (caseError && mode === "edit") {
    return (
      <div className="p-4 text-red-600">
        Failed to load case data: {caseError.message}
      </div>
    );
  }

    useEffect(() => {
      if (state?.success) {
        showToast("success", state.success);

        if (mode === "add") {
          form.reset(defaultFormValues);
          setTimeout(() => {
            setSelectKey((prev) => prev + 1);
          }, 0);
        }
        mutateSubmissions();

        if (onSuccess) {
          onSuccess();
        }
      }
    }, [state, form, defaultFormValues, mutateEmployeesList, mode, onSuccess]);

  // Form submission handlers
  const handleFormSubmit = async (
    data: Record<string, unknown>,
  ): Promise<boolean> => {
    try {
      const submissionData: CreateFormSubmissionData = {
        form_section_id: currentSection.id,
        form_data: data,
        // Include case_id if in edit mode
        ...(mode === "edit" && caseId && { case_id: caseId }),
        // For new cases, we might need to provide case creation data
        ...(mode === "new" && {
          case_type: "standard", // Default case type
          priority_level: 1, // Default priority
          confidentiality_level: 1, // Default confidentiality
        }),
      };

      console.log("🚀 ~ handleFormSubmit ~ submissionData:", submissionData);

      // const result = await createFormSubmissionAction(
      //   { success: null, error: null },
      //   submissionData,
      // );

          startTransition(() => {
            submitAction(formData);
          });

      if (result.success) {
        // Mark current section as completed using section ID
        

        // If this is the first submission in new mode, we should get the case_id from the response
        if (mode === "new" && result.data?.data?.attributes?.case_id) {
          const newCaseId = result.data.data.attributes.case_id;
          console.log(
            "🚀 ~ handleFormSubmit ~ New case created with ID:",
            newCaseId,
          );

          // TODO: We should update the URL or store the case_id for subsequent submissions
          // This would require updating the router or using a state management solution
          // For now, we'll just log it and continue with the flow
        }

        // Refresh submissions state
        await mutateSubmissions();
        return true;
      } else {
        console.error("Form submission failed:", result.error);
        return false;
      }
    } catch (error) {
      console.error("Form submission error:", error);
      return false;
    }
  };

  const handleNext = () => {
    // Use actual sections count from template attributes
    const currentTemplateSectionsCount =
      currentTemplate?.attributes.sections_count || sections.length;

    if (currentSectionIndex < currentTemplateSectionsCount - 1) {
      // Move to next section within current template
      setCurrentSectionIndex(currentSectionIndex + 1);
    } else if (currentTemplateIndex < templates.length - 1) {
      // All sections completed in current template, move to next template
      setCompletedTemplates((prev) => new Set([...prev, currentTemplateIndex]));
      setCurrentTemplateIndex(currentTemplateIndex + 1);
      setCurrentSectionIndex(0); // Reset to first section of next template
    }
  };

  const handlePrevious = () => {
    if (currentSectionIndex > 0) {
      // Move to previous section within current template
      setCurrentSectionIndex(currentSectionIndex - 1);
    } else if (currentTemplateIndex > 0) {
      // Move to previous template's last section
      const previousTemplate = templates[currentTemplateIndex - 1];
      const previousTemplateSectionsCount =
        previousTemplate?.attributes.sections_count || 1;

      setCurrentTemplateIndex(currentTemplateIndex - 1);
      setCurrentSectionIndex(previousTemplateSectionsCount - 1); // Go to last section of previous template
    }
  };

  const handleSectionClick = (index: number) => {
    setCurrentSectionIndex(index);
  };

  const handleTemplateChange = (templateIndex: number) => {
    setCurrentTemplateIndex(templateIndex);
    setCurrentSectionIndex(0);
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  // If no sections available, show message
  if (sections.length === 0) {
    return (
      <div className="p-8 text-center">
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No form sections available
        </h3>
        <p className="text-gray-600">
          Please configure form sections for this template.
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-row-reverse gap-8 bg-[hsl(var(--primary),0)]">
      <div className="w-80 flex flex-col gap-8 rounded-xl bg-[#F5F9F4] p-4">
        <header className="flex flex-col gap-2">
          <h2 className="text-lg font-bold text-secondary">عنوان رئيسي</h2>
          <p className="text-sm font-normal text-main-2/60">
            هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة، لقد تم توليد هذا
            النص من مولد النص العربى،
          </p>
        </header>
        {/* Step Timeline Sidebar */}
        <StepTimelineSidebar
          caseId={caseId}
          templates={templates}
          currentTemplateIndex={currentTemplateIndex}
          currentSectionIndex={currentSectionIndex}
          completedTemplates={completedTemplates}
          completedSections={completedSections}
          onTemplateClick={handleTemplateChange}
          onSectionClick={handleSectionClick}
        />
      </div>

      {/* Main Form Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="mb-10">
          <div className="flex items-start gap-1 flex-col">
            <h1 className="text-lg text-main-2 font-bold">
              {currentSection.attributes.name}
            </h1>
            <p className="text-sm font-normal text-main-2/60">
              {currentSection.attributes.description ||
                "Complete this section to continue"}
            </p>
          </div>
        </div>

        {/* Form Content */}
        <div className="flex-1">
          <div>
            <DynamicForm
              formSectionId={currentSection.id}
              caseId={mode === "edit" ? caseId : undefined}
              submission={submissions}
              submissionsLoading={submissionsLoading}
              onSubmit={handleFormSubmit}
              onNext={handleNext}
              onPrevious={handlePrevious}
              isSubmitting={isPending}
              mode={mode === "new" ? "create" : "edit"}
              canGoNext={
                currentSectionIndex <
                  (currentTemplate?.attributes.sections_count ||
                    sections.length) -
                    1 || currentTemplateIndex < templates.length - 1
              }
              canGoPrevious={
                currentSectionIndex > 0 || currentTemplateIndex > 0
              }
              isFirstSection={
                currentSectionIndex === 0 && currentTemplateIndex === 0
              }
              isLastSection={
                currentSectionIndex ===
                  (currentTemplate?.attributes.sections_count ||
                    sections.length) -
                    1 && currentTemplateIndex === templates.length - 1
              }
              currentSectionIndex={currentSectionIndex}
              totalSections={
                currentTemplate?.attributes.sections_count || sections.length
              }
            />
          </div>
        </div>
      </div>
    </div>
  );
}
