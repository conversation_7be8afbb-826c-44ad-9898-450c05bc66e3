"use server";

import { cmAPI } from "@/services/api/cm";
import {
  CreateFormSubmissionData,
  UpdateFormSubmissionData,
  FormSubmissionResponse,
} from "@/types/cm";
import { ActionState } from "@/types";
import { revalidateTag } from "next/cache";
import { handleError } from "@/lib/utils";

// Create a new form submission
export async function createFormSubmissionAction(
  _prevState: ActionState<FormSubmissionResponse>,
  data: CreateFormSubmissionData,
): Promise<ActionState<FormSubmissionResponse>> {
  console.log("🚀 ~ createFormSubmissionAction ~ data:", data);
  try {
    const response = await cmAPI.createFormSubmission(data);

    return {
      success: "Form submission created successfully",
      data: response,
    };
  } catch (error) {
    console.error("Error creating form submission:", error);
    return handleError(error, "Failed to create form submission");
  }
}

// Update an existing form submission
export async function updateFormSubmissionAction(
  _prevState: ActionState<FormSubmissionResponse>,
  submissionId: string,
  data: UpdateFormSubmissionData,
): Promise<ActionState<FormSubmissionResponse>> {
  try {
    const response = await cmAPI.updateFormSubmission(submissionId, data);

    return {
      success: "Form submission updated successfully",
      data: response,
    };
  } catch (error) {
    console.error("Error updating form submission:", error);
    return handleError(error, "Failed to update form submission");
  }
}
