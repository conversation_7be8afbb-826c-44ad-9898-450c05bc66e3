"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import React from "react";

const CasesHeader = ({ description }: { description: string }) => {
  const t = useTranslations();
  const lang = useLocale();
  const router = useRouter();

  const handleNewCase = () => {
    router.push(`/${lang}/cm/cases/forms?mode=new`);
  };

  return (
    <div className="flex justify-between items-center">
      <h2 className="max-md:pt-2 md:pb-5 text-secondary max-w-[301px] leading-[120%]">
        {description}
      </h2>
      <Button
        onClick={handleNewCase}
        className="text-sm font-medium min-h-12 min-w-[132px]"
      >
        <Plus />
        {t("cm.cases.actions.createCase")}
      </Button>
    </div>
  );
};

export default CasesHeader;
