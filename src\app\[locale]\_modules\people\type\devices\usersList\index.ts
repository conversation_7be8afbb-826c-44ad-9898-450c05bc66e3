import { TEmployee } from "../../employee";

// Root response interface
export type TIncludedEmployee = {
  id: string;
  type: string;
  attributes: TEmployee;
};

export type TDeviceUsersResponse = {
  data: TDeviceUser[];
  included?: TIncludedEmployee[];
  meta: TMetaData;
};

// Represents each device user
export type TDeviceUser = {
  id: string;
  type: "attendance_device_user";
  attributes: TDeviceUserAttributes;
  relationships: {
    mapping?: {
      data: {
        id: string;
        type: "employee_device_mapping";
      } | null;
    };
    device: {
      data: {
        id: string;
        type: "attendance_device";
      };
    };
    employee: {
      data: {
        id: string;
        type: string;
      } | null;
    };
  };
};

// Attributes of a device user
export type TDeviceUserAttributes = {
  user_id: string;
  name: string;
  privilege: number;
  password: string | null;
  group_id: string | null;
  card_number: number;
  device_id: number;
  "admin?": boolean;
  "user?": boolean;
  "has_card?": boolean;
  "has_password?": boolean;
  verification_methods: string[];
};

// Metadata section
export type TMetaData = {
  pagination: TPaginationInfo;
  device: DeviceInfo;
};

// Pagination metadata - Updated to match API response format
export type TPaginationInfo = {
  count: number;
  page: number;
  limit: number;
  from: number;
  to: number;
};

// Device metadata
export type DeviceInfo = {
  id: number;
  name: string;
  adapter_type: string;
  total_users: number;
  supports_user_management: boolean;
};

// Device command types
export type TDeviceCommandResponse = {
  success: boolean;
  message: string;
  command: string;
  device_id: string;
  executed_at: string;
  result?: Record<string, unknown>;
  error?: string;
};

export type TAvailableCommand = {
  id: string;
  type: "attendance_device_command";
  attributes: {
    name: string;
    display_name: string;
    description: string;
    parameters: Record<string, TCommandParameter>;
  };
  relationships: {
    device: {
      data: {
        id: string;
        type: "attendance_device";
      };
    };
  };
};

export type TCommandParameter = {
  type: "string" | "integer" | "boolean";
  required: boolean;
  default: string | number | boolean | null;
  description: string;
};

export type TAvailableCommandsResponse = {
  data: TAvailableCommand[];
};

// Device user creation/update response types
export type TDeviceUserResponse = {
  data: TDeviceUser;
  included?: TIncludedEmployee[];
};

// Device user mapping types
export type TDeviceUserMappingAttributes = {
  employee_id: string;
  device_user_id: string;
  notes: string;
  created_at: string;
  updated_at: string;
};

export type TDeviceUserMapping = {
  id: string;
  type: "employee_device_mapping";
  attributes: TDeviceUserMappingAttributes;
  relationships: {
    employee: {
      data: {
        id: string;
        type: string;
      };
    };
    device_user: {
      data: {
        id: string;
        type: "attendance_device_user";
      };
    };
    device: {
      data: {
        id: string;
        type: "attendance_device";
      };
    };
  };
};

export type TDeviceUserMappingResponse = {
  data: TDeviceUserMapping;
  included?: (TIncludedEmployee | TDeviceUser)[];
};

// Command execution response
export type TCommandExecutionResponse = {
  data: {
    id: string;
    type: "device_command_execution";
    attributes: {
      command: string;
      parameters: Record<string, string>;
      status: "pending" | "executing" | "completed" | "failed";
      result?: Record<string, unknown>;
      error?: string;
      executed_at: string;
      completed_at?: string;
    };
  };
};
