import { z } from "zod";
import { TFunction } from "@/types";

export const deviceSchema = (t: TFunction) => {
  return z.object({
    name: z.string().min(1, {
      message: t(
        "people.devices-page.add-device-dialog.form.device-name.error.required",
      ),
    }),

    adapter_type: z.string().min(1, {
      message: t(
        "people.devices-page.add-device-dialog.form.device-type.error.required",
      ),
    }),

    ip_address: z
      .string()
      .min(1, {
        message: t(
          "people.devices-page.add-device-dialog.form.ip-address.error.required",
        ),
      })
      .regex(
        /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
        {
          message: t(
            "people.devices-page.add-device-dialog.form.ip-address.error.invalid",
          ),
        },
      ),

    port: z
      .string()
      .min(1, {
        message: t(
          "people.devices-page.add-device-dialog.form.port.error.required",
        ),
      })
      .refine((val) => !isNaN(Number(val)), {
        message: t(
          "people.devices-page.add-device-dialog.form.port.error.invalid",
        ),
      })
      .refine((val) => Number(val) >= 1 && Number(val) <= 65535, {
        message: t(
          "people.devices-page.add-device-dialog.form.port.error.range",
        ),
      }),

    location: z.string().min(1, {
      message: t(
        "people.devices-page.add-device-dialog.form.location.error.required",
      ),
    }),

    additional_settings: z
      .array(
        z.object({
          id: z.string(),
          type: z.string(),
          value: z.string(),
        }),
      )
      .optional()
      .default([]),
  });
};

export type DeviceSchemaType = z.infer<ReturnType<typeof deviceSchema>>;
