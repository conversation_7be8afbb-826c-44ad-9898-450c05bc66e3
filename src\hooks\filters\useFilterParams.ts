import { useSearchParams, usePathname } from "next/navigation";
import qs from "qs";
import { parseRansackKey, isArrayOperator, parseFilterValues } from "@/components/table/filter/ransack-utils";

// Migration function for legacy table IDs
const migrateTableData = () => {
  if (typeof window === 'undefined') return; // Skip on server-side

  const oldTableId = "attendance-requests";
  const newTableId = "attendance-events";

  const keysToMigrate = [
    `table-filters-${oldTableId}`,
    `table-columns-${oldTableId}`,
    `table-sorting-${oldTableId}`,
    `table-pagination-${oldTableId}`,
    `table-state-${oldTableId}`
  ];

  keysToMigrate.forEach(oldKey => {
    const data = localStorage.getItem(oldKey);
    if (data) {
      const newKey = oldKey.replace(oldTableId, newTableId);
      localStorage.setItem(newKey, data);
      localStorage.removeItem(oldKey);
      console.log(`Migrated ${oldKey} → ${newKey}`);
    }
  });
};

export interface FilterParams {
  filters: Record<string, any>;
  hasFilters: boolean;
  filterCount: number;
  rawFilterParams: URLSearchParams;
}

export const useFilterParams = (tableId?: string): FilterParams => {
  // Run migration for attendance-requests → attendance-events
  if (tableId === "attendance-events") {
    migrateTableData();
  }

  const searchParams = useSearchParams();

  const searchString = searchParams.toString();
  if (!searchString) {
    return {
      filters: {},
      hasFilters: false,
      filterCount: 0,
      rawFilterParams: new URLSearchParams(),
    };
  }

  const allParams = qs.parse(searchString, {
    duplicates: 'combine',
    ignoreQueryPrefix: true
  });

  const filterParams = allParams.filter || {};

  // Filter valid ransack keys
  const validatedFilters: Record<string, any> = {};
  Object.entries(filterParams).forEach(([key, value]) => {
    const parsedKey = parseRansackKey(key);
    if (parsedKey || key.includes("_")) {
      if (parsedKey && isArrayOperator(parsedKey.operator)) return;
      validatedFilters[key] = value;
    }
  });

  // Use universal helper - handles ALL type conversion
  // If tableId is provided, use it for type conversion, otherwise return raw values
  const filters = tableId
    ? parseFilterValues(validatedFilters, tableId)
    : validatedFilters;

  const rawFilterParams = new URLSearchParams();
  Object.entries(filters).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      value.forEach(v => rawFilterParams.append(`filter[${key}]`, String(v)));
    } else {
      rawFilterParams.append(`filter[${key}]`, String(value));
    }
  });

  return {
    filters,
    hasFilters: Object.keys(filters).length > 0,
    filterCount: Object.keys(filters).length,
    rawFilterParams,
  };
};
