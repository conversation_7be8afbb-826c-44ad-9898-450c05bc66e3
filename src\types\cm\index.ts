import { number } from "zod";
import { ApiResponse } from "../index";

// Case Management Types
export type TCaseAttributes = {
  status: string;
  approval_status: string | null;
  started_at: string;
  closed_at: string | null;
  created_at: string;
  updated_at: string;
  case_number: string;
  case_type: string;
  priority_level: string;
  confidentiality_level: string;
  notes: string | null;
  beneficiary_name: string;
  beneficiary_age: number;
  beneficiary_gender: string;
  beneficiary_nationality: string;
  beneficiary_phone: string;
  beneficiary_id_number: string;
  beneficiary_date_of_birth: string;
  beneficiary_city: string;
  age_in_days: number;
  is_active: boolean;
  is_closed: boolean;
  is_suspended: boolean;
  requires_approval: boolean;
  is_approved: boolean;
  is_rejected: boolean;
  can_be_closed: boolean;
};

export type TCasesResponse = {
  data: {
    id: string;
    type: string;
    attributes: TCaseAttributes;
  }[];
  meta: {
    pagination: {
      count: number;
      from: number;
      limit: number;
      page: number;
      to: number;
    };
  };
};

export type TCaseResponse = ApiResponse<TCaseAttributes>;

// Form Template Types
export type FormTemplate = {
  id: string;
  type: "form_template";
  attributes: {
    name: string;
    title: string;
    description: string;
    active: boolean;
    target_role: string;
    sequence_order: number;
    prerequisite_forms: string[]; // Array of prerequisite form names
    project_id: number;
    created_at: string;
    updated_at: string;
    form_type: string;
    sections_count: number;
    fields_count: number;
    estimated_completion_time: number;
  };
};

export type FormTemplatesResponse = {
  data: FormTemplate[];
  meta: {
    pagination: {
      count: number;
      page: number;
      limit: number;
      from: number;
      to: number;
    };
  };
};

export type FormSection = {
  id: string;
  type: "form_section";
  attributes: {
    name: string;
    title: string;
    description: string;
    display_order: number;
    is_required: boolean;
    visible: boolean;
    display_condition_field_id: string | null;
    display_condition_value: string | null;
    display_condition_user_role: string | null;
    display_condition_project_type: string | null;
    created_at: string;
    updated_at: string;
    fields_count: number;
    required_fields_count: number;
    has_conditional_logic: boolean;
    is_visible: boolean;
    form_template_id: number;
    form_template_name: string;
  };
  relationships: {
    form_template: {
      data: {
        id: string;
        type: "form_template";
      };
    };
    form_fields: {
      data: Array<{
        id: string;
        type: "form_field";
      }>;
    };
  };
};

export type FormSectionsResponse = {
  data: FormSection[];
  meta: {
    pagination: {
      count: number;
      page: number;
      limit: number;
      from: number;
      to: number;
    };
  };
};

// Updated to match actual API response structure
export type FormFieldOption = {
  id: number;
  option_key: string;
  option_value: string;
  display_order: number;
  active: boolean;
  metadata: Record<string, any>;
};

export type FormFieldValidation = {
  id: number;
  validation_type: string;
  validation_value: any;
  error_message: string;
};

export type FormField = {
  field_name: string;
  label: string;
  field_type: string;
  data_type: string;
  display_order: number;
  required: boolean;
  visible: boolean;
  help_text: string | null;
  placeholder: string | null;
  validation_rules: Record<string, unknown>;
  field_config: Record<string, unknown>;
  calculation_formula: string | null;
  lookup_source_type: string | null;
  lookup_source_config: string | null;
  parent_field_id: string | null;
  depends_on: string | null;
  auto_calculate: boolean;
  created_at: string;
  updated_at: string;
  is_calculated: boolean;
  is_lookup: boolean;
  has_options: boolean;
  options_count: number;
  validations_count: number;
  has_dependencies: boolean;
  form_section_id: number;
  form_section_name: string;
  form_template_id: number;
  form_template_name: string;
  field_type_definition_name: string | null;
  field_type_definition_input_type: string | null;
};

// Legacy type for backward compatibility - maps to new structure
export type CMFormField = {
  id: string;
  type: "form_field";
  attributes: {
    name: string;
    label: string;
    field_type: string;
    is_required: boolean;
    sequence_order: number;
    validation_rules?: Record<string, any>;
    options?: Array<{ value: string; label: string }>;
    default_value?: any;
    placeholder?: string;
    help_text?: string;
    form_section_id: string;
    current_value?: any;
    created_at: string;
    updated_at: string;
  };
};

export type FormFieldsResponse = {
  data: FormField[];
  meta?: {
    pagination?: {
      count: number;
      page: number;
      limit: number;
      from: number;
      to: number;
    };
    // Progressive loading metadata
    section_info?: {
      section_type: "form" | "list";
      total_fields: number;
      loaded_fields: number;
      has_conditional_fields: boolean;
      has_calculated_fields: boolean;
    };
    field_dependencies?: Record<string, string[]>; // Map of field dependencies
  };
};

// Legacy response type for backward compatibility
export type LegacyFormFieldsResponse = {
  data: CMFormField[];
  meta: {
    pagination: {
      count: number;
      page: number;
      limit: number;
      from: number;
      to: number;
    };
  };
};

// Lookup options for dynamic fields
export type LookupOption = {
  value: string;
  label: string;
  metadata?: Record<string, any>;
};

export type LookupOptionsResponse = {
  data: LookupOption[];
  meta: {
    pagination: {
      count: number;
      page: number;
      limit: number;
      from: number;
      to: number;
    };
    search_term?: string;
    field_id: string;
  };
};

// Field calculation request/response
export type CalculateFieldRequest = {
  field_id: string;
  form_data: Record<string, any>;
  case_id?: string;
};

export type CalculateFieldResponse = {
  field_id: string;
  calculated_value: any;
  success: boolean;
  error?: string;
};

export type FormSubmission = {
  id: string;
  type: "form_submission";
  attributes: {
    case_id: number;
    form_template_id: number;
    form_section_id: number;
    form_data: Record<string, any>;
    status: string;
    submitted_at: string | null;
    completed_at: string | null;
    created_by_id: number;
    updated_by_id: number | null;
    created_at: string;
    updated_at: string;
    completion_percentage: number;
    can_be_submitted: boolean;
    is_draft: boolean;
    is_in_progress: boolean;
    is_submitted: boolean;
    is_completed: boolean;
    validation_errors: any[];
    section_completion_status: Array<{
      section_id: number;
      section_name: string;
      completion_percentage: number;
      required_fields_completed: boolean;
    }>;
  };
  relationships: {
    case: {
      data: {
        id: string;
        type: "case";
      };
    };
    form_template: {
      data: {
        id: string;
        type: "form_template";
      };
    };
    form_section: {
      data: {
        id: string;
        type: "form_section";
      };
    };
    created_by: {
      data: {
        id: string;
        type: "remote_user";
      } | null;
    };
    updated_by: {
      data: {
        id: string;
        type: "remote_user";
      } | null;
    };
  };
};

export type FormSubmissionsResponse = {
  data: FormSubmission[];
  meta: {
    pagination: {
      count: number;
      page: number;
      limit: number;
      from: number;
      to: number;
    };
  };
};

export type FormSubmissionResponse = {
  data: FormSubmission;
  included?: any[];
};

export type CreateFormSubmissionData = {
  case_id?: string | number; // Optional for new cases, required for existing cases
  form_section_id: string | number;
  assigned_user_id?: string;
  case_type?: string;
  priority_level?: string | number;
  confidentiality_level?: string | number;
  form_data: Record<string, any>;
};

export type UpdateFormSubmissionData = {
  form_data?: Record<string, any>;
  status?: string;
  // Add other fields that can be updated
};

export type Case = {
  id: string;
  type: "case";
  attributes: TCaseAttributes;
  relationships: {
    assigned_user: {
      data: {
        id: string;
        type: "remote_user";
      };
    };
    created_by_user: {
      data: {
        id: string;
        type: "remote_user";
      };
    };
    project: {
      data: {
        id: string;
        type: "remote_project";
      };
    };
  };
};

export type WorkflowStep = {
  id: string;
  name: string;
  title: string;
  description?: string;
  template_id: string;
  section_id?: string;
  sequence_order: number;
  status: "completed" | "current" | "pending" | "locked";
  observations_count?: number;
  fields_count?: number;
  completion_percentage?: number;
  is_required: boolean;
};

export type CaseProgress = {
  id: string;
  type: "case_progress";
  attributes: {
    case_id: string;
    current_step_id: string;
    current_template_id: string;
    current_section_id?: string;
    total_steps: number;
    completed_steps: number;
    progress_percentage: number;
    is_completed: boolean;
    workflow_steps: WorkflowStep[];
    updated_at: string;
  };
};
