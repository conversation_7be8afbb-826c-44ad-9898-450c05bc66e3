"use client";

import { useState } from "react";
import { useToastMessage } from "@/hooks/use-toast-message";
import {
  acceptRequest,
  rejectRequest as rejectRequestAction,
} from "../actions/requests";
import { withdrawLeaveRequest as withdrawLeaveRequestAction } from "../actions/leave-requests";
import {
  ApprovalActionResponse,
  ApprovalRequestData,
} from "../type/approval-request";
import { useTranslations } from "next-intl";

type ApprovalMessages = {
  approveSuccess: string;
  approveError: string;
  rejectSuccess: string;
  rejectError: string;
  approveFinalSuccess?: string;
  withdrawSuccess?: string;
  withdrawError?: string;
};

/**
 * A global hook for handling approval requests (leaves, salaries, expenses, etc.)
 * Can be used across any component that needs to approve/reject/withdraw requests
 */
export const useApprovalMutations = (messages?: ApprovalMessages) => {
  const [isApproving, setIsApproving] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);
  const [isWithdrawing, setIsWithdrawing] = useState(false);
  const { showToast } = useToastMessage();
  const t = useTranslations();

  // Global default messages - can be overridden by passing custom messages
  const getMessages = (): ApprovalMessages => {
    // If custom messages are provided, use them
    if (messages) {
      return messages;
    }

    // Default global translated messages - generic for any approval workflow
    return {
      approveSuccess: t("common.actions.approve-success"),
      approveError: t("common.actions.approve-error"),
      rejectSuccess: t("common.actions.reject-success"),
      rejectError: t("common.actions.reject-error"),
      approveFinalSuccess: t("common.actions.approve-final-success"),
      withdrawSuccess: t("common.actions.withdraw-success"),
      withdrawError: t("common.actions.withdraw-error"),
    };
  };

  const approveRequest = async (
    approvalRequestId: string,
    comment: string = "",
    onSuccess?: (approvalRequest?: ApprovalRequestData) => void,
    onOptimisticUpdate?: () => void,
  ) => {
    try {
      setIsApproving(true);

      // Perform optimistic update if provided
      if (onOptimisticUpdate) {
        onOptimisticUpdate();
      }

      const result = await acceptRequest(approvalRequestId, comment);

      if (result.error) {
        throw result.error;
      }

      // Check if this is the final approval or an intermediate step
      const isFinalApproval =
        result.data?.approvalRequest?.attributes?.current_step === null;

      const messages = getMessages();
      showToast(
        "success",
        isFinalApproval
          ? messages.approveFinalSuccess || messages.approveSuccess
          : messages.approveSuccess,
      );

      // Call success callback if provided, passing the approval request data
      if (onSuccess) {
        onSuccess(result.data?.approvalRequest);
      }

      return result;
    } catch (error) {
      const messages = getMessages();
      showToast(
        "error",
        error instanceof Error ? error.message : messages.approveError,
      );
      throw error;
    } finally {
      setIsApproving(false);
    }
  };

  const rejectRequest = async (
    approvalRequestId: string,
    comment: string = "",
    onSuccess?: (approvalRequest?: ApprovalRequestData) => void,
    onOptimisticUpdate?: () => void,
  ): Promise<ApprovalActionResponse> => {
    try {
      setIsRejecting(true);

      // Perform optimistic update if provided
      if (onOptimisticUpdate) {
        onOptimisticUpdate();
      }

      const result = await rejectRequestAction(approvalRequestId, comment);

      if (result.error) {
        throw result.error;
      }

      const messages = getMessages();
      showToast("success", messages.rejectSuccess);

      // Call success callback if provided, passing the approval request data
      if (onSuccess) {
        onSuccess(result.data?.approvalRequest);
      }

      return result;
    } catch (error) {
      const messages = getMessages();
      showToast(
        "error",
        error instanceof Error ? error.message : messages.rejectError,
      );
      throw error;
    } finally {
      setIsRejecting(false);
    }
  };

  const withdrawLeaveRequest = async (
    leaveId: string,
    employeeId: string,
    onSuccess?: () => void,
    onOptimisticUpdate?: () => void,
  ): Promise<ApprovalActionResponse> => {
    try {
      setIsWithdrawing(true);

      // Perform optimistic update if provided
      if (onOptimisticUpdate) {
        onOptimisticUpdate();
      }

      const result = await withdrawLeaveRequestAction(leaveId, employeeId);

      if (result.error) {
        throw result.error;
      }

      const messages = getMessages();
      showToast(
        "success",
        messages.withdrawSuccess || "Request withdrawn successfully",
      );

      // Call success callback if provided
      if (onSuccess) {
        onSuccess();
      }

      return result;
    } catch (error) {
      const messages = getMessages();
      showToast(
        "error",
        error instanceof Error
          ? error.message
          : messages.withdrawError || "Failed to withdraw request",
      );
      throw error;
    } finally {
      setIsWithdrawing(false);
    }
  };

  return {
    approveRequest,
    rejectRequest,
    withdrawLeaveRequest,
    isApproving,
    isRejecting,
    isWithdrawing,
  };
};
