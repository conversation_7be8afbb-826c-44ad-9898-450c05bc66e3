"use client";

import { useTranslations } from "next-intl";
import { useEmployeeOvertime } from "../../../hooks/employees/useEmployeeOvertime";
import { MetricCardsSkeleton } from "../../../skeletons/metric-card-skeleton";
import { MetricCard } from "../../common/metric-card";

type EmployeeOvertimeCardsProps = {
  employeeId: string;
};

export const EmployeeOvertimeCards = ({
  employeeId,
}: EmployeeOvertimeCardsProps) => {
  const t = useTranslations();

  // Fetch overtime statistics for the employee
  const { overtimeStats, isLoading, error } = useEmployeeOvertime(employeeId);

  if (isLoading) {
    return <MetricCardsSkeleton count={4} />;
  }

  if (error) {
    return (
      <div className="text-error p-4 min-h-[148px] border rounded-[20px] flex justify-center items-center">
        {error.message || "Error loading overtime data"}
      </div>
    );
  }

  if (!overtimeStats) {
    return null;
  }

  const comparisonText = t(
    "people.employees-page.profile.stats.compared-to-last-month",
  );

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      <MetricCard
        title={t("people.employees-page.profile.stats.overtime-requests")}
        value={overtimeStats.overtimeRequests.value}
        percentageChange={overtimeStats.overtimeRequests.percentageChange}
        comparisonText={comparisonText}
      />
      <MetricCard
        title={t("people.employees-page.profile.stats.approved-overtime")}
        value={overtimeStats.approvedOvertime.value}
        percentageChange={overtimeStats.approvedOvertime.percentageChange}
        comparisonText={comparisonText}
      />
      <MetricCard
        title={t("people.employees-page.profile.stats.rejected-overtime")}
        value={overtimeStats.rejectedOvertime.value}
        percentageChange={overtimeStats.rejectedOvertime.percentageChange}
        comparisonText={comparisonText}
      />
      <MetricCard
        title={t("people.employees-page.profile.stats.pending-overtime")}
        value={overtimeStats.pendingOvertime.value}
        percentageChange={overtimeStats.pendingOvertime.percentageChange}
        comparisonText={comparisonText}
      />
    </div>
  );
};
