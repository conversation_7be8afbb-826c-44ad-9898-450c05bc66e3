"use client";

import React, {
  useRef,
  useEffect,
  useMemo,
  useActionState,
  startTransition,
} from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Loader } from "lucide-react";
import { PAGES } from "@/enums";
import { ActionState, TinputField, TFunction } from "@/types";
import {
  holidaySchema,
  HolidaySchemaType,
} from "@/app/[locale]/_modules/settings/schemas/holiday-schema";
import { THoliday } from "@/types/settings/holidays";
import useFormFields from "@/hooks/useFormFields";
import FormFieldRenderer from "@/components/auth/fields/form-field-renderer";
import { addHoliday } from "@/app/[locale]/_modules/settings/actions/add-holiday";
import { updateHoliday } from "@/app/[locale]/_modules/settings/actions/update-holiday";
import { useToastMessage } from "@/hooks/use-toast-message";
import { format } from "date-fns";
import { useAttendanceExemptions } from "@/app/[locale]/_modules/people/hooks/attendance/useAttendanceExemptions";

type HolidayFormProps = {
  onSuccess: () => void;
  onCancel: () => void;
  holiday?: THoliday | null; // For edit mode
  mode?: "add" | "edit";
};

export default function HolidayForm({
  onSuccess,
  onCancel,
  holiday = null,
  mode = "add",
}: HolidayFormProps) {
  const t = useTranslations() as TFunction;
  const formRef = useRef<HTMLFormElement | null>(null);
  const { getFormFields } = useFormFields({ formType: PAGES.ADDHOLIDAY, t });
  const { showToast } = useToastMessage();
  const { mutate: mutateHolidaysList } = useAttendanceExemptions();

  const initialState: ActionState<THoliday> = {
    error: "",
    success: "",
    issues: [],
    data: null,
  };

  // Create bound action for edit mode
  const boundUpdateAction =
    mode === "edit" && holiday
      ? updateHoliday.bind(null, String(holiday.id))
      : addHoliday;

  const [state, submitAction, isPending] = useActionState(
    boundUpdateAction,
    initialState,
  );

  const defaultFormValues = useMemo(
    () => ({
      name: holiday?.attributes?.name || "",
      start_date: holiday?.attributes?.start_date
        ? new Date(holiday.attributes.start_date)
        : new Date(),
      end_date: holiday?.attributes?.end_date
        ? new Date(holiday.attributes.end_date)
        : new Date(),
    }),
    [holiday],
  );

  const form = useForm<HolidaySchemaType>({
    resolver: zodResolver(holidaySchema(t)),
    defaultValues: defaultFormValues,
    mode: "all",
  });

  useEffect(() => {
    if (state?.success) {
      showToast("success", state.success);
      if (mode === "add") {
        form.reset(defaultFormValues);
      }
      mutateHolidaysList();
      onSuccess();
    }
  }, [state, form, defaultFormValues, mode, mutateHolidaysList]);

  useEffect(() => {
    if (state?.error) {
      showToast("error", state.issues?.[0] || state.error);
    }
  }, [state]);

  const handleSubmit = (data: HolidaySchemaType) => {
    const formData = new FormData();
    const toDateOnly = (date: Date) => format(date, "yyyy-MM-dd");

    formData.append("exemption_type", "holiday");

    formData.append("name", data.name);

    if (data.start_date) {
      formData.append("start_date", toDateOnly(data.start_date));
    }

    if (data.end_date) {
      formData.append("end_date", toDateOnly(data.end_date));
    }

    startTransition(() => {
      submitAction(formData);
    });
  };

  return (
    <div className="bg-white rounded-lg max-w-2xl mx-auto w-[99.5%]">
      <Form {...form}>
        <form
          ref={formRef}
          action={submitAction}
          onSubmit={form.handleSubmit(handleSubmit)}
          className="space-y-6"
        >
          {/* Other form fields */}
          {(getFormFields() as TinputField<HolidaySchemaType>[]).map(
            (fieldConfig) => (
              <FormFieldRenderer
                key={fieldConfig.name?.toString()}
                fieldConfig={fieldConfig}
                form={form}
                isPending={isPending}
              />
            ),
          )}

          <div className="sticky border-t border-t-slate-200 pt-2.5 bottom-0 left-0 w-[100%] rounded-t-[18px] bg-white flex flex-row items-center justify-between gap-4 sm:gap-6">
            <Button
              disabled={isPending || !form.formState.isValid}
              type="submit"
              className="px-4 py-2 w-full h-12 sm:max-w-[244px] rounded-lg"
            >
              {isPending ? (
                <Loader className="animate-spin" />
              ) : mode === "edit" ? (
                t("settings.holidays.form.update-button")
              ) : (
                t("settings.holidays.form.add-button")
              )}
            </Button>
            <Button
              variant="outline"
              type="button"
              className="w-full h-12 sm:max-w-[244px] rounded-lg"
              onClick={() => {
                form.reset(defaultFormValues);
                onCancel();
              }}
            >
              {t("settings.holidays.form.cancel-button")}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
