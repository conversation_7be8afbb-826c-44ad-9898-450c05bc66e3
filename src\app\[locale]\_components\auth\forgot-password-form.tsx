"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { PAGES } from "@/enums";
import useFormFields from "@/hooks/useFormFields";
import { onSubmitForgotPassword } from "@/server/actions/auth";
import { ForgotPasswordSchema, ForgotPasswordSchemaType } from "@/schemas/auth";
import { ActionState, TFunction, TinputField } from "@/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader } from "lucide-react";
import React, {
  startTransition,
  useActionState,
  useEffect,
  useRef,
} from "react";
import { useForm } from "react-hook-form";
import { useLocale, useTranslations } from "next-intl";
import FormFieldRenderer from "@/components/auth/fields/form-field-renderer";
import ErrorMessage from "@/components/errorMessage";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { Locale } from "@/i18n/routing";

const ForgotPassForm = () => {
  const t = useTranslations() as TFunction;
  const { getFormFields } = useFormFields({ formType: PAGES.FORGOTPASS, t });
  const formRef = useRef<HTMLFormElement | null>(null);
  const { toast } = useToast();
  const router = useRouter();
  const locale: Locale = useLocale() as Locale;
  const initialState: ActionState<null> = {
    error: "",
    success: "",
    issues: [],
  };

  const [state, submitAction, isPending] = useActionState(
    onSubmitForgotPassword,
    initialState,
  );

  const form = useForm<ForgotPasswordSchemaType>({
    resolver: zodResolver(ForgotPasswordSchema(t)),
    defaultValues: {
      email: "",
    },
    mode: "all",
  });
  const isSubmitSuccessful = form.formState.isSubmitSuccessful;
  useEffect(() => {
    if (isSubmitSuccessful) {
      form.reset();
    }
  }, [form, form.reset, isSubmitSuccessful]);

  useEffect(() => {
    if (state.success) {
      toast({ className: "toast-success", description: state.success });
      router.push(`/${locale}/auth/verify-code`);
    }
  }, [router, state, locale, toast]);

  return (
    <Form {...form}>
      <form
        ref={formRef}
        action={submitAction}
        onSubmit={(e) => {
          e.preventDefault();
          form.handleSubmit(() => {
            startTransition(async () => {
              if (formRef.current) {
                await submitAction(new FormData(formRef.current!));
              }
            });
          })(e);
        }}
        className="space-y-3 mt-10"
      >
        {(getFormFields() as TinputField<ForgotPasswordSchemaType>[])?.map(
          (fieldConfig) => {
            return (
              <FormFieldRenderer
                key={fieldConfig.name}
                fieldConfig={fieldConfig}
                form={form}
                isPending={isPending}
              />
            );
          },
        )}
        {/* handle form errors */}
        {state?.error && (!state?.issues || state.issues.length === 0) && (
          <ErrorMessage message={state.error} />
        )}
        {Array.isArray(state?.issues) && state?.issues?.length > 0 && (
          <ErrorMessage message={state.issues[0]} />
        )}
        <Button
          disabled={isPending}
          type="submit"
          className="w-full h-12 max-h-12 !mt-10"
        >
          {!isPending ? (
            t("auth.forgotPass.buttons.resetPass")
          ) : (
            <Loader className="animate-spin text-white" />
          )}
        </Button>
      </form>
    </Form>
  );
};

export default ForgotPassForm;
