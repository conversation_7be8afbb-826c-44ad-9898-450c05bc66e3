import DeviceUsersTab from "@/app/[locale]/_modules/people/_components/devices/details/device-users-tab";

export default async function DeviceUsersPage({
  searchParams,
  params,
}: {
  searchParams: Promise<{ page: string; limit: string }>;
  params: Promise<{ id: string }>;
}) {
  const { id: deviceId } = await params;
  const allParams = await searchParams;

  return <DeviceUsersTab searchParams={allParams} params={deviceId} />;
}
