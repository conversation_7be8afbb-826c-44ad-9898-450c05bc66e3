"use client";

import { useState } from "react";

export const useCopyToClipboard = () => {
  const [copiedText, setCopiedText] = useState<string | null>(null);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(text);

      // Reset copiedText after 2 seconds
      setTimeout(() => setCopiedText(null), 2000);

      return true;
    } catch (err) {
      console.log("Failed to copy:", err);
      return false;
    }
  };

  return { copiedText, copyToClipboard };
};
