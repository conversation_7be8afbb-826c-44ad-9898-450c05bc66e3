import { z } from "zod";
import { TFunction } from "@/types";
import { validatePhoneNumber } from "@/lib/utils";
import {
  timeValidation,
  emailValidation,
  phoneValidation,
  optionalStringValidation,
  booleanValidation,
  integerValidation,
  positiveNumberValidation,
  decimalValidation,
  stringArrayValidation,
} from "./common-validations";

export const createSettingsValidationSchema = (
  settings: Array<{
    attributes: {
      key: string;
      value: unknown;
      namespace: string;
      setting_type: string;
    };
  }>,
  t: TFunction,
) => {
  const schemaFields: Record<string, z.ZodTypeAny> = {};

  settings.forEach((setting) => {
    const { key, setting_type } = setting.attributes;

    switch (setting_type) {
      case "time":
        schemaFields[key] = timeValidation(t);
        break;
      case "email":
        schemaFields[key] = emailValidation(t);
        break;
      case "phone":
        schemaFields[key] = phoneValidation(t).refine(
          (value) => validatePhoneNumber(value),
          {
            message:
              t("common.form.mobile.error.notValid") ||
              "Please enter a valid phone number",
          },
        );
        break;
      case "integer":
        schemaFields[key] = integerValidation(t);
        break;
      case "float":
      case "number":
        schemaFields[key] = positiveNumberValidation(t);
        break;
      case "decimal":
        schemaFields[key] = decimalValidation(t);
        break;
      case "boolean":
        schemaFields[key] = booleanValidation();
        break;
      case "array":
        schemaFields[key] = stringArrayValidation();
        break;
      case "string":
      default:
        schemaFields[key] = optionalStringValidation();
        break;
    }
  });

  return z.object(schemaFields);
};
