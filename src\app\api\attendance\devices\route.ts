import { NextRequest, NextResponse } from "next/server";
import { peopleService } from "@/services/api/people";
import { createErrorResponse } from "@/utils/api-error-extractor";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Parse parameters - support both formats for backward compatibility
    const page = parseInt(
      searchParams.get("page") || searchParams.get("page[number]") || "1",
      10,
    );
    const limit = parseInt(
      searchParams.get("limit") || searchParams.get("page[size]") || "5",
      10,
    );
    const sort = searchParams.get("sort") || "created_at";
    const search = searchParams.get("search") || "";

    const filterParams = new URLSearchParams();
    for (const [key, value] of searchParams.entries()) {
      if (key.startsWith("filter[") && !key.startsWith("group[")) {
        filterParams.append(key, value);
      }
    }

    const filters = filterParams.toString();

    const response = await peopleService.getDevices(
      page,
      limit,
      sort,
      search,
      filters,
    );

    return NextResponse.json(response);
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET Devices route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
