import { NextRequest, NextResponse } from "next/server";
import { peopleService } from "@/services/api/people";
import { createErrorResponse } from "@/utils/api-error-extractor";

type ActionType = "preview" | "download";

function isValidAction(action: string | null): action is ActionType {
  return action === "preview" || action === "download";
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: "Missing salary calculation ID" },
        { status: 400 },
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get("action");

    if (!isValidAction(action)) {
      return NextResponse.json(
        {
          error:
            "Invalid or missing action parameter. Use 'preview' or 'download'",
        },
        { status: 400 },
      );
    }

    try {
      if (action === "preview") {
        const response = (await peopleService.getSalarySlip(
          id,
          action,
        )) as Record<string, unknown>;
        return NextResponse.json(response);
      }

      // Handle download
      const response = (await peopleService.getSalarySlip(
        id,
        action,
      )) as Response;
      const fileData = await response.arrayBuffer();
      const contentDisposition = response.headers.get("content-disposition");
      const filename =
        contentDisposition
          ?.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)?.[1]
          ?.replace(/['"]/g, "") || `salary-slip-${id}.pdf`;

      return new NextResponse(fileData, {
        headers: {
          "Content-Type":
            response.headers.get("content-type") || "application/pdf",
          "Content-Disposition": `attachment; filename="${filename}"`,
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      });
    } catch (error) {
      console.error(`Error processing ${action} request:`, error);
      throw error;
    }
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET salary slip route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
