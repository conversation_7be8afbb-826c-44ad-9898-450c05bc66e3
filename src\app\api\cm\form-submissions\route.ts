import { NextRequest, NextResponse } from "next/server";
import { cmAPI } from "@/services/api/cm";
import { createErrorResponse } from "@/utils/api-error-extractor";
import { CreateFormSubmissionData, UpdateFormSubmissionData } from "@/types/cm";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    const caseId = searchParams.get("case_id");
    const formSectionId = searchParams.get("form_section_id");

    const params: {
      case_id?: string;
      form_section_id?: string;
    } = {};

    if (caseId) {
      params.case_id = caseId;
    }

    if (formSectionId) {
      params.form_section_id = formSectionId;
    }

    const response = await cmAPI.getFormSubmissions(params);
    return NextResponse.json(response);
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET Form Submissions route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body: CreateFormSubmissionData = await request.json();

    // Validate required fields
    if (!body.form_template_id || !body.form_section_id || !body.form_data) {
      return NextResponse.json(
        {
          error: "Missing required fields",
          details:
            "form_template_id, form_section_id, and form_data are required",
        },
        { status: 400 },
      );
    }

    // Log the submission data for debugging
    console.log("🚀 ~ POST form submission ~ body:", body);

    const response = await cmAPI.createFormSubmission(body);
    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "POST Form Submissions route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
