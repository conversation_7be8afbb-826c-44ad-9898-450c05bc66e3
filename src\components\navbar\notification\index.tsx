"use client";

import { <PERSON>, <PERSON> } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

import { useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import { LANGUAGES } from "@/constants/enum";
import NotificationsList from "./notification-list";
import { Notification } from "@/types";
import { Locale } from "@/i18n/routing";

export default function Notifications() {
  const t = useTranslations();
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: 1,
      title: "اقترب موعد جلستك",
      description:
        "تذكير بموعد جلسة العلاج المقررة غداً في تمام الساعة 10:00 صباحاً. يرجى الحضور قبل 15 دقيقة من الموعد المحدد.",
      date: "05:00 | 25-01-2025",
      isRead: false,
    },
    {
      id: 2,
      title: "تم الغاء موعد جلستك",
      description: "تم إلغاء موعد جلسة العلاج النفسي بسبب ظروف طارئة. سيتم التواصل معك لتحديد موعد بديل قريباً.",
      date: "05:00 | 25-01-2025",
      isRead: true,
    },
    {
      id: 3,
      title: "تم استلام التقرير من الطبيب",
      description: "وصل التقرير الطبي الشامل من الطبيب المختص. يمكنك مراجعة التفاصيل والتوصيات في ملفك الشخصي.",
      date: "05:00 | 25-01-2025",
      isRead: true,
    },
    {
      id: 4,
      title: "تم استلام التقرير من الطبيب",
      description: "وصل التقرير الطبي الشامل من الطبيب المختص. يمكنك مراجعة التفاصيل والتوصيات في ملفك الشخصي.",
      date: "05:00 | 25-01-2025",
      isRead: true,
    },
    {
      id: 5,
      title: "تم استلام التقرير من الطبيب",
      description: "تقرير طبي إضافي تم استلامه مع توصيات جديدة للعلاج. يرجى مراجعة المحتوى والتنسيق مع الفريق الطبي.",
      date: "05:00 | 25-01-2025",
      isRead: true,
    },
  ]);

  const [isOpen, setIsOpen] = useState(false);

  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;

  // any unread notifications?
  const hasUnread = notifications.some((n) => !n.isRead);
  // const notificationSoundEnabled = true;
  // useEffect(() => {
  //   // Play sound only when there is a new unread notification and sound is enabled
  //   if (hasUnread && notificationSoundEnabled) {
  //     const audio = new Audio("/sound/notification-bubble.mp3");
  //     audio.play().catch((error) => {
  //       console.error("Failed to play notification sound:", error);
  //     });
  //   }
  // }, [hasUnread, notifications, notificationSoundEnabled]);

  // Mark all as read
  const markAllAsRead = () => {
    setNotifications((prev) =>
      prev.map((notification) => ({ ...notification, isRead: true })),
    );
  };

  // Mark a single notification as read
  const markAsRead = (id: number) => {
    setNotifications((prev) =>
      prev.map((n) => (n.id === id ? { ...n, isRead: true } : n)),
    );
  };

  return (
    <>
      {/* Dark overlay when open */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/30 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}

      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className={`rounded-full relative ${
              isOpen ? "z-50" : "z-10"
            } w-[38px] h-[38px] bg-background-v2 shrink-0`}
          >
            <div className="relative">
              <Bell className="h-4 w-4 text-[#242E2C]" />
              {/* Show red dot only if there are unread notifications */}
              {hasUnread && (
                <span className="absolute -top-[2px] -end-[1px] h-2 w-2 rounded-full bg-red-500" />
              )}
            </div>
          </Button>
        </PopoverTrigger>

        <PopoverContent
          className="w-[380px] p-0 z-50 rounded-2xl"
          align="end"
          sideOffset={8}
          lang={locale}
          dir={isAr ? "rtl" : "ltr"}
        >
          <Card className="border-0 shadow-xl w-full max-w-[376px]">
            {/* Header */}
            <div className="p-4 border-b flex items-center justify-between">
              <h2 className="text-xl font-semibold text-[#24282E]">
                {t("common.navbar.notification.title")}
              </h2>
              <Button
                variant="ghost"
                className="text-[#727A90] w-4 h-4 cursor-pointer p-0 focus-visible:ring-0"
                onClick={() => setIsOpen(false)}
              >
                <X className="!h-6 !w-6" />
              </Button>
            </div>

            <NotificationsList
              notifications={notifications}
              markAllAsRead={markAllAsRead}
              markAsRead={markAsRead}
              onSeeMore={() => {
                setIsOpen(false);
              }}
            />
          </Card>
        </PopoverContent>
      </Popover>
    </>
  );
}
