"use client";

import React, { useState } from "react";
import { FormTemplate } from "@/types/cm";
import { useFormSections } from "@/app/[locale]/_modules/cm/hooks/useFormSections";
import { CheckCircle, Circle, Clock, ChevronRight, Check } from "lucide-react";
import clsx from "clsx";

type StepTimelineSidebarProps = {
  caseId?: string;
  templates: FormTemplate[];
  currentTemplateIndex: number;
  currentSectionIndex: number;
  completedTemplates: Set<number>;
  completedSections: Set<string>;
  onTemplateClick?: (templateIndex: number) => void;
  onSectionClick?: (sectionIndex: number) => void;
  isReadOnly?: boolean;
};

type StepStatus = "completed" | "current" | "pending" | "locked";

const getTemplateStatus = (
  index: number,
  currentIndex: number,
  completedTemplates: Set<number>,
): StepStatus => {
  if (completedTemplates.has(index)) return "completed";
  if (index === currentIndex) return "current";
  if (index < currentIndex) return "completed";
  return "locked";
};

const getSectionStatus = (
  sectionId: string,
  sectionIndex: number,
  currentSectionIndex: number,
  currentTemplateIndex: number,
  templateIndex: number,
  completedSections: Set<string>,
): StepStatus => {
  if (completedSections.has(sectionId)) return "completed";

  // If this is the current template
  if (templateIndex === currentTemplateIndex) {
    if (sectionIndex === currentSectionIndex) return "current";
    if (sectionIndex < currentSectionIndex) return "completed";
    return "locked";
  }

  // If this is a previous template, all sections should be accessible
  if (templateIndex < currentTemplateIndex) return "completed";

  // Future templates are locked
  return "locked";
};

export const StepTimelineSidebar = ({
  caseId,
  templates,
  currentTemplateIndex,
  currentSectionIndex,
  completedTemplates,
  completedSections,
  onTemplateClick,
  onSectionClick,
  isReadOnly = false,
}: StepTimelineSidebarProps) => {
  // Get current template for sections loading
  const currentTemplate = templates[currentTemplateIndex];

  // Fetch sections for current template to show in sidebar
  const { sections: currentTemplateSections, isLoading: sectionsLoading } =
    useFormSections({
      formTemplateId: currentTemplate?.id,
      sort: "display_order",
    });

  // Calculate completed sections for current template only
  const currentTemplateCompletedSections = currentTemplateSections.filter(
    (section) => completedSections.has(section.id),
  ).length;

  // State for expanded steps (using step-timeline logic)
  const [expandedSteps, setExpandedSteps] = useState<Record<number, boolean>>(
    templates.reduce(
      (acc, template, index) => {
        acc[index] = index === 0; // Only first template expanded by default
        return acc;
      },
      {} as Record<number, boolean>,
    ),
  );

  const toggleStep = (templateIndex: number) => {
    // Close all steps first
    const newExpandedState = Object.keys(expandedSteps).reduce(
      (acc, key) => {
        acc[parseInt(key)] = false;
        return acc;
      },
      {} as Record<number, boolean>,
    );

    // Then toggle the clicked step
    newExpandedState[templateIndex] = !expandedSteps[templateIndex];
    setExpandedSteps(newExpandedState);
  };

  const handleTemplateClick = (templateIndex: number) => {
    if (isReadOnly || !onTemplateClick) return;

    const templateStatus = getTemplateStatus(
      templateIndex,
      currentTemplateIndex,
      completedTemplates,
    );

    // Only allow clicking on current or completed templates
    if (templateStatus === "current" || templateStatus === "completed") {
      onTemplateClick(templateIndex);
    }
  };

  const handleSectionClick = (sectionIndex: number) => {
    if (isReadOnly || !onSectionClick) return;
    onSectionClick(sectionIndex);
  };

  return (
    <div className="flex flex-col items-center">
      {/* Timeline Container - Adapted from step-timeline */}
      <div className="flex-1 overflow-y-auto">
        <div className="relative">
          {templates.map((template, templateIndex) => {
            const templateStatus = getTemplateStatus(
              templateIndex,
              currentTemplateIndex,
              completedTemplates,
            );
            const isExpanded = expandedSteps[templateIndex];
            const isLast = templateIndex === templates.length - 1;

            return (
              <div
                key={template.id}
                className={clsx("relative", !isLast && "pb-6")}
              >
                {/* Vertical line between circles */}
                {!isLast && (
                  <div
                    className={clsx(
                      "absolute start-2.5 top-7 h-[calc(100%-30px)] transition-colors duration-300 w-[2px] rounded-full z-0",
                      isExpanded ? "bg-main-2" : "bg-[#D4E5CD]",
                    )}
                  />
                )}

                {/* Template header section */}
                <div
                  className="flex items-center cursor-pointer mb-6 relative z-10"
                  onClick={() => toggleStep(templateIndex)}
                >
                  {/* Template number circle */}
                  <div
                    className={clsx(
                      "absolute start-0 w-6 h-6 rounded-full flex items-center justify-center transition-colors duration-300",
                      isExpanded ? "bg-primary" : "bg-[#D4E5CD]",
                    )}
                  >
                    <span
                      className={clsx(
                        "font-medium text-sm transition-colors duration-300",
                        isExpanded ? "text-main-2" : "text-[#86B177]",
                      )}
                    >
                      {templateIndex + 1}
                    </span>
                  </div>

                  <div className="flex justify-between items-center w-full">
                    <h3
                      className={clsx(
                        "ps-10 font-medium text-base transition-colors duration-300",
                        isExpanded ? "text-main-2" : "text-[#2d3c4d]",
                      )}
                    >
                      {template.attributes.title || template.attributes.name}
                    </h3>

                    {/* Status indicator */}
                    {templateStatus === "current" && (
                      <span className="ms-4 bg-blue-50 text-blue-700 text-xs rounded-full px-3 py-1">
                        جاري
                      </span>
                    )}
                  </div>
                </div>

                {/* Template Sections - Show when expanded */}
                <div
                  className={clsx(
                    "ps-10 flex flex-col justify-center gap-4 overflow-hidden transition-all duration-300 ease-in-out",
                    isExpanded ? "max-h-96 opacity-100" : "max-h-0 opacity-0",
                  )}
                >
                  {/* {!isLastSection && (
                    <div
                      className={clsx(
                        "absolute start-2.5 top-7 h-[calc(100%-30px)] transition-colors duration-300 w-[2px] rounded-full z-0",
                        isExpanded ? "bg-main-2" : "bg-[#D4E5CD]",
                      )}
                    />
                  )} */}
                  {sectionsLoading ? (
                    <div className="text-xs text-gray-500 p-2">
                      جاري تحميل الأقسام...
                    </div>
                  ) : currentTemplateSections.length > 0 ? (
                    currentTemplateSections.map((section, sectionIndex) => {
                      const sectionStatus = getSectionStatus(
                        section.id,
                        sectionIndex,
                        currentSectionIndex,
                        currentTemplateIndex,
                        templateIndex,
                        completedSections,
                      );

                      return (
                        <div
                          key={section.id}
                          className="flex justify-between items-center"
                        >
                          <div className="flex items-center justify-between w-full">
                            <div className="flex items-center">
                              <div
                                className={clsx(
                                  "w-5 h-5 rounded-full border-2 me-4 transition-colors duration-200 flex items-center justify-center",
                                  sectionStatus === "current"
                                    ? "border-primary w-[27.5px] h-[27.5px]"
                                    : sectionStatus === "completed"
                                      ? "border-primary bg-primary"
                                      : "border-gray-300",
                                )}
                              >
                                {sectionStatus === "current" && (
                                  <div className="bg-primary rounded-full w-5 h-5 flex items-center justify-center">
                                    <div className="bg-white rounded-full w-2 h-2"></div>
                                  </div>
                                )}

                                {sectionStatus === "completed" && (
                                  <Check
                                    size={8}
                                    className="h-4 w-4 text-secondary font-bold !stroke-2"
                                  />
                                )}
                              </div>
                              <span
                                className={clsx(
                                  "transition-colors duration-200 font-normal text-sm cursor-pointer",
                                  sectionStatus === "current"
                                    ? "text-[#2d3c4d]"
                                    : sectionStatus === "completed"
                                      ? "text-green-700"
                                      : "text-gray-700",
                                )}
                                onClick={() => handleSectionClick(sectionIndex)}
                              >
                                {section.attributes.name}
                              </span>
                            </div>

                            <span className="text-xs text-gray-500">
                              {section.attributes.fields_count} حقل
                            </span>
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    <div className="text-xs text-gray-500 p-2">
                      لا توجد أقسام متاحة
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
