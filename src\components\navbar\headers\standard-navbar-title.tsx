"use client";

import React from "react";
import { SidebarData } from "@/types";
import { Button } from "@/components/ui/button";
import { LeftCircleArrow, RightArrow } from "../../../../public/images/icons";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";

type StandardNavbarTitleProps = {
  title: string;
  data?: SidebarData;
  locale?: string;
};

const StandardNavbarTitle: React.FC<StandardNavbarTitleProps> = ({ title }) => {
  const router = useRouter();
  const handleBack = () => {
    router.back();
  };
  const t = useTranslations();
  return (
    <div className="flex items-center gap-4 text-sm font-semibold leading-5 tracking-[0.5%]">
      <Button
        variant="ghost"
        title="go back to employees page"
        onClick={handleBack}
        className="w-5 h-5 p-0 outline-none"
      >
        <RightArrow className="!w-7 !h-6 ltr:rotate-180" />
      </Button>
      <LeftCircleArrow className="!w-7 !h-6 ltr:rotate-180" />
      <div className="flex items-center gap-2 text-secondary ms-2 lg:ms-6">
        <h1>{t("people.devices-page.title")}</h1>
        <span className="text-gray-300">/</span>
        <h2 className="text-gray-400">{title}</h2>
      </div>
    </div>
  );
};

export default StandardNavbarTitle;
