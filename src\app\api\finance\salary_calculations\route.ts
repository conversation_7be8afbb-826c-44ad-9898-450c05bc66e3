import { NextRequest, NextResponse } from "next/server";
import { peopleService } from "@/services/api/people";
import { createErrorResponse } from "@/utils/api-error-extractor";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    const search = searchParams.get("search") || "";

    // Extract all filter parameters
    const filterParts: string[] = [];
    for (const [key, value] of searchParams.entries()) {
      if (key.startsWith("filter[") && value) {
        filterParts.push(`${key}=${encodeURIComponent(value)}`);
      }
    }
    const filters = filterParts.join('&');

    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "20", 10);
    const sort = searchParams.get("sort") || "-period_start_date";

    try {
      const response = await peopleService.getSalaryCalculations(
        page,
        limit,
        sort,
        search,
        filters,
      );

      // Return the response directly without wrapping it
      return NextResponse.json(response);
    } catch (error) {
      // Re-throw for the outer catch block to handle
      throw error;
    }
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET salary calculations route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
