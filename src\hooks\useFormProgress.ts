import { useState, useEffect } from "react";

type FormProgress = {
  completedSections: Set<number>;
  currentSectionIndex: number;
  lastSavedAt: string | null;
  formData: Record<string, any>;
};

type UseFormProgressProps = {
  caseId?: string;
  templateId?: string;
  mode: "new" | "edit";
};

export const useFormProgress = ({
  caseId,
  templateId,
  mode,
}: UseFormProgressProps) => {
  const [progress, setProgress] = useState<FormProgress>({
    completedSections: new Set(),
    currentSectionIndex: 0,
    lastSavedAt: null,
    formData: {},
  });

  // Generate storage key
  const getStorageKey = () => {
    if (mode === "edit" && caseId) {
      return `form_progress_${caseId}_${templateId}`;
    }
    return `form_progress_new_${templateId}`;
  };

  // Load progress from localStorage
  useEffect(() => {
    const storageKey = getStorageKey();
    const savedProgress = localStorage.getItem(storageKey);

    if (savedProgress) {
      try {
        const parsed = JSON.parse(savedProgress);
        setProgress({
          ...parsed,
          completedSections: new Set(parsed.completedSections || []),
        });
      } catch (error) {
        console.error("Failed to parse saved progress:", error);
      }
    }
  }, [caseId, templateId, mode]);

  // Save progress to localStorage
  const saveProgress = (newProgress: Partial<FormProgress>) => {
    const updatedProgress = {
      ...progress,
      ...newProgress,
      lastSavedAt: new Date().toISOString(),
    };

    // Convert Set to Array for JSON serialization
    const serializable = {
      ...updatedProgress,
      completedSections: Array.from(updatedProgress.completedSections),
    };

    const storageKey = getStorageKey();
    localStorage.setItem(storageKey, JSON.stringify(serializable));
    setProgress(updatedProgress);
  };

  // Mark section as completed
  const markSectionCompleted = (sectionIndex: number) => {
    const newCompletedSections = new Set(progress.completedSections);
    newCompletedSections.add(sectionIndex);

    saveProgress({
      completedSections: newCompletedSections,
    });
  };

  // Update current section
  const setCurrentSection = (sectionIndex: number) => {
    saveProgress({
      currentSectionIndex: sectionIndex,
    });
  };

  // Save form data
  const saveFormData = (sectionId: string, data: Record<string, any>) => {
    const newFormData = {
      ...progress.formData,
      [sectionId]: data,
    };

    saveProgress({
      formData: newFormData,
    });
  };

  // Clear progress (on successful submission)
  const clearProgress = () => {
    const storageKey = getStorageKey();
    localStorage.removeItem(storageKey);
    setProgress({
      completedSections: new Set(),
      currentSectionIndex: 0,
      lastSavedAt: null,
      formData: {},
    });
  };

  // Get form data for a specific section
  const getSectionData = (sectionId: string) => {
    return progress.formData[sectionId] || {};
  };

  return {
    progress,
    markSectionCompleted,
    setCurrentSection,
    saveFormData,
    getSectionData,
    clearProgress,
    isCompleted: (sectionIndex: number) =>
      progress.completedSections.has(sectionIndex),
    completionPercentage: (totalSections: number) =>
      Math.round((progress.completedSections.size / totalSections) * 100),
  };
};
