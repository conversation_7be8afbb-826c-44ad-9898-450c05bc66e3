export type TDeviceAttributes = {
  name: string;
  adapter_type: string;
  ip_address: string;
  port: number;
  location: string;
  status: "active" | "inactive" | "maintenance" | "error";
  last_seen_at: string | null;
  connection_config: Record<string, unknown>;
  sync_config: Record<string, unknown>;
  capabilities: Record<string, unknown>;
  created_at: string;
  updated_at: string;
  health_score: number;
  last_sync: {
    id: number;
    status: string;
    started_at: string;
    completed_at: string;
    duration: string;
    success_rate: number;
    records_processed: number;
    records_imported: number;
  } | null;
  last_successful_sync: {
    id: number;
    completed_at: string;
    records_imported: number;
  } | null;
  connection_status: "online" | "offline" | "unknown";
  sync_statistics: {
    total_syncs: number;
    successful_syncs: number;
    failed_syncs: number;
    success_rate: number;
    average_sync_time: number;
    last_7_days_syncs: number;
  };
  device_capabilities: {
    supports_real_time: boolean;
    supports_user_management: boolean;
    supports_clear_data: boolean;
  };
  recent_events_count: number;
};

export type TDeviceRelationships = {
  sync_logs: {
    data: unknown[];
  };
  events: {
    data: unknown[];
  };
};

export type TDevice = {
  id: string;
  type: string;
  attributes: TDeviceAttributes;
  relationships: TDeviceRelationships;
};

export type TDeviceResponse = {
  data: TDevice[];
  meta: {
    pagination: {
      count: number;
      page: number;
      limit: number;
      from: number;
      to: number;
    };
  };
};

export type DeviceFilters = {
  search?: string;
  status?: "active" | "inactive" | "maintenance" | "error";
  adapter_type?: string;
  location?: string;
};
