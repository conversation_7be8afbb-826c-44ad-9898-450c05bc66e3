import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();

    // Get all relevant tokens
    const tokens = {
      people_session_token: cookieStore.get("people_session_token")?.value,
      core_session_token: cookieStore.get("core_session_token")?.value,
      main_token: cookieStore.get("main_token")?.value,
      procure_session_token: cookieStore.get("procure_session_token")?.value,
      currSystem: cookieStore.get("currSystem")?.value,
      locale: cookieStore.get("NEXT_LOCALE")?.value,
    };

    // Parse JWT tokens to check expiration
    const parseJWT = (token: string | undefined) => {
      if (!token) return null;
      try {
        const payload = JSON.parse(atob(token.split(".")[1]));
        return {
          sub: payload.sub,
          scope: payload.scope,
          user_type: payload.user_type,
          exp: payload.exp,
          iat: payload.iat,
          expired: payload.exp < Date.now() / 1000,
          expiresAt: new Date(payload.exp * 1000).toISOString(),
          issuedAt: new Date(payload.iat * 1000).toISOString(),
        };
      } catch (e) {
        return { error: "Invalid JWT format" };
      }
    };

    const tokenInfo = {
      people_session_token: {
        present: !!tokens.people_session_token,
        length: tokens.people_session_token?.length || 0,
        prefix:
          tokens.people_session_token?.substring(0, 20) + "..." || "No token",
        parsed: parseJWT(tokens.people_session_token),
      },
      core_session_token: {
        present: !!tokens.core_session_token,
        length: tokens.core_session_token?.length || 0,
        prefix:
          tokens.core_session_token?.substring(0, 20) + "..." || "No token",
        parsed: parseJWT(tokens.core_session_token),
      },
      main_token: {
        present: !!tokens.main_token,
        length: tokens.main_token?.length || 0,
        prefix: tokens.main_token?.substring(0, 20) + "..." || "No token",
        parsed: parseJWT(tokens.main_token),
      },
      currSystem: tokens.currSystem,
      locale: tokens.locale,
    };

    // Test direct backend call
    const peopleToken = tokens.people_session_token;
    let backendTest = null;

    if (peopleToken) {
      try {
        console.log("🧪 Testing direct backend call...");
        const testResponse = await fetch(
          "http://people.athar.test/api/attendance/devices/1/users?page[number]=1&page[size]=5",
          {
            headers: {
              Authorization: `Bearer ${peopleToken}`,
              "Content-Type": "application/json",
              "Accept-Language": tokens.locale || "ar",
            },
          },
        );

        console.log("🧪 Backend test response status:", testResponse.status);

        if (testResponse.ok) {
          const data = await testResponse.json();
          backendTest = {
            status: testResponse.status,
            success: true,
            dataKeys: Object.keys(data),
            userCount: data.data?.length || 0,
            firstUserName: data.data?.[0]?.attributes?.name || "No users",
          };
        } else {
          const errorData = await testResponse.text();
          backendTest = {
            status: testResponse.status,
            success: false,
            error: errorData,
          };
        }
      } catch (error) {
        backendTest = {
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
        };
      }
    }

    return NextResponse.json({
      timestamp: new Date().toISOString(),
      tokens: tokenInfo,
      backendTest,
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        BASE_API_URL: process.env.BASE_API_URL,
      },
    });
  } catch (error) {
    console.error("Debug tokens error:", error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}
