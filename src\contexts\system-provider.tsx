// contexts/SystemContext.tsx
"use client";

import React, { createContext, useContext, useEffect, useMemo } from "react";
import { TSystems } from "@/types";

// System display names mapping
const SYSTEM_DISPLAY_NAMES: Record<TSystems, string> = {
  core: "",
  people: "AtharHR",
  procure: "AtharProcure",
  cm: "AtharCaseManager",
};

interface SystemContextType {
  currSystem: TSystems;
  systemDisplayName: string;
}

const SystemContext = createContext<SystemContextType | undefined>(undefined);

export const SystemProvider = ({
  children,
  currSystem,
}: {
  children: React.ReactNode;
  currSystem: string;
}) => {
  // Store currSystem in cookies on mount
  useEffect(() => {
    document.cookie = `currSystem=${currSystem}; path=/; secure; samesite=strict`;
  }, [currSystem]);

  // Get the display name for the current system
  const systemDisplayName = useMemo(() => {
    return (
      SYSTEM_DISPLAY_NAMES[currSystem as TSystems] ||
      `Athar ${currSystem.toUpperCase()}`
    );
  }, [currSystem]);

  return (
    <SystemContext.Provider
      value={{
        currSystem: currSystem as TSystems,
        systemDisplayName,
      }}
    >
      {children}
    </SystemContext.Provider>
  );
};

export const useSystem = (): SystemContextType => {
  const context = useContext(SystemContext);
  if (context === undefined) {
    throw new Error("useSystem must be used within a SystemProvider");
  }
  return context;
};
