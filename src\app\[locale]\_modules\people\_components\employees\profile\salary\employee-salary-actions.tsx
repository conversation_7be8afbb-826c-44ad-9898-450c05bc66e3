"use client";

import { Row } from "@tanstack/react-table";
import { useLocale, useTranslations } from "next-intl";
import { Locale } from "@/i18n/routing";
import { mapSalaryStatusToCanonical } from "@/constants/translations-mapping";
import {
  SalaryCalculation,
  SalaryCalculationDetail,
} from "../../../../type/employees-salaries";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  MoreHorizontal,
  Eye,
  Download,
  RefreshCw,
  CreditCard,
} from "lucide-react";
import { LANGUAGES, SALARY_STATUS } from "@/constants/enum";
import { PermissionEnum } from "@/enums/Permission";
import { usePermission } from "@/contexts/PermissionContext";
import { downloadFile } from "@/lib/downloadFile";
import { useToastMessage } from "@/hooks/use-toast-message";
import { useState } from "react";
import FilePreview from "@/components/file-preview";
import { TIncludedEmployee } from "../../../../type/employee-leaves";
import { ApprovalRequestData } from "../../../../type/approval-request";
import { useApprovalPermissions } from "../../../../hooks/useApprovalPermissions";
import ApprovalWorkflowModal from "../../../approval-workflow-modal";

type EmployeeSalaryActionsProps<TData> = {
  row: Row<TData>;
  onApproveSalary?: (salaryId: string) => void;
  onRejectSalary?: (salaryId: string) => void;
  onSubmitSalary?: (salaryId: string) => void;
  onPaySalary?: (salaryId: string) => void;
  onRegenerateSlip?: (salaryId: string) => void;
  isApproving?: boolean;
  isRejecting?: boolean;
  isSubmitting?: boolean;
  includedData?: (
    | TIncludedEmployee
    | ApprovalRequestData
    | SalaryCalculationDetail
  )[];
  isPaying?: boolean;
  isRegenerating?: boolean;
};

export const EmployeeSalaryActions = <TData extends SalaryCalculation>({
  row,
  onApproveSalary,
  onRejectSalary,
  onSubmitSalary,
  onPaySalary,
  onRegenerateSlip,
  isApproving,
  isRejecting,
  isSubmitting,
  includedData,
  isPaying,
  isRegenerating,
}: EmployeeSalaryActionsProps<TData>) => {
  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const t = useTranslations();
  const status = row.original.attributes.status;
  const canonicalStatus = mapSalaryStatusToCanonical(status);
  const { hasPermission } = usePermission();
  const { showToast } = useToastMessage();
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [previewData, setPreviewData] = useState<{
    fileName: string;
    fileUrl: string;
    fileType: string;
  } | null>(null);
  const [isDownloading, setIsDownloading] = useState(false);
  const [showWorkflowModal, setShowWorkflowModal] = useState(false);

  // Check if the status is draft (only show submit option)
  const isDraft = canonicalStatus === SALARY_STATUS.DRAFT;

  // For salaries, only SUBMITTED status can be approved/rejected
  const canBeApprovedOrRejected = canonicalStatus === SALARY_STATUS.SUBMITTED;

  // Check if salary slip is available
  const hasSlip = row.original.attributes.has_salary_slip;

  // Get approval request data for permissions
  const approvalRequestId =
    row.original.relationships?.approval_request?.data?.id;

  const approvalRequest =
    approvalRequestId && includedData
      ? (includedData.find(
          (item) =>
            item.type === "approval_request" && item.id === approvalRequestId,
        ) as ApprovalRequestData | undefined)
      : undefined;

  // Use approval permissions hook
  const { canAct, hasActed, userAction } =
    useApprovalPermissions(approvalRequest);

  // Check if salary is in paid state
  const isPaid = canonicalStatus === SALARY_STATUS.PAID;

  // Check if salary is approved (can view slip but may not be paid yet)
  const isApproved = canonicalStatus === SALARY_STATUS.APPROVED;

  // Check permissions
  const canSubmitSalary = hasPermission(
    PermissionEnum.SUBMIT_SALARY_CALCULATION,
  );
  const canApproveSalary = hasPermission(
    PermissionEnum.APPROVE_SALARY_CALCULATION,
  );
  const canReadSalary = hasPermission(PermissionEnum.READ_SALARY_CALCULATION);
  const canReadOwnSalary = hasPermission(
    PermissionEnum.READ_OWN_SALARY_CALCULATION,
  );

  // Determine if user can read this specific salary calculation
  // TODO: Add employee ownership check in Phase 3
  // For now, allow if user has general read OR own read permission
  const canReadThisSalary = canReadSalary || canReadOwnSalary;

  // Use the actual permission check
  const canReadThisSalaryForced = canReadThisSalary;

  // Check if actions are available based on permissions and status
  const hasSubmitAction = isDraft && onSubmitSalary && canSubmitSalary;
  const hasApproveAction =
    canBeApprovedOrRejected &&
    onApproveSalary &&
    canApproveSalary &&
    (canAct || hasActed);
  const hasRejectAction =
    canBeApprovedOrRejected &&
    onRejectSalary &&
    canApproveSalary &&
    (canAct || hasActed);

  const hasPayAction = isApproved && onPaySalary && canApproveSalary; // Use same permission as approve

  // Slip actions are available if slip exists AND user can read (but may be disabled if not paid)
  const hasSlipActions = hasSlip && canReadThisSalaryForced;

  // Regenerate action is available ONLY for PAID calculations with READ permission
  const hasRegenerateAction =
    isPaid && canReadThisSalaryForced && onRegenerateSlip;

  // Check if there are any actions available
  const hasActions =
    hasSubmitAction ||
    hasApproveAction ||
    hasRejectAction ||
    hasPayAction ||
    hasSlipActions ||
    hasRegenerateAction;

  // Handle slip preview
  const handlePreviewSlip = async () => {
    try {
      const response = await fetch(
        `/api/finance/salary_calculations/${row.original.id}/slip?action=preview`,
      );

      if (!response.ok) {
        throw new Error("Failed to get slip preview");
      }

      const data = await response.json();

      setPreviewData({
        fileName: data.fileName || `salary-slip-${row.original.id}.pdf`,
        fileUrl: data.fileUrl,
        fileType: data.fileType || "application/pdf",
      });
      setIsPreviewOpen(true);
    } catch (error) {
      showToast("error", t("people.employees-page.salary.slip.preview-error"));
    }
  };

  // Handle slip download
  const handleDownloadSlip = async () => {
    if (isDownloading) return;

    setIsDownloading(true);
    try {
      const response = await fetch(
        `/api/finance/salary_calculations/${row.original.id}/slip?action=download`,
      );

      if (!response.ok) {
        throw new Error("Failed to download slip");
      }

      const blob = await response.blob();
      const fileName = `salary-slip-${row.original.id}.pdf`;
      const url = URL.createObjectURL(blob);

      downloadFile(url, fileName);
      URL.revokeObjectURL(url);

      showToast(
        "success",
        t("people.employees-page.salary.slip.download-success"),
      );
    } catch (error) {
      showToast("error", t("people.employees-page.salary.slip.download-error"));
    } finally {
      setIsDownloading(false);
    }
  };

  // Handle slip regeneration
  const handleRegenerateSlip = async () => {
    if (isRegenerating || !onRegenerateSlip) return;

    try {
      await onRegenerateSlip(row.original.id);
    } catch (error) {
      // Error handling is done in the hook
      console.error("Regenerate slip error:", error);
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild disabled={!hasActions}>
          <Button
            variant="ghost"
            className="h-8 w-8 p-0 focus-visible:ring-0 focus-visible:ring-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
            dir={locale === LANGUAGES.ARABIC ? "rtl" : "ltr"}
            disabled={isApproving || isRejecting || !hasActions}
          >
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align={isAr ? "start" : "end"}
          className="w-[220px]"
        >
          {/* Slip actions */}
          {hasSlipActions && (
            <>
              <DropdownMenuItem
                onClick={isPaid ? handlePreviewSlip : undefined}
                disabled={!isPaid}
                dir={locale === LANGUAGES.ARABIC ? "rtl" : "ltr"}
                className={`justify-start ${
                  isPaid
                    ? "cursor-pointer font-medium text-blue-600"
                    : "cursor-not-allowed font-medium text-gray-400"
                }`}
              >
                <Eye className="h-4 w-4 ms-2" />
                {t("people.employees-page.salary.slip.preview") ||
                  "Preview Slip"}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={
                  isPaid && !isDownloading ? handleDownloadSlip : undefined
                }
                disabled={!isPaid || isDownloading}
                dir={locale === LANGUAGES.ARABIC ? "rtl" : "ltr"}
                className={`justify-start ${
                  isPaid && !isDownloading
                    ? "cursor-pointer font-medium text-green-600"
                    : "cursor-not-allowed font-medium text-gray-400"
                }`}
              >
                <Download className="h-4 w-4 ms-2" />
                {isDownloading
                  ? t("people.employees-page.salary.slip.downloading") ||
                    "Downloading..."
                  : t("people.employees-page.salary.slip.download") ||
                    "Download Slip"}
              </DropdownMenuItem>
              {(hasSubmitAction ||
                hasApproveAction ||
                hasRejectAction ||
                hasRegenerateAction) && <DropdownMenuSeparator />}
            </>
          )}

          {/* Regenerate action - show for paid salaries with read permission */}
          {hasRegenerateAction && (
            <>
              <DropdownMenuItem
                onClick={!isRegenerating ? handleRegenerateSlip : undefined}
                disabled={isRegenerating}
                dir={locale === LANGUAGES.ARABIC ? "rtl" : "ltr"}
                className={`justify-start ${
                  !isRegenerating
                    ? "cursor-pointer font-medium text-orange-600"
                    : "cursor-not-allowed font-medium text-gray-400"
                }`}
              >
                <RefreshCw className="h-4 w-4 ms-2" />
                {isRegenerating
                  ? t("people.employees-page.salary.slip.regenerating") ||
                    "Regenerating..."
                  : t("people.employees-page.salary.slip.regenerate") ||
                    "Regenerate Slip"}
              </DropdownMenuItem>
              {(hasSubmitAction || hasApproveAction || hasRejectAction) && (
                <DropdownMenuSeparator />
              )}
            </>
          )}

          {/* Submit option for draft status */}
          {hasSubmitAction && (
            <DropdownMenuItem
              onClick={() => onSubmitSalary!(row.original.id)}
              disabled={isSubmitting}
              dir={locale === LANGUAGES.ARABIC ? "rtl" : "ltr"}
              className="cursor-pointer font-medium text-blue-600 justify-start"
            >
              {t("people.employees-salaries-page.table.actions.submit") ||
                "Submit"}
            </DropdownMenuItem>
          )}

          {/* Approve option for submitted status */}
          {hasApproveAction && (
            <DropdownMenuItem
              onClick={() =>
                canAct && !hasActed && onApproveSalary!(row.original.id)
              }
              disabled={isApproving || !canAct || hasActed}
              dir={locale === LANGUAGES.ARABIC ? "rtl" : "ltr"}
              className="cursor-pointer font-medium text-green-600 justify-start disabled:opacity-50"
              title={
                hasActed && userAction === "approve"
                  ? "Already Approved"
                  : undefined
              }
            >
              {t("people.employees-page.salary.table.actions.approve")}
              {hasActed && userAction === "approve" ? "✓ " : ""}
            </DropdownMenuItem>
          )}

          {/* Pay option for approved status */}
          {hasPayAction && (
            <DropdownMenuItem
              onClick={() => onPaySalary!(row.original.id)}
              disabled={isPaying}
              dir={locale === LANGUAGES.ARABIC ? "rtl" : "ltr"}
              className="cursor-pointer font-medium text-purple-600 justify-start"
            >
              <CreditCard className="h-4 w-4 ms-2" />
              {t("people.employees-page.salary.table.actions.pay") || "Pay"}
            </DropdownMenuItem>
          )}

          {/* Reject option for submitted status */}
          {hasRejectAction && (
            <DropdownMenuItem
              onClick={() =>
                canAct && !hasActed && onRejectSalary!(row.original.id)
              }
              disabled={isRejecting || !canAct || hasActed}
              dir={locale === LANGUAGES.ARABIC ? "rtl" : "ltr"}
              className="cursor-pointer font-medium text-red-600 justify-start disabled:opacity-50"
              title={
                hasActed && userAction === "reject"
                  ? "Already Rejected"
                  : undefined
              }
            >
              {t("people.employees-page.salary.table.actions.reject")}
              {hasActed && userAction === "reject" ? "✓ " : ""}
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* File Preview Modal */}
      {previewData && (
        <FilePreview
          fileName={previewData.fileName}
          fileUrl={previewData.fileUrl}
          fileType={previewData.fileType}
          isOpen={isPreviewOpen}
          onClose={() => {
            setIsPreviewOpen(false);
            setPreviewData(null);
          }}
        />
      )}

      {/* Approval Workflow Modal */}
      {showWorkflowModal && (
        <ApprovalWorkflowModal
          isOpen={showWorkflowModal}
          onClose={() => setShowWorkflowModal(false)}
          data={row.original}
          included={includedData || []}
          type="salary"
        />
      )}
    </>
  );
};
