import React from "react";
import CasesTable from "../../_modules/cm/_components/cases/table";
import CasesHeader from "../../_modules/cm/_components/cases/casess-header";

type CasesPageProps = {
  searchParams: Promise<{
    page?: string;
    limit?: string;
    search?: string;
    sort?: string;
    [key: string]: string | undefined;
  }>;
};

const CasesPage = async ({ searchParams }: CasesPageProps) => {
  const params = await searchParams;

  return (
    <>
      <CasesHeader
        description={"هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة"}
      />
      <CasesTable searchParams={params} showPagination={true} />
    </>
  );
};

export default CasesPage;
