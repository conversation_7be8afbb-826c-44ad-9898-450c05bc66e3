import React from "react";
import { StatisticsCardWrapper } from "../_modules/procure/_component/common/statistics-card-wrapper";
import StepperWrapper from "../_modules/procure/_component/common/StepperWrapper";
import { listSampleItems, tempApprovalItems } from "./tempDate";
import { ApprovalDirection } from "@/enums/procure";
import { ItemsList } from "../_modules/procure/_component/common/ItemsList";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import ActionButton from "./_components/action-button";
import { getTranslations } from "next-intl/server";
import LatestNoteTable from "../_modules/procure/_component/common/latest-notes-table";

type ProcureSystemProps = {
  searchParams: Promise<{ page: string; limit: string }>;
};

const ProcureSystem = async ({ searchParams }: ProcureSystemProps) => {
  const params = await searchParams;
  const t = await getTranslations();

  return (
    <div>
      <StatisticsCardWrapper />
      <div className="my-4 flex flex-col md:flex-row gap-4">
        <Card className="bg-white w-full md:w-1/2 border-gray-200 border border-solid rounded-3xl shadow-sm">
          <CardHeader className="flex-row items-center justify-between w-full relative p-4 ps-6 pe-3">
            <CardTitle className="text-base text-gray-700 font-medium">
              {t("procure.HomePage.approvals.title")}
            </CardTitle>
            <ActionButton
              action="viewAllRequests"
              className="text-black text-sm bg-white border border-dark-100 rounded-lg px-4 py-1 font-medium transition-colors duration-200 hover:bg-gray-200 hover:text-black/80 shadow-none"
            >
              {t("procure.HomePage.approvals.viewAll")}
            </ActionButton>
            <div className="w-full bg-gray-100 h-[1px] absolute bottom-0 left-0" />
          </CardHeader>
          <StepperWrapper
            cardTitle={t("procure.HomePage.approvals.itemTitle")}
            cardSubtitle={t("procure.HomePage.approvals.itemCategory")}
            approvalItems={tempApprovalItems}
            direction={ApprovalDirection.Vertical}
          />
        </Card>
        <Card className="bg-white w-full md:w-1/2 border-gray-200 border border-solid rounded-3xl shadow-sm">
          <CardHeader className="flex-row items-center justify-between w-full relative p-4 pr-6 pl-3">
            <CardTitle className="text-base text-gray-700 font-medium">
              {t("procure.HomePage.items.title")}
            </CardTitle>
            <ActionButton
              action="viewAllItems"
              className="text-black text-sm bg-white border border-dark-100 rounded-lg px-4 py-1 font-medium transition-colors duration-200 hover:bg-gray-200 hover:text-black/80 shadow-none"
            >
              {t("procure.HomePage.items.viewAll")}
            </ActionButton>
            <div className="w-full bg-gray-100 h-[1px] absolute bottom-0 left-0" />
          </CardHeader>
          <ItemsList
            items={listSampleItems}
            currencySuffix={t("procure.HomePage.items.currencySuffix")}
            orderSuffix={t("procure.HomePage.items.orderSuffix")}
          />
        </Card>
      </div>

      <Card className="bg-white w-full border-gray-200 border border-solid rounded-3xl shadow-sm">
        <CardHeader className="flex items-start justify-center w-full relative p-6 pr-6 pl-3 border-b-1">
          <CardTitle className="text-base text-start text-gray-700 font-medium">
            {t("procure.HomePage.table.header")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <LatestNoteTable showPagination={false} searchParams={params} />
        </CardContent>
      </Card>
    </div>
  );
};

export default ProcureSystem;
