import { TEmployeeData } from "./employee";
import { ApprovalRequestData } from "./approval-request";

//legacy type for backward compatibility
export type TEmployeesSalaries = {
  id: string;
  employeeName: string;
  month: string;
  totalHours: number;
  totalSalary: number;
  status: string;
  paymentDate: string | null;
  numberOfLeaves: number;
  overtimeHours: number;
  hourlyRate: number;
  totalDiscount: number;
  tax: number;
  notes?: string;
};

//legacy type for backward compatibility
export type TEmployeesSalariesResponse = {
  salariesData: TEmployeesSalaries[];
  totalCount: number;
};

export type SalaryCalculationApiResponse = {
  data: SalaryCalculation[];
  included: (TEmployeeData | ApprovalRequestData | SalaryCalculationDetail)[];
  meta: {
    pagination: {
      count: number;
      page: number;
      limit: number;
      from: number;
      to: number;
    };
  };
};

// Salary calculation detail type for the detailed breakdown
export type SalaryCalculationDetail = {
  id: string;
  type: string;
  attributes: {
    id: number;
    detail_type: "deduction" | "base" | "allowance" | string;
    category: string;
    description: string;
    amount: number;
    reference_info: any;
  };
};

export type SalaryCalculation = {
  id: string;
  type: string;
  employee: TEmployeeData;
  attributes: {
    period: string;
    period_start_date: string;
    period_end_date: string;
    gross_salary: number;
    total_hours?: number;
    deductions: {
      income_tax: number;
      salary_advances: number;
      other_deductions: number;
      medical_insurance: number;
      employee_social_security: number;
      leave_deductions: number;
    };
    net_salary: number;
    status: string;
    calculation_date: string;
    payment_date: string | null;
    notes: string | null;
    created_at: string;
    updated_at: string;
    total_deductions: number;
    has_salary_slip: boolean;
  };
  relationships: {
    employee: {
      data: {
        id: string;
        type: string;
      };
    };
    salary_package: {
      data: {
        id: string;
        type: string;
      };
    };
    calculation_details?: {
      data: Array<{
        id: string;
        type: string;
      }>;
    };
    approval_request?: {
      data: {
        id: string;
        type: string;
      } | null;
    };
  };
};
