"use client";

import React, { useEffect, useMemo, useRef } from "react";
import { useForm } from "react-hook-form";
import { useTranslations } from "next-intl";
import { Loader2 } from "lucide-react";

import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import FormFieldRenderer from "@/components/auth/fields/form-field-renderer";

import { useFormFields } from "@/app/[locale]/_modules/cm/hooks/useFormFields";
import { FormField, FormSubmission } from "@/types/cm";
import { TinputField } from "@/types";

import { convertNewAPIFormFieldToInputField } from "@/utils/form-field-helpers";

type DynamicFormProps = {
  formSectionId: string | number;
  caseId?: string | number;
  submission?: FormSubmission[];
  submissionsLoading: boolean;
  onSubmit: (data: Record<string, unknown>) => Promise<boolean>;
  onSaveDraft?: (data: Record<string, unknown>) => Promise<void>;
  onNext?: () => void;
  onPrevious?: () => void;
  isSubmitting?: boolean;
  isDraftSaving?: boolean;
  mode?: "create" | "edit";
  canGoNext?: boolean;
  canGoPrevious?: boolean;
  isFirstSection?: boolean;
  isLastSection?: boolean;
  currentSectionIndex?: number;
  totalSections?: number;
};

// Convert backend field format to frontend input field
const convertFormFieldToInputField = (
  field: FormField,
): TinputField<Record<string, unknown>> | null =>
  convertNewAPIFormFieldToInputField(field);

export const DynamicForm = ({
  formSectionId,
  caseId,
  submission,
  submissionsLoading,
  onSubmit,
  onNext,
  onPrevious,
  isSubmitting = false,
  isDraftSaving = false,
  mode = "create",
  canGoNext = false,
  canGoPrevious = false,
  isFirstSection = false,
  isLastSection = false,
  currentSectionIndex = 0,
  totalSections = 1,
}: DynamicFormProps) => {
  const t = useTranslations();

  const {
    fields = [],
    isLoading,
    error,
  } = useFormFields({
    formSectionId,
    ...(mode === "edit" ? { caseId } : {}),
  });

  const getDefaultValues = (): Record<string, unknown> => {
    const defaults: Record<string, unknown> = {};
    if (mode === "edit" && submission?.length) {
      const currentSubmission = submission.find(
        (sub) =>
          sub.attributes.form_section_id?.toString() ===
          formSectionId.toString(),
      );
      if (currentSubmission?.attributes?.form_data) {
        Object.assign(defaults, currentSubmission.attributes.form_data);
      }
    }

    return defaults;
  };

  const form = useForm({
    defaultValues: getDefaultValues(),
    mode: "onChange",
  });

  useEffect(() => {
    const values = getDefaultValues();
    form.reset(values);
  }, [formSectionId, submission]);

  // Error state
  if (error) {
    console.error("Form fields loading error:", error);
    return (
      <div className="p-4 text-red-600 bg-red-50 rounded-lg">
        <h3 className="font-medium mb-2">خطأ في تحميل حقول النموذج</h3>
        <p className="text-sm">
          فشل في تحميل حقول النموذج. يرجى المحاولة مرة أخرى.
        </p>
        <details className="mt-2 text-xs">
          <summary className="cursor-pointer">تفاصيل الخطأ</summary>
          <pre className="mt-1 whitespace-pre-wrap">{error.message}</pre>
        </details>
      </div>
    );
  }

  const handleSubmit = async (data: Record<string, any>) => {
    const success = await onSubmit(data);
    if (success && !isLastSection && onNext) {
      onNext();
    }
  };

  const handleNext = () => {
    form.handleSubmit(handleSubmit)();
  };

  const handlePrevious = () => {
    onPrevious?.();
  };

  const validFields = fields.filter(
    (field) => field?.field_name?.trim() !== "",
  );

  const inputFields = validFields
    .sort((a, b) => a.display_order - b.display_order)
    .map(convertFormFieldToInputField)
    .filter(
      (field): field is TinputField<Record<string, any>> => field !== null,
    );

  return (
    <Form {...form}>
      <form action={submitAction} onSubmit={form.handleSubmit(handleSubmit)}>
        <div className="flex flex-col gap-5">
          {inputFields.map((fieldConfig) => (
            <FormFieldRenderer
              key={fieldConfig.name?.toString()}
              fieldConfig={fieldConfig}
              form={form}
              isPending={isSubmitting || isDraftSaving}
            />
          ))}
        </div>

        <div className="flex items-center justify-center mb-6 text-sm text-gray-600">
          <span>
            {currentSectionIndex + 1} of {totalSections} sections
          </span>
        </div>

        <div className="flex justify-between sm:sticky -bottom-4 bg-[#FBFBFC] pt-9 mb-0">
          <Button
            type="button"
            variant="outline"
            onClick={handlePrevious}
            disabled={isFirstSection || isSubmitting || isDraftSaving}
            className="flex items-center gap-2 min-w-36 min-h-10"
          >
            {t("common.buttonText.previous")}
          </Button>

          {isLastSection ? (
            <Button
              type="submit"
              disabled={isSubmitting || isDraftSaving}
              className="flex items-center gap-2"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  إرسال...
                </>
              ) : (
                t("common.buttonText.submit")
              )}
            </Button>
          ) : (
            <Button
              type="button"
              onClick={handleNext}
              disabled={isSubmitting || isDraftSaving}
              className="flex items-center gap-2 min-w-36 min-h-10"
            >
              {t("common.buttonText.next")}
            </Button>
          )}
        </div>
      </form>
    </Form>
  );
};
