"use client";

import * as React from "react";
import { ChevronUp, ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Input } from "./input";
import { Button } from "./button";

export interface NumberInputProps
  extends Omit<
    React.InputHTMLAttributes<HTMLInputElement>,
    "type" | "onChange"
  > {
  value?: string | number;
  onChange?: (value: string) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  min?: number;
  max?: number;
  step?: number;
  showSpinners?: boolean;
  className?: string;
}

const NumberInput = React.forwardRef<HTMLInputElement, NumberInputProps>(
  (
    {
      className,
      value = "",
      onChange,
      onBlur,
      min,
      max,
      step = 1,
      showSpinners = true,
      disabled,
      ...props
    },
    ref,
  ) => {
    const handleIncrement = () => {
      if (disabled) return;

      const currentValue = parseFloat(String(value)) || 0;
      const newValue = currentValue + step;

      if (max !== undefined && newValue > max) return;

      onChange?.(String(newValue));
    };

    const handleDecrement = () => {
      if (disabled) return;

      const currentValue = parseFloat(String(value)) || 0;
      const newValue = currentValue - step;

      if (min !== undefined && newValue < min) return;

      onChange?.(String(newValue));
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value;

      // Allow empty string, numbers, and decimal points
      if (inputValue === "" || /^-?\d*\.?\d*$/.test(inputValue)) {
        onChange?.(inputValue);
      }
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === "ArrowUp") {
        e.preventDefault();
        handleIncrement();
      } else if (e.key === "ArrowDown") {
        e.preventDefault();
        handleDecrement();
      }
    };

    return (
      <div className="relative">
        <Input
          ref={ref}
          type="text"
          inputMode="numeric"
          value={value}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onBlur={onBlur}
          min={min}
          max={max}
          step={step}
          disabled={disabled}
          className={cn(showSpinners ? "pe-8" : "", className)}
          {...props}
        />

        {showSpinners && (
          <div className="absolute end-1 top-1/2 -translate-y-1/2 flex flex-col">
            <Button
              type="button"
              variant={"ghost"}
              onClick={handleIncrement}
              disabled={
                disabled ||
                (max !== undefined && parseFloat(String(value)) >= max)
              }
              className={cn(
                "h-3 w-6 flex py-2 items-center justify-center rounded-sm",
                "hover:bg-muted transition-colors",
                "disabled:opacity-50 disabled:cursor-not-allowed",
                "focus:outline-none focus:ring-1 focus:ring-ring",
              )}
              tabIndex={-1}
            >
              <ChevronUp className="h-2.5 w-2.5" />
            </Button>

            <Button
              type="button"
              variant={"ghost"}
              onClick={handleDecrement}
              disabled={
                disabled ||
                (min !== undefined && parseFloat(String(value)) <= min)
              }
              className={cn(
                "h-3 w-6 flex py-2 items-center justify-center rounded-sm",
                "hover:bg-muted transition-colors",
                "disabled:opacity-50 disabled:cursor-not-allowed",
                "focus:outline-none focus:ring-1 focus:ring-ring",
              )}
              tabIndex={-1}
            >
              <ChevronDown className="h-2.5 w-2.5" />
            </Button>
          </div>
        )}
      </div>
    );
  },
);

NumberInput.displayName = "NumberInput";

export { NumberInput };
