import React, { Suspense } from "react";
import EmployeesHeader from "../../_modules/people/_components/employees/header/employees-header";
import EmployeesTable from "../../_modules/people/_components/employees/table";
import { getTranslations } from "next-intl/server";

const Employees = async ({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) => {
  const params = await searchParams;
  const t = await getTranslations("people.employees-page");

  return (
    <>
      <EmployeesHeader title={t("index.title")} />
      <Suspense fallback={<div>{"loading... "}</div>}>
        <EmployeesTable showPagination={true} searchParams={params} />
      </Suspense>
    </>
  );
};

export default Employees;
