import { Textarea } from "@/components/ui/textarea";
import { Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { TFunction } from "@/types";

type DeviceCommandResultProps = {
  success?: string | null;
  error?: string | null;
  isPending: boolean;
};

export default function DeviceCommandResult({
  success,
  error,
  isPending,
}: DeviceCommandResultProps) {
  const t = useTranslations() as TFunction;

  // Don't render if there's nothing to show
  if (!success && !error && !isPending) {
    return null;
  }

  const resultValue = isPending
    ? t("people.devices-page.device-commands.form.result.executing")
    : success || error || "";

  return (
    <div className="mt-6 pt-6 border-t border-gray-200">
      <h4 className="text-base font-medium mb-4 text-start flex items-center gap-2">
        {t("people.devices-page.device-commands.form.result.title")}
        {isPending && <Loader2 className="h-4 w-4 animate-spin" />}
      </h4>

      <div className="relative">
        <Textarea
          className="min-h-[120px] text-start"
          value={resultValue}
          readOnly
          placeholder={t(
            "people.devices-page.device-commands.form.result.placeholder",
          )}
        />

        {isPending && (
          <div className="absolute inset-0 bg-gray-50/50 rounded-md flex items-center justify-center">
            <div className="flex items-center gap-2 text-gray-600">
              <Loader2 className="h-5 w-5 animate-spin" />
              <span className="text-sm">
                {t("people.devices-page.device-commands.form.result.executing")}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
