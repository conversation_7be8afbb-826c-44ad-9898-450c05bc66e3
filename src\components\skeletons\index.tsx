import { cn } from "@/lib/utils";
import type {
  CardSkeletonProps,
  CardsSkeletonProps,
  CalendarSkeletonProps,
  ChartSkeleton,
  SkeletonTableProps,
} from "@/types/skeletons";
import { LoaderCircle } from "lucide-react";

// Shimmer effect CSS class
const shimmer =
  "before:absolute before:inset-0 before:-translate-x-full before:animate-[shimmer_2s_infinite] before:bg-gradient-to-r before:from-transparent before:via-white/60 before:to-transparent";

// Individual Card Skeleton Component
export function CardSkeleton({
  hasIcon = true,
  titleClassName = "h-9",
  valueClassName = "h-6",
  detailClassName = "h-4",
  className = "",
  shimmer: shimmerEffect = "animate-pulse",
  maxHeight = "max-h-[106px]",
  padding = "py-6 px-3 xl:px-5",
  hasDetail = false,
}: CardSkeletonProps) {
  return (
    <div
      className={`relative overflow-hidden rounded-xl border border-neutral-100 bg-gray-100 ${padding} flex gap-3 xl:gap-5 items-center ${maxHeight} ${shimmerEffect} ${className}`}
    >
      {/* Icon placeholder (optional) */}
      {hasIcon && <div className="h-6 w-6 rounded-md bg-gray-200" />}
      <div className="flex flex-col flex-1 gap-2">
        {/* Title placeholder */}
        <div className={`ml-2 w-1/3 rounded bg-gray-200 ${titleClassName}`} />
        {/* Value placeholder */}
        <div className={`ml-2 w-1/2 rounded bg-gray-200 ${valueClassName}`} />
        {/* Detail placeholder (optional) */}
        {hasDetail && (
          <div
            className={`ml-2 w-1/2 rounded bg-gray-200 ${detailClassName}`}
          />
        )}
      </div>
    </div>
  );
}

// Multiple Cards Skeleton Component
export function CardsSkeleton({ count = 3, ...rest }: CardsSkeletonProps) {
  return (
    <>
      {Array.from({ length: count }).map((_, idx) => (
        <CardSkeleton key={idx} {...rest} />
      ))}
    </>
  );
}

// Chart Skeleton Component
export function ChartSkeleton({
  skeletonWrapperStyle,
  titleStyle,
  showLabel = true,
  showSelect = true,
}: ChartSkeleton) {
  return (
    <div
      className={`${shimmer} bg-gray-100 relative overflow-hidden w-full h-[407px] rounded-2xl ${skeletonWrapperStyle}`}
    >
      <div className="rounded-2xl h-full p-4">
        <div className="mt-0 h-full rounded-2xl bg-white p-2">
          <div className="flex justify-between items-center pt-2">
            <div
              className={`ml-2 h-4 w-20 rounded-md bg-gray-200 ${titleStyle}`}
            />
            {showSelect && (
              <div className="ml-2 h-8 w-28 rounded-md bg-gray-200" />
            )}
          </div>
          {showLabel && (
            <div className="flex justify-end items-center pb-2 gap-1 pt-3">
              <div className="h-4 w-4 rounded-full bg-gray-200" />
              <div className="ml-2 h-3 w-20 rounded-md bg-gray-200" />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Calendar Skeleton Component
export default function CalendarSkeleton({ className }: CalendarSkeletonProps) {
  return (
    <div
      className={cn(
        "animate-pulse rounded-3xl w-full min-h-[85vh] bg-gray-100",
        className,
      )}
    >
      {/* Header Skeleton */}
      <div className="h-[72px] rounded-t-3xl bg-gray-200 mb-4" />
      {/* Grid Skeleton for calendar cells */}
      <div className="grid grid-cols-7 gap-1">
        {Array.from({ length: 42 }).map((_, index) => (
          <div key={index} className="min-h-[105px] bg-gray-200 rounded" />
        ))}
      </div>
    </div>
  );
}

export const SkeletonTable = ({
  rowCount,
  rowHeight = 60,
  headerHeight = 60,
  paginationHeight = undefined,
  wrapperClass,
}: SkeletonTableProps) => {
  const rowSkeletons = Array.from({ length: rowCount });

  return (
    <div
      className={`animate-pulse rounded-lg overflow-hidden bg-white/50 relative ${wrapperClass}`}
      style={{
        minHeight:
          headerHeight + rowCount * rowHeight + (paginationHeight ?? 0),
      }}
    >
      {/* Skeleton for header */}
      <div
        className="bg-gray-100 h-[78px] rounded-t-lg box-border border-b border-gray-200"
        style={{ height: headerHeight }}
      ></div>
      {/* Skeleton for rows */}
      {rowSkeletons.map((_, index) => (
        <div
          key={index}
          className="bg-gray-50 border-b border-gray-100"
          style={{ height: rowHeight }}
        ></div>
      ))}

      {paginationHeight && (
        <div
          className="bg-gray-50 rounded-md border-t border-gray-100"
          style={{ height: paginationHeight }}
        ></div>
      )}

      {/* Centered loader */}
      <div className="absolute inset-0 flex items-center justify-center bg-white/30 backdrop-blur-[1px]">
        <LoaderCircle className="h-12 w-12 animate-spin text-primary" />
      </div>
    </div>
  );
};
