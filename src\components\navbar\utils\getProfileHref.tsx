import { PermissionEnum } from "@/enums/Permission";

type GetProfileHrefParams = {
  locale: string;
  employeeId?: number | string | null;
  hasPermission: (p: PermissionEnum) => boolean;
};

export function getProfileHref({
  locale,
  employeeId,
  hasPermission,
}: GetProfileHrefParams): string | undefined {
  if (hasPermission(PermissionEnum.READ_EMPLOYEE)) {
    return employeeId != null
      ? `/${locale}/people/employees/${employeeId}`
      : `/${locale}/people/employees`;
  }

  if (hasPermission(PermissionEnum.READ_OWN_EMPLOYEE)) {
    return `/${locale}/people`;
  }
}
