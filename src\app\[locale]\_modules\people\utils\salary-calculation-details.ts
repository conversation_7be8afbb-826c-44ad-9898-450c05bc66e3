import {
  SalaryCalculation,
  SalaryCalculationDetail,
} from "../type/employees-salaries";

/**
 * Get calculation details for a specific salary calculation
 */
export const getCalculationDetails = (
  salaryCalculation: SalaryCalculation,
  calculationDetailsData: SalaryCalculationDetail[],
): SalaryCalculationDetail[] => {
  const detailIds =
    salaryCalculation.relationships.calculation_details?.data?.map(
      (detail) => detail.id,
    ) || [];

  return calculationDetailsData.filter((detail) =>
    detailIds.includes(detail.id),
  );
};

/**
 * Group calculation details by type
 */
export const groupCalculationDetailsByType = (
  details: SalaryCalculationDetail[],
): Record<string, SalaryCalculationDetail[]> => {
  return details.reduce(
    (acc, detail) => {
      const type = detail.attributes.detail_type;
      if (!acc[type]) {
        acc[type] = [];
      }
      acc[type].push(detail);
      return acc;
    },
    {} as Record<string, SalaryCalculationDetail[]>,
  );
};
