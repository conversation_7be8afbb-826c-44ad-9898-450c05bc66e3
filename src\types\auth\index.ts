import { TSystems } from "..";

// user type
export type TUser = {
  id: string;
  name: string;
  email: string;
  profileImage?: string;
  avatar?: string;
  availableSubsystems?: TSystems[];
};

// Define a type for user system access
export type TUserSystem = {
  id: string;
  name: string;
  alias: string;
};

export type LoginResponse = {
  message: string;
  user: TUser;
  main_token: string;
};

export type SessionResponse = {
  message: string;
  scope: TSystems;
  session_token: string;
};
