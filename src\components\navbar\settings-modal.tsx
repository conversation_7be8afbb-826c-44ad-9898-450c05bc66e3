"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogClose,
} from "@/components/ui/dialog";
import { useLocale, useTranslations } from "next-intl";
import { Settings, X } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { LANGUAGES } from "@/constants/enum";
import {
  Bell,
  CalendarRemove,
  BriefCase,
  DollarSquare,
  CPUSettings,
} from "../../../public/images/icons";
import ChangePasswordForm from "@/app/[locale]/_components/settings/change-password";
import EditProfileForm from "@/app/[locale]/_components/settings/edit-profile";
import NotificationSound from "@/app/[locale]/_components/settings/notification-sound";
import Holidays from "@/app/[locale]/_components/settings/holidays";
import CompanySettings from "@/app/[locale]/_components/settings/company-settings";
import AttendanceSettings from "@/app/[locale]/_components/settings/attendance-settings";
import SalarySettings from "@/app/[locale]/_components/settings/salary-settings";
import { Locale } from "@/i18n/routing";
import { useAttendanceExemptions } from "@/app/[locale]/_modules/people/hooks/attendance/useAttendanceExemptions";
import { ScrollArea } from "../ui/scroll-area";
import { useCenteredDraggable } from "@/hooks/useDraggable";
import { cn } from "@/lib/utils";
import { useEffect } from "react";
import useMediaQuery from "@/hooks/use-media-query";
import { usePermission } from "@/contexts/PermissionContext";
import { PermissionEnum } from "@/enums/Permission";
import { useSettingsLoading } from "@/hooks/use-settings-loading";
import { Loader2 } from "lucide-react";

type SettingsModalProps = {
  open: boolean;
  activeTab:
    | "GENERAL"
    | "NOTIFICATIONS"
    | "HOLIDAYS"
    | "COMPANY"
    | "SALARY"
    | "ATTENDANCE";
  subForm: "EDIT_PROFILE" | "CHANGE_PASSWORD" | null;
  openSettings: (
    tab:
      | "GENERAL"
      | "NOTIFICATIONS"
      | "HOLIDAYS"
      | "COMPANY"
      | "SALARY"
      | "ATTENDANCE",
    subForm?: "EDIT_PROFILE" | "CHANGE_PASSWORD",
  ) => void;
  onClose: () => void;
};

export default function SettingsModal({
  open,
  activeTab,
  subForm,
  onClose,
  openSettings,
}: SettingsModalProps) {
  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const t = useTranslations();
  const { isLoading } = useAttendanceExemptions();
  const isSmallScreen = useMediaQuery("(max-width:768px)");
  const { hasPermission } = usePermission();
  const { companyLoading, attendanceLoading, salaryLoading } =
    useSettingsLoading();

  const { ref, resetPosition, handleMouseDown, isDragging } =
    useCenteredDraggable({
      enabled: !isSmallScreen,
      containWithinViewport: true,
    });

  useEffect(() => {
    if (!isSmallScreen) {
      resetPosition();
    }
  }, [open, isSmallScreen]);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent
        ref={ref}
        lang={locale}
        hideClose={true}
        className={cn(
          "w-full max-w-[836px] min-h-[596.6px] flex flex-col !rounded-2xl p-0 gap-0",
          "top-1/2 left-1/2",
        )}
        style={{
          cursor: isDragging ? "grabbing" : "default",
        }}
      >
        {/* Modal Header */}

        <DialogHeader
          onMouseDown={handleMouseDown}
          className="relative flex flex-row text-start items-center border-b h-[75px] cursor-grab active:cursor-grabbing select-none"
        >
          <DialogTitle className="text-start flex items-center max-w-[217px] w-full border-e h-full ps-6">
            {t("settings.title")}
          </DialogTitle>

          <DialogDescription className="text-start px-10">
            {subForm === "CHANGE_PASSWORD" && activeTab === "GENERAL" ? (
              <span>{t("common.settings.tabs.change-password")}</span>
            ) : subForm === "EDIT_PROFILE" && activeTab === "GENERAL" ? (
              <span className="sr-only">
                {t("common.navbar.userProfile.editProfile")}
              </span>
            ) : activeTab === "HOLIDAYS" ? (
              <span className="sr-only">{t("settings.holidays.title")}</span>
            ) : (
              <span className="sr-only">
                {t("settings.notifications.title")}
              </span>
            )}
          </DialogDescription>

          <DialogClose asChild>
            <Button
              variant="ghost"
              className="absolute top-3 end-0 text-icons-main hover:text-secondary transition-colors"
            >
              <X className="!h-6 !w-6" />
            </Button>
          </DialogClose>
        </DialogHeader>

        {/* Tabs */}
        <Tabs
          orientation="vertical"
          lang={locale}
          dir={isAr ? "rtl" : "ltr"}
          value={activeTab}
          onValueChange={(newValue) =>
            openSettings(
              newValue as
                | "GENERAL"
                | "NOTIFICATIONS"
                | "HOLIDAYS"
                | "COMPANY"
                | "SALARY"
                | "ATTENDANCE",
            )
          }
          className="bg-none flex flex-1 h-full"
        >
          <TabsList className="flex flex-col bg-transparent w-full h-full relative rounded-none max-w-[216px] justify-start p-0">
            <TabsTrigger
              className="w-full justify-start gap-3 active:bg-secondary-2 data-[state=active]:shadow-none data-[state=active]:bg-background-v2 data-[state=active]:border-e-2 data-[state=active]:border-secondary rounded-none h-11 group"
              value="GENERAL"
              onClick={() => openSettings("GENERAL", subForm || "EDIT_PROFILE")}
            >
              <Settings className="w-[18px] h-[18px] stroke-[1.5px] group-hover:text-black group-data-[state=active]:text-black" />
              <span className="font-medium text-sm group-hover:text-black">
                {t("settings.global.title")}
              </span>
            </TabsTrigger>
            <TabsTrigger
              className="w-full justify-start gap-3 active:bg-secondary-2 data-[state=active]:shadow-none data-[state=active]:bg-background-v2 data-[state=active]:border-e-2 data-[state=active]:border-secondary rounded-none h-11 group"
              value="NOTIFICATIONS"
              onClick={() => openSettings("NOTIFICATIONS")}
            >
              <Bell className="fill-none w-[18px] h-[18px] stroke-[1.5px] text-icons-main group-data-[state=active]:text-black group-hover:text-black" />
              <span className="font-medium text-sm group-hover:text-black">
                {t("settings.notifications.title")}
              </span>
            </TabsTrigger>
            {hasPermission(PermissionEnum.MANAGE_ATTENDANCE_EXEMPTION) && (
              <TabsTrigger
                disabled={isLoading}
                className="w-full justify-start gap-3 group active:bg-secondary-2 data-[state=active]:shadow-none data-[state=active]:bg-background-v2 data-[state=active]:border-e-2 data-[state=active]:border-secondary rounded-none h-11"
                value="HOLIDAYS"
                onClick={() => openSettings("HOLIDAYS")}
              >
                <CalendarRemove className="w-[18px] h-[18px] stroke-[1.5px] text-icons-main group-data-[state=active]:text-black group-hover:text-black" />
                <span className="font-medium text-sm group-hover:text-black">
                  {t("settings.holidays.title")}
                </span>
              </TabsTrigger>
            )}
            {hasPermission(PermissionEnum.READ_SETTING) && (
              <TabsTrigger
                className="w-full justify-start gap-3 group active:bg-secondary-2 data-[state=active]:shadow-none data-[state=active]:bg-background-v2 data-[state=active]:border-e-2 data-[state=active]:border-secondary rounded-none h-11 disabled:opacity-50 disabled:cursor-not-allowed"
                value="COMPANY"
                onClick={() => openSettings("COMPANY")}
                disabled={companyLoading}
              >
                <BriefCase className="w-[18px] h-[18px] stroke-[1.5px] text-icons-main group-data-[state=active]:text-black group-hover:text-black" />
                <span className="font-medium text-sm group-hover:text-black flex items-center gap-2">
                  {t("common.settings.tabs.company")}
                  {companyLoading && (
                    <Loader2 className="w-3 h-3 animate-spin" />
                  )}
                </span>
              </TabsTrigger>
            )}
            {hasPermission(PermissionEnum.READ_SETTING) && (
              <TabsTrigger
                className="w-full justify-start gap-3 group active:bg-secondary-2 data-[state=active]:shadow-none data-[state=active]:bg-background-v2 data-[state=active]:border-e-2 data-[state=active]:border-secondary rounded-none h-11 disabled:opacity-50 disabled:cursor-not-allowed"
                value="SALARY"
                onClick={() => openSettings("SALARY")}
                disabled={salaryLoading}
              >
                <DollarSquare className="w-[18px] h-[18px] stroke-[1.5px] text-icons-main group-data-[state=active]:text-black group-hover:text-black" />
                <span className="font-medium text-sm group-hover:text-black flex items-center gap-2">
                  {t("common.settings.tabs.salary")}
                  {salaryLoading && (
                    <Loader2 className="w-3 h-3 animate-spin" />
                  )}
                </span>
              </TabsTrigger>
            )}
            {hasPermission(PermissionEnum.READ_SETTING) && (
              <TabsTrigger
                className="w-full justify-start gap-3 group active:bg-secondary-2 data-[state=active]:shadow-none data-[state=active]:bg-background-v2 data-[state=active]:border-e-2 data-[state=active]:border-secondary rounded-none h-11 disabled:opacity-50 disabled:cursor-not-allowed"
                value="ATTENDANCE"
                onClick={() => openSettings("ATTENDANCE")}
                disabled={attendanceLoading}
              >
                <CPUSettings className="w-[18px] h-[18px] stroke-[1.5px] text-icons-main group-data-[state=active]:text-black group-hover:text-black" />
                <span className="font-medium text-sm group-hover:text-black flex items-center gap-2">
                  {t("common.settings.tabs.attendance")}
                  {attendanceLoading && (
                    <Loader2 className="w-3 h-3 animate-spin" />
                  )}
                </span>
              </TabsTrigger>
            )}
          </TabsList>

          <ScrollArea
            className="w-full [&_[data-radix-scroll-area-viewport]]:!px-0"
            verticleScrollStyle="rtl:left-0 ltr:right-0"
          >
            <div className="border-s border-border flex-1 !h-[95%] pb-6">
              <TabsContent
                value="GENERAL"
                className="pt-4 px-10 flex-1 relative h-full mt-0"
              >
                {subForm === "EDIT_PROFILE" && (
                  <EditProfileForm
                    backToGeneral={() =>
                      openSettings("GENERAL", "CHANGE_PASSWORD")
                    }
                  />
                )}
                {subForm === "CHANGE_PASSWORD" && (
                  <div className="relative h-full">
                    <ChangePasswordForm
                      backToGeneral={() =>
                        openSettings("GENERAL", "EDIT_PROFILE")
                      }
                    />
                  </div>
                )}
                {!subForm && (
                  <EditProfileForm
                    backToGeneral={() =>
                      openSettings("GENERAL", "CHANGE_PASSWORD")
                    }
                  />
                )}
              </TabsContent>

              <TabsContent
                value="NOTIFICATIONS"
                className="pt-6 w-full px-10 mt-0"
              >
                <NotificationSound />
              </TabsContent>

              {hasPermission(PermissionEnum.MANAGE_ATTENDANCE_EXEMPTION) && (
                <TabsContent
                  value="HOLIDAYS"
                  className="pt-6 w-full px-10 mt-0"
                >
                  <Holidays />
                </TabsContent>
              )}

              {hasPermission(PermissionEnum.READ_SETTING) && (
                <TabsContent value="COMPANY" className="pt-6 w-full px-10 mt-0">
                  <CompanySettings />
                </TabsContent>
              )}

              {hasPermission(PermissionEnum.READ_SETTING) && (
                <TabsContent value="SALARY" className="pt-6 w-full px-10 mt-0">
                  <SalarySettings />
                </TabsContent>
              )}

              {hasPermission(PermissionEnum.READ_SETTING) && (
                <TabsContent
                  value="ATTENDANCE"
                  className="pt-6 w-full px-10 mt-0"
                >
                  <AttendanceSettings />
                </TabsContent>
              )}
            </div>
          </ScrollArea>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
