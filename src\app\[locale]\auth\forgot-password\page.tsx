import CardWrapper from "@/components/auth/card-wrapper";
import React from "react";
import ForgotPassForm from "../../_components/auth/forgot-password-form";
import { getTranslations } from "next-intl/server";

const ForgotPassword = async ({
  searchParams,
}: {
  searchParams: Promise<{ status: string }>;
}) => {
  const params = (await searchParams).status;
  const isStatusSuccess = params === "success";
  const t = await getTranslations("auth");
  return (
    <CardWrapper
      headerTitle={
        isStatusSuccess ? t("checkEmail.title") : t("forgotPass.title")
      }
      headerLabel={
        isStatusSuccess
          ? t("checkEmail.description")
          : t("forgotPass.description")
      }
    >
      <ForgotPassForm />
    </CardWrapper>
  );
};

export default ForgotPassword;
