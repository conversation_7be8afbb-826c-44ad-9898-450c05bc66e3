export type CustomXAxisTickProps = {
  x: number;
  y: number;
  payload: any;
  fullPayload: any;
  isMobile?: boolean;
  isAr?: boolean;
  isLargeScreen?: boolean;
  isMediumScreen?: boolean;
  xAxisTickFormatter: (value: string) => string;
};

const CustomXAxisTick = ({
  x,
  y,
  payload,
  fullPayload,
  isMobile,
  isAr,
  isLargeScreen,
  isMediumScreen,
  xAxisTickFormatter,
}: CustomXAxisTickProps) => {
  const angle = !isLargeScreen ? 0 : -45;
  let label = xAxisTickFormatter(payload.value);
  const words = label.split(" ");
  const isHighestPercentage = fullPayload?.highlight;
  const fillColor = isHighestPercentage ? "hsl(var(--secondary))" : "#A0A0AB";
  return (
    <text
      x={x}
      y={y + 6} // vertical offset; adjust as needed.
      textAnchor="middle"
      fill={fillColor}
      fontSize={isMobile ? 10 : isAr ? 14 : isMediumScreen ? 12 : 14}
      fontWeight={400}
      transform={isMediumScreen ? `rotate(${angle}, ${x}, ${y}) ` : ""}
    >
      {words.map((word, index) => (
        <tspan key={index} x={x} dy={index === 0 ? "0" : "1em"}>
          {isAr
            ? word
            : fullPayload?.month && isLargeScreen
              ? word.slice(0, 3)
              : word}
        </tspan>
      ))}
    </text>
  );
};

export default CustomXAxisTick;
