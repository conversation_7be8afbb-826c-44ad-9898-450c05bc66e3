import { useToast } from "@/hooks/use-toast";

export const useToastMessage = () => {
  const { toast } = useToast();

  const showToast = (
    type: "success" | "error",
    messageKey: string,
    title?: string,
  ) => {
    toast({
      ...(title ? { title } : {}),
      className: type === "success" ? "success-toast" : "error-toast",
      description: messageKey,
      duration: 5000, // Set a reasonable duration (5 seconds)
    });
  };

  return { showToast };
};
