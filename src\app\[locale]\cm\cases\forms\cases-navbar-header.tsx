"use client";

import { usePathname, useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";
import ProfileNavHeader from "../../../_modules/people/_components/employees/profile/employee-profile-nav-header";
import { useCase } from "@/app/[locale]/_modules/cm/hooks/useCase";
import { useEffect, useState } from "react";

export default function CasesNavbarHeader() {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const t = useTranslations();
  const id = searchParams.get("case_id") || undefined;
  const [caseId, setCaseId] = useState<string | undefined>(undefined);

  const { case: caseData, error, isLoading } = useCase(caseId);
  console.log("🚀 ~ CasesNavbarHeader ~ caseData:", caseData);

  useEffect(() => {
    setCaseId(id as string);
  }, [pathname, id]);

  return (
    <div className="max-md:px-2">
      <ProfileNavHeader
        headerName={t("common.sidebar.links.cases")}
        name={
          error
            ? `Error: ${error.message || "Failed to load case"}`
            : caseData?.attributes?.beneficiary_name || ""
        }
        isLoading={isLoading}
      />
    </div>
  );
}
