import { But<PERSON> } from "@/components/ui/button";
import { Row } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { X } from "lucide-react";
import { LeaveDetail, TIncludedEmployee } from "../../../type/employee-leaves";
import { TEmployee } from "../../../type/employee";
import { findEmployeeByIdFromIncluded } from "../../../utils/find-employee";
import { mapRequestStatusToCanonical } from "@/constants/translations-mapping";
import { REQUEST_STATUS } from "@/constants/enum";
import RequestStatus from "@/components/status/request-status";
import { ApprovalRequestData } from "../../../type/approval-request";
import { getIncludedItem } from "../../../utils/included-data/getIncludedItem";
import { useApprovalPermissions } from "../../../hooks/useApprovalPermissions";

type LeavesRequestsActionsProps<TData> = {
  row: Row<TData>;
  onShowDetails: (value: TD<PERSON>, employee: TEmployee) => void;
  onAccept: (value: TData, employee: TEmployee) => void;
  onReject: (value: TData, employee: TEmployee) => void;
  includedData: (TIncludedEmployee | ApprovalRequestData)[];
};

const LeavesRequestsActions = <TData extends LeaveDetail>({
  row,
  onAccept,
  onReject,
  includedData,
}: LeavesRequestsActionsProps<TData>) => {
  const t = useTranslations();
  const employeeId = row.original.relationships.employee.data.id;
  const employee = findEmployeeByIdFromIncluded(includedData, employeeId);
  const status = row.original.attributes.status as REQUEST_STATUS;
  const final = mapRequestStatusToCanonical(status);

  // Approval request permissions
  const approvalRequestId =
    row.original.relationships.approval_request?.data?.id;
  const approvalRequest = approvalRequestId
    ? (getIncludedItem(
        includedData ?? [],
        "approval_request",
        approvalRequestId,
      ) as ApprovalRequestData)
    : undefined;

  const { canAct, hasActed, userAction } =
    useApprovalPermissions(approvalRequest);

  if (!employee) return null;

  const isPending = status === "pending";

  return (
    <div className="flex items-center gap-2 min-w-[116px]">
      {/* Final status badge */}
      {!isPending && (
        <RequestStatus
          status={final}
          label={t(`common.status.request-status.${final}`)}
        />
      )}

      {/* Action buttons for pending requests */}
      {isPending && (
        <>
          <Button
            variant="outline"
            onClick={() => onAccept(row.original, employee)}
            className="h-9 min-w-[72px] p-0 text-black text-xs font-medium rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
            title={t("people.leaves-requests-component.actions.accept")}
            disabled={!canAct || hasActed}
          >
            {hasActed && userAction === "approve" ? "✓ " : ""}
            {t("people.leaves-requests-component.actions.accept")}
          </Button>
          <Button
            variant="outline"
            onClick={() => onReject(row.original, employee)}
            className="h-9 w-9 p-0 text-error hover:text-red-600 text-xs font-medium rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
            title={t("people.leaves-requests-component.actions.reject")}
            disabled={!canAct || hasActed}
          >
            <X className="!w-5 !h-5" />
          </Button>
        </>
      )}
    </div>
  );
};

export { LeavesRequestsActions };
