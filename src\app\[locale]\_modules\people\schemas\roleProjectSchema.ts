import { z } from "zod";
import { TFunction } from "@/types";

export const roleProjectSchema = (
  t: TFunction,
  getScopeByRoleId: (roleId: string) => string | undefined,
) =>
  z
    .object({
      roleId: z.string().min(1, {
        message: t(
          "people.employees-page.profile.roles-projects.form.role.required",
        ),
      }),
      projectId: z.string().optional(), // allow empty
    })
    .refine(
      (data) => {
        const scope = getScopeByRoleId(data.roleId);
        // if this is a project-based role, projectId must be non-empty
        if (scope === "project_based") {
          return !!data.projectId && data.projectId.trim().length > 0;
        }
        // for global_role we don’t care about projectId
        return true;
      },
      {
        path: ["projectId"],
        message: t(
          "people.employees-page.profile.roles-projects.form.project.required",
        ),
      },
    );

export type RoleProjectSchemaType = z.infer<
  ReturnType<typeof roleProjectSchema>
>;
