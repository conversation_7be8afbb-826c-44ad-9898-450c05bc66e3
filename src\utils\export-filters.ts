import { ColumnFiltersState, SortingState } from "@tanstack/react-table";
import { ReadonlyURLSearchParams } from "next/navigation";
import type { DataTableExportConfig } from "@/types/export";
import { convertSortingToApiFormat } from "./table-sorting";

export function getExportFilters(
  columnFilters: ColumnFiltersState,
  sorting: SortingState,
  searchParams: ReadonlyURLSearchParams,
  exportConfig?: DataTableExportConfig,
): Record<string, any> {
  const filters: Record<string, any> = {};

  // Add search filter from table state
  const searchFilter = columnFilters.find((filter) => filter.id === "search");
  if (searchFilter?.value) {
    filters.search = searchFilter.value;
  }

  // Add ALL URL search params (this captures all current filters)
  for (const [key, value] of searchParams.entries()) {
    if (value && value.trim() !== "") {
      filters[key] = value;
    }
  }

  // Add table column filters (status, department, etc.)
  columnFilters.forEach((filter) => {
    if (
      filter.value !== undefined &&
      filter.value !== null &&
      filter.value !== ""
    ) {
      filters[filter.id] = filter.value;
    }
  });

  // Add sorting from table state
  const sortString = convertSortingToApiFormat(sorting);
  if (sortString) {
    filters.sort = sortString;
  }

  // Add custom filters from export config (these can override defaults)
  if (exportConfig?.customFilters) {
    Object.assign(filters, exportConfig.customFilters);
  }

  return filters;
}
