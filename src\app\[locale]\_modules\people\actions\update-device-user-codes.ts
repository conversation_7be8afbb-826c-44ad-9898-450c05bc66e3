"use server";

import { ActionState } from "@/types";
import { peopleService } from "@/services/api/people";
import { revalidatePath } from "next/cache";

export type UpdateDeviceUserCodesData = {
  deviceId: string;
  mappingId?: string; // Optional - if not provided, will create new mapping
  systemCode: string;
  deviceCode: string;
  isNewMapping?: boolean; // Flag to indicate if this is a new mapping
};

export async function updateDeviceUserCodes(
  data: UpdateDeviceUserCodesData,
): Promise<ActionState<null>> {
  try {
    if (data.isNewMapping || !data.mappingId) {
      // Create new mapping for unmapped users
      await peopleService.createDeviceUserMapping(
        data.deviceId,
        data.systemCode,
        data.deviceCode,
      );
    } else {
      // Update existing mapping for mapped users
      await peopleService.updateDeviceUserCodes(
        data.deviceId,
        data.mappingId,
        data.systemCode,
        data.deviceCode,
      );
    }

    // Revalidate the device users page to refresh the data
    revalidatePath(`/people/devices/${data.deviceId}`);

    return {
      success:
        data.isNewMapping || !data.mappingId
          ? "Device user mapping created successfully"
          : "Device user codes updated successfully",
      error: "",
      issues: [],
    };
  } catch (error) {
    console.error("Error updating device user codes:", error);

    return {
      success: "",
      error:
        error instanceof Error
          ? error.message
          : data.isNewMapping || !data.mappingId
            ? "Failed to create device user mapping"
            : "Failed to update device user codes",
      issues: [],
    };
  }
}
