import { NextRequest, NextResponse } from "next/server";
import { peopleService } from "@/services/api/people";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Get query parameters
    const namespaceFilter = searchParams.get("filter[namespace_eq]");
    const sort = searchParams.get("sort") || "namespace,key";

    // Build query parameters for the backend API
    const queryParams = new URLSearchParams();
    queryParams.append("sort", sort);

    if (namespaceFilter) {
      queryParams.append("filter[namespace_eq]", namespaceFilter);
    }

    // Call the backend API with query parameters
    const response = await peopleService.getSettings(queryParams.toString());

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching settings:", error);
    return NextResponse.json(
      { error: "Failed to fetch settings" },
      { status: 500 },
    );
  }
}
