import React from "react";
import { Edit2, <PERSON>, Trash } from "../../../../../../public/images/icons";
import { TEmployeeAttachment } from "../type/employee";
import { TFunction } from "@/types";

export const getAttachmentMenuItems = (
  t: TFunction,
  attachment: TEmployeeAttachment,
  handlers: {
    onView: () => void;
    onRename: (attachment: TEmployeeAttachment) => void;
    onDelete: (attachment: TEmployeeAttachment) => void;
  },
) => [
  {
    icon: (
      <Eye className="!w-6 !h-6 stroke-[#6B7280] group-hover:stroke-secondary" />
    ),
    label: t("people.employees-page.profile.attachments.actions.view"),
    onClick: handlers.onView,
    className:
      "flex rtl:flex-row-reverse items-center min-h-10 group rounded-2xl",
  },
  {
    icon: (
      <Edit2 className="!w-6 !h-6 stroke-[#6B7280] group-hover:stroke-secondary" />
    ),
    label: t("people.employees-page.profile.attachments.actions.rename"),
    onClick: () => handlers.onRename(attachment),
    className:
      "flex rtl:flex-row-reverse items-center min-h-10 group rounded-2xl",
  },
  {
    icon: (
      <Trash className="!w-6 !h-6 stroke-[#6B7280] group-hover:stroke-error" />
    ),
    label: t("common.buttonText.delete"),
    onClick: () => handlers.onDelete(attachment),
    className:
      "flex rtl:flex-row-reverse items-center min-h-10 group rounded-2xl text-error",
  },
];
