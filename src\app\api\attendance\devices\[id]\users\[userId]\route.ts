import { NextRequest, NextResponse } from "next/server";
import { peopleService } from "@/services/api/people";
import { createErrorResponse } from "@/utils/api-error-extractor";

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string; userId: string }> },
) {
  try {
    const { id, userId } = await params;

    if (!id || !userId) {
      return NextResponse.json(
        { error: "Device ID and User ID are required" },
        { status: 400 },
      );
    }

    const response = await peopleService.getDeviceUser(id, userId);
    return NextResponse.json(response);
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET Device User route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string; userId: string }> },
) {
  try {
    const { id, userId } = await params;

    if (!id || !userId) {
      return NextResponse.json(
        { error: "Device ID and User ID are required" },
        { status: 400 },
      );
    }

    await peopleService.deleteDeviceUser(id, userId);
    return NextResponse.json({ message: "User deleted successfully" });
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "DELETE Device User route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
