import { NextRequest, NextResponse } from "next/server";
import { peopleService } from "@/services/api/people";
import { createErrorResponse } from "@/utils/api-error-extractor";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(request.url);

    if (!id) {
      return NextResponse.json(
        { error: "Device ID is required" },
        { status: 400 },
      );
    }

    // Parse parameters - support both formats for backward compatibility
    const page = parseInt(
      searchParams.get("page") || searchParams.get("page[number]") || "1",
      10,
    );
    const limit = parseInt(
      searchParams.get("limit") || searchParams.get("page[size]") || "25",
      10,
    );

    // Build filter parameters
    const filterParams = new URLSearchParams();
    for (const [key, value] of searchParams.entries()) {
      if (key.startsWith("filter[") && !key.startsWith("group[")) {
        filterParams.append(key, value);
      }
    }

    const filters = filterParams.toString();

    const response = await peopleService.getDeviceUsers(
      id,
      page,
      limit,
      filters,
    );

    return NextResponse.json(response);
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET Device Users route",
    );

    return NextResponse.json({ error: errorMessage }, { status });
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: "Device ID is required" },
        { status: 400 },
      );
    }

    const formData = await request.formData();
    const response = await peopleService.createDeviceUser(id, formData);
    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "POST Device User route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
