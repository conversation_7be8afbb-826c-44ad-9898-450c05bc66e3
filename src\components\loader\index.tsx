"use client";

import { cn } from "@/lib/utils";
import React from "react";

export interface LoaderProps {
  size?: number;
  spinnerColor?: string;
  borderWidth?: number;
  overlayColor?: string;
  overlayOpacity?: number;
  overlayClassName?: string;
  spinnerClassName?: string;
}

export default function Loader({
  size = 64,
  spinnerColor = "hsl(var(--primary))", // default to primary color
  borderWidth = 4,
  overlayColor = "#ffffff",
  overlayOpacity = 0.75,
  overlayClassName = "",
  spinnerClassName = "",
}: LoaderProps) {
  // Inline style for the overlay (background color and opacity)
  const overlayStyle = {
    backgroundColor: overlayColor,
    opacity: overlayOpacity,
  };

  // Inline style for the spinner to control size and border properties
  const spinnerStyle: React.CSSProperties = {
    width: size,
    height: size,
    borderWidth: borderWidth,
    borderStyle: "solid",
    borderColor: spinnerColor,
    borderTopColor: "transparent",
  };

  return (
    <div
      className={cn(
        "fixed inset-0 z-50 flex items-center justify-center ",
        overlayClassName,
      )}
      style={overlayStyle}
    >
      <div
        className={cn(
          "rounded-full animate-spin border-[4px] border-primary border-t-transparent w-16 h-16",
          spinnerClassName,
        )}
        style={spinnerStyle}
      />
    </div>
  );
}
