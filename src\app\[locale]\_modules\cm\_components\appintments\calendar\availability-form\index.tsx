"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { AvailabilityFormData } from "../../../../types/appointment-calendar";
import { useFieldArray, useForm } from "react-hook-form";
import { startTransition, useActionState, useEffect, useRef } from "react";
import { useTranslations } from "next-intl";
import { ActionState, TFunction } from "@/types";
import { DayAvailability } from "./day-availability";
import { DAYS } from "@/constants";
import { submitAvailability } from "@/server/actions/availability-times";
import { useToast } from "@/hooks/use-toast";
import { Loader } from "lucide-react";

const AvailabilityForm: React.FC = () => {
  const formRef = useRef<HTMLFormElement | null>(null);
  const { toast } = useToast();

  const initialState: ActionState<AvailabilityFormData> = {
    error: null,
    success: null,
    data: null,
  };
  const [state, submitAction, pending] = useActionState(
    submitAvailability,
    initialState,
  );

  // Use the converted data as defaultValues.
  const defaultFormValues: AvailabilityFormData = {
    availability: DAYS.map((day) => ({
      day: day.value,
      isEnabled: false,
      // Doctor’s input time in local 24-hour format.
      intervals: [{ from: "09:00", to: "17:00" }],
    })),
  };

  const form = useForm<AvailabilityFormData>({
    defaultValues: defaultFormValues,
  });
  const { isDirty } = form.formState;
  const { control, register, handleSubmit, reset } = form;

  const t = useTranslations() as TFunction;
  const { fields: availabilityFields } = useFieldArray({
    control,
    name: "availability",
  });

  useEffect(() => {
    if (state.data) {
      reset(state.data);
    }
  }, [state.data, reset]);

  const onSubmit = async (data: AvailabilityFormData) => {
    if (!isDirty) {
      toast({
        className: "error-toast",
        title: t("common.Error.noChanges"),
      });
      return;
    }
    startTransition(async () => {
      await submitAction(data);
    });
  };

  useEffect(() => {
    // handle success and error messages
    if (state.success) {
      toast({
        className: "success-toast",
        title: "Updated data successfully",
      });
    } else if (state.issues?.length) {
      toast({
        className: "error-toast",
        title: state.issues[0] || "An error occurred",
      });
    } else if (state.error) {
      toast({
        className: "error-toast",
        title: `${state.error}`,
      });
    }
  }, [state]);

  return (
    <form
      ref={formRef}
      onSubmit={handleSubmit(onSubmit)}
      className="flex flex-col h-full pb-36 sm:pb-24"
    >
      {availabilityFields.map((dayField, dayIndex) => {
        const intervalArrayMethods = useFieldArray({
          control,
          name: `availability.${dayIndex}.intervals`,
        });
        return (
          <DayAvailability
            key={dayField.id}
            dayField={dayField}
            dayIndex={dayIndex}
            control={control}
            register={register}
            intervalArrayMethods={intervalArrayMethods}
          />
        );
      })}
      <div className="fixed bottom-0 left-0 w-[98%] px-6 py-4 sm:p-6 min-h-[85px] rounded-[18px] bg-white flex flex-col sm:flex-row items-center justify-between gap-4 sm:gap-6">
        <Button
          disabled={pending}
          type="submit"
          className="px-4 py-2 w-full h-12 sm:max-w-[244px] rounded-lg"
        >
          {pending ? (
            <Loader className="animate-spin" />
          ) : (
            t("common.buttonText.submit")
          )}
        </Button>
        <Button
          variant={"outline"}
          type="button"
          className="w-full h-12 sm:max-w-[244px] rounded-lg"
          // Reset to the default (local time) values.
          onClick={() => reset(defaultFormValues)}
        >
          {t("common.buttonText.cancel")}
        </Button>
      </div>
    </form>
  );
};

export default AvailabilityForm;
