import { cookies } from "next/headers";
import { BaseAPI } from "./index";
import {
  Case,
  CaseProgress,
  FormTemplatesResponse,
  FormSectionsResponse,
  FormFieldsResponse,
  FormSubmissionsResponse,
  FormSubmissionResponse,
  CreateFormSubmissionData,
  UpdateFormSubmissionData,
  TCasesResponse,
  LookupOptionsResponse,
  CalculateFieldRequest,
  CalculateFieldResponse,
} from "@/types/cm";
import { buildApiUrl } from "@/utils/api";

export class CMAPI extends BaseAPI {
  constructor() {
    // super("https://74e50a84fdbd.ngrok-free.app");
    super(buildApiUrl("cm"));
  }

  async getCases(
    page: number = 1,
    limit: number = 5,
    sort: string = "-id",
    search: string = "",
    filters: string = "",
  ): Promise<TCasesResponse> {
    const cmToken = await getCMSessionToken();

    // Build query parameters
    let url = `api/cases?sort=${sort}&page[number]=${page}&page[size]=${limit}`;

    if (search) {
      url += `&filter[search]=${encodeURIComponent(search)}`;
    }
    if (filters) {
      url += `&${filters}`;
    }

    return this.request<TCasesResponse>(url, {
      headers: {
        Authorization: `Bearer ${cmToken}`,
      },
      method: "GET",
    });
  }

  async getFormTemplates(params?: {
    sort?: string;
  }): Promise<FormTemplatesResponse> {
    const cmToken = await getCMSessionToken();

    if (!cmToken) {
      throw new Error("CM session token not found");
    }

    let url = "api/form_templates";

    if (params) {
      const queryParams = new URLSearchParams();
      if (params.sort) {
        queryParams.append("sort", params.sort);
      }
      url += `?${queryParams.toString()}`;
    }

    return this.request<FormTemplatesResponse>(url, {
      headers: {
        Authorization: `Bearer ${cmToken}`,
      },
      method: "GET",
    });
  }

  async getFormSections(params?: {
    sort?: string;
    form_template_id?: string | number;
  }): Promise<FormSectionsResponse> {
    const cmToken = await getCMSessionToken();

    if (!cmToken) {
      throw new Error("CM session token not found");
    }

    let url = "api/form_sections";

    if (params) {
      const queryParams = new URLSearchParams();

      if (params.form_template_id) {
        queryParams.append(
          "filter[form_template_id_eq]",
          params.form_template_id.toString(),
        );
      }
      url += `?${queryParams.toString()}`;
    }

    return this.request<FormSectionsResponse>(url, {
      headers: {
        Authorization: `Bearer ${cmToken}`,
      },
      method: "GET",
    });
  }

  async getFormFields(params?: {
    form_section_id?: string | number;
    case_id?: string | number;
  }): Promise<FormFieldsResponse> {
    const cmToken = await getCMSessionToken();

    if (!cmToken) {
      throw new Error("CM session token not found");
    }

    let url = "api/form_fields";
    console.log("🚀 ~ CMAPI ~ getFormFields ~ url:", url);

    if (params) {
      const queryParams = new URLSearchParams();
      if (params.form_section_id) {
        queryParams.append(
          "form_section_id",
          params.form_section_id.toString(),
        );
      }
      if (params.case_id) {
        queryParams.append("case_id", params.case_id.toString());
      }
      url += `?${queryParams.toString()}`;
    }

    return this.request<FormFieldsResponse>(url, {
      headers: {
        Authorization: `Bearer ${cmToken}`,
      },
      method: "GET",
    });
  }

  async getFormSubmissions(params?: {
    case_id?: string | number;
    form_template_id?: string | number;
    form_section_id?: string | number;
  }): Promise<FormSubmissionsResponse> {
    const cmToken = await getCMSessionToken();

    if (!cmToken) {
      throw new Error("CM session token not found");
    }

    let url = "api/form_submissions";

    if (params) {
      const queryParams = new URLSearchParams();
      if (params.case_id) {
        queryParams.append("filter[case_id_eq]", params.case_id.toString());
      }

      if (params.form_section_id) {
        queryParams.append(
          "filter[form_section_id_eq]",
          params.form_section_id.toString(),
        );
      }
      // Include case data
      queryParams.append("include", "case");
      url += `?${queryParams.toString()}`;
    }

    return this.request<FormSubmissionsResponse>(url, {
      headers: {
        Authorization: `Bearer ${cmToken}`,
      },
      method: "GET",
    });
  }

  async createFormSubmission(
    data: CreateFormSubmissionData,
  ): Promise<FormSubmissionResponse> {
    const cmToken = await getCMSessionToken();

    if (!cmToken) {
      throw new Error("CM session token not found");
    }

    // Convert data to FormData format as expected by the API
    const formData = new FormData();

    formData.append(
      "form_submission[form_section_id]",
      data.form_section_id.toString(),
    );

    if (data.case_id) {
      formData.append("form_submission[case_id]", data.case_id.toString());
    }
    if (data.assigned_user_id) {
      formData.append("assigned_user_id", data.assigned_user_id);
    }
    if (data.case_type) {
      formData.append("case_type", data.case_type);
    }
    if (data.priority_level) {
      formData.append("priority_level", data.priority_level.toString());
    }
    if (data.confidentiality_level) {
      formData.append(
        "confidentiality_level",
        data.confidentiality_level.toString(),
      );
    }

    // Add form_data fields with the new nested format: form_submission[form_data][field_name]
    Object.entries(data.form_data).forEach(([key, value]) => {
      let formattedValue = "";

      if (value instanceof Date) {
        // Format dates as YYYY-MM-DD format for date fields
        const year = value.getFullYear();
        const month = String(value.getMonth() + 1).padStart(2, "0");
        const day = String(value.getDate()).padStart(2, "0");
        formattedValue = `${year}-${month}-${day}`;
      } else if (typeof value === "boolean") {
        // Convert boolean to string (try "true"/"false" format first)
        formattedValue = value.toString();
      } else if (value !== null && value !== undefined) {
        formattedValue = value.toString();
      }

      console.log(`🔧 Form field ${key}:`, {
        original: value,
        formatted: formattedValue,
      });
      // New nested format: form_submission[form_data][field_name]
      formData.append(`form_submission[form_data][${key}]`, formattedValue);
    });

    console.log("🚀 ~ createFormSubmission ~ FormData entries:");
    for (const [key, value] of formData.entries()) {
      console.log(`  ${key}: ${value}`);
    }

    return this.requestWithFormData<FormSubmissionResponse>(
      "api/form_submissions",
      {
        headers: { Authorization: `Bearer ${cmToken}` },
        method: "POST",
        body: formData,
      },
    );
  }

  async updateFormSubmission(
    submissionId: string | number,
    data: UpdateFormSubmissionData,
  ): Promise<FormSubmissionResponse> {
    const cmToken = await getCMSessionToken();

    if (!cmToken) {
      throw new Error("CM session token not found");
    }

    // Convert data to FormData format for PATCH request
    const formData = new FormData();

    // Add form_data fields with the new nested format: form_submission[form_data][field_name]
    if (data.form_data) {
      Object.entries(data.form_data).forEach(([key, value]) => {
        formData.append(
          `form_submission[form_data][${key}]`,
          value?.toString() || "",
        );
      });
    }

    // Add other fields that can be updated
    if (data.status) {
      formData.append("status", data.status);
    }

    return this.request<FormSubmissionResponse>(
      `api/form_submissions/${submissionId}`,
      {
        headers: {
          Authorization: `Bearer ${cmToken}`,
          // Don't set Content-Type for FormData, let the browser set it
        },
        method: "PATCH",
        body: formData,
      },
    );
  }

  async getCase(caseId: string): Promise<{ data: Case }> {
    const cmToken = await getCMSessionToken();

    if (!cmToken) {
      throw new Error("CM session token not found");
    }
    return this.request<{ data: Case }>(
      `api/cases/${caseId}?include=assigned_user`,
      {
        headers: {
          Authorization: `Bearer ${cmToken}`,
        },
        method: "GET",
      },
    );
  }
}

// Create a singleton instance
export const cmAPI = new CMAPI();

export const getCMSessionToken = async (): Promise<string | null> => {
  const cookieStore = await cookies();
  return cookieStore.get("cm_session_token")?.value || null;
};
