import { LANGUAGES } from "@/constants/enum";

// Types for better type safety
type DateInput = Date | string | number | null | undefined;
type LocaleCode = string;
type FormatString = string;

// Cache for Intl.DateTimeFormat instances to improve performance
const formattersCache = new Map<string, Intl.DateTimeFormat>();

// Enhanced validation function
export const isValidDate = (dateInput: DateInput): boolean => {
  if (!dateInput) return false;

  const date = convertToDate(dateInput);
  return date instanceof Date && !isNaN(date.getTime());
};

// Helper function to convert various input types to Date
const convertToDate = (dateInput: DateInput): Date | null => {
  if (!dateInput) return null;

  if (dateInput instanceof Date) {
    return dateInput;
  }

  if (typeof dateInput === "number") {
    // Handle both seconds (10 digits) and milliseconds timestamps
    const timestamp =
      dateInput.toString().length === 10 ? dateInput * 1000 : dateInput;
    return new Date(timestamp);
  }

  if (typeof dateInput === "string") {
    // Handle empty strings
    if (dateInput.trim() === "") return null;

    // Try to parse the string as a date
    const parsedDate = new Date(dateInput);
    return isNaN(parsedDate.getTime()) ? null : parsedDate;
  }

  return null;
};

// Get cached formatter or create new one
const getFormatter = (
  locale: LocaleCode,
  options: Intl.DateTimeFormatOptions,
): Intl.DateTimeFormat => {
  const cacheKey = `${locale}-${JSON.stringify(options)}`;

  if (!formattersCache.has(cacheKey)) {
    formattersCache.set(cacheKey, new Intl.DateTimeFormat(locale, options));
  }

  return formattersCache.get(cacheKey)!;
};

// Enhanced formatDate function with better error handling
export const formatDate = (
  dateInput: DateInput,
  locale: LocaleCode,
  format: FormatString = "",
): string => {
  // Handle null/undefined input
  if (!dateInput) {
    return locale === LANGUAGES.ARABIC ? "غير متوفر" : "Not available";
  }

  // Convert input to Date object
  const date = convertToDate(dateInput);

  // Handle invalid dates
  if (!date || isNaN(date.getTime())) {
    return locale === LANGUAGES.ARABIC ? "تاريخ غير صالح" : "Invalid date";
  }

  // Parse format string or use default
  const formatOptions: Intl.DateTimeFormatOptions = format
    ? parseFormatString(format)
    : {
        year: "numeric",
        month: "long",
        day: "numeric",
        numberingSystem: "latn",
      };

  try {
    // Get cached formatter and format the date
    const formatter = getFormatter(locale, formatOptions);
    return formatter.format(date);
  } catch (error) {
    // Fallback in case of formatting errors
    console.warn("Date formatting error:", error);
    return locale === LANGUAGES.ARABIC ? "خطأ في التنسيق" : "Format error";
  }
};

// Enhanced format string parser with more comprehensive token support
function parseFormatString(format: string): Intl.DateTimeFormatOptions {
  if (!format || typeof format !== "string") {
    return {};
  }

  // Split on common separators while preserving the tokens
  const tokens = format.split(/[\s,/:-]+/).filter((token) => token.length > 0);
  const options: Intl.DateTimeFormatOptions = {};

  tokens.forEach((token) => {
    switch (token.toLowerCase()) {
      // Day tokens
      case "dd":
        options.day = "2-digit";
        break;
      case "d":
        options.day = "numeric";
        break;

      // Month tokens
      case "mmmm":
        options.month = "long";
        break;
      case "mmm":
        options.month = "short";
        break;
      case "mm":
        // Check if this is month or minute based on context
        if (!options.hour && !options.minute) {
          options.month = "2-digit";
        } else {
          options.minute = "2-digit";
        }
        break;
      case "m":
        // Check if this is month or minute based on context
        if (!options.hour && !options.minute) {
          options.month = "numeric";
        } else {
          options.minute = "numeric";
        }
        break;

      // Year tokens
      case "yyyy":
        options.year = "numeric";
        break;
      case "yy":
        options.year = "2-digit";
        break;

      // Weekday tokens
      case "eeee":
        options.weekday = "long";
        break;
      case "eee":
        options.weekday = "short";
        break;

      // Hour tokens
      case "hh":
        options.hour = "2-digit";
        options.hour12 = true;
        break;
      case "h":
        options.hour = "numeric";
        options.hour12 = true;
        break;
      case "HH":
        options.hour = "2-digit";
        options.hour12 = false;
        break;
      case "H":
        options.hour = "numeric";
        options.hour12 = false;
        break;

      // Minute tokens (handled above in mm/m cases)

      // Second tokens
      case "ss":
        options.second = "2-digit";
        break;
      case "s":
        options.second = "numeric";
        break;

      // Timezone tokens
      case "z":
      case "zzz":
        options.timeZoneName = "short";
        break;
      case "zzzz":
        options.timeZoneName = "long";
        break;

      // AM/PM tokens
      case "a":
      case "aa":
        options.hour12 = true;
        break;
    }
  });

  // Ensure we have at least some format options
  if (Object.keys(options).length === 0) {
    return {
      year: "numeric",
      month: "long",
      day: "numeric",
    };
  }

  return options;
}

// Utility function to clear the formatters cache (useful for testing or memory management)
export const clearFormattersCache = (): void => {
  formattersCache.clear();
};

// Utility function to get cache size (useful for debugging)
export const getFormattersCacheSize = (): number => {
  return formattersCache.size;
};
