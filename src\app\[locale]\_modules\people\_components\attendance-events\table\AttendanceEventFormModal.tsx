"use client";

import React from "react";
import { useTranslations } from "next-intl";
import ResponsiveDialog from "@/components/responsive-dialog";
import { DialogTitle, DialogDescription } from "@/components/ui/dialog";
import AttendanceEventForm from "./AttendanceEventForm";

type AttendanceEventFormModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  showEmployeeField?: boolean;
  employeeId?: string;
  mode?: "add" | "edit";
  attendanceEvent?: any;
};

export const AttendanceEventFormModal = ({
  isOpen,
  onClose,
  onSuccess,
  showEmployeeField = false,
  employeeId,
  mode = "add",
  attendanceEvent,
}: AttendanceEventFormModalProps) => {
  const t = useTranslations();

  const getTitle = () => {
    return mode === "add"
      ? t("people.attendance-events-page.create.title")
      : t("people.attendance-events-page.edit.title");
  };

  const getDescription = () => {
    return mode === "add"
      ? t("people.attendance-events-page.create.description")
      : t("people.attendance-events-page.edit.description");
  };

  const handleSuccess = () => {
    onClose();
    onSuccess?.();
  };

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={onClose}
      closeBtnStyle="top-[21px]"
      header={
        <>
          <div className="px-6 py-[22px] border-b">
            <DialogTitle className="font-semibold text-[18px] leading-[28px]">
              {getTitle()}
            </DialogTitle>
            <DialogDescription className="text-sm text-gray-600 mt-1 sr-only">
              {getDescription()}
            </DialogDescription>
          </div>
        </>
      }
    >
      <AttendanceEventForm
        key={`${mode}-${attendanceEvent?.id || 'new'}`}
        mode={mode}
        attendanceEvent={attendanceEvent}
        showEmployeeField={showEmployeeField}
        employeeId={employeeId}
        onSuccess={handleSuccess}
        onCancel={onClose}
      />
    </ResponsiveDialog>
  );
};
